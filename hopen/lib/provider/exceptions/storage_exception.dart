/// Base exception for all storage-related errors
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final bool isRetryable;
  final Duration? retryAfter;
  final Map<String, dynamic>? metadata;

  const AppException(
    this.message, {
    this.code,
    this.isRetryable = false,
    this.retryAfter,
    this.metadata,
  });

  @override
  String toString() =>
      '${runtimeType}: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Storage-related exceptions for file operations
class StorageException extends AppException {
  const StorageException(
    String message, {
    String? code,
    bool isRetryable = true,
    Duration? retryAfter,
    Map<String, dynamic>? metadata,
  }) : super(
          message,
          code: code,
          isRetryable: isRetryable,
          retryAfter: retryAfter,
          metadata: metadata,
        );

  /// File too large error
  factory StorageException.fileTooLarge({
    required int maxSizeBytes,
    required int actualSizeBytes,
  }) {
    final maxSizeMB = (maxSizeBytes / (1024 * 1024)).toStringAsFixed(1);
    final actualSizeMB = (actualSizeBytes / (1024 * 1024)).toStringAsFixed(1);

    return StorageException(
      'File too large. Maximum size is ${maxSizeMB}MB, but file is ${actualSizeMB}MB.',
      code: 'FILE_TOO_LARGE',
      isRetryable: false,
      metadata: {
        'max_size_bytes': maxSizeBytes,
        'actual_size_bytes': actualSizeBytes,
      },
    );
  }

  /// Invalid file format error
  factory StorageException.invalidFormat({
    required String actualFormat,
    required List<String> allowedFormats,
  }) {
    return StorageException(
      'Invalid file format "$actualFormat". Allowed formats: ${allowedFormats.join(', ')}.',
      code: 'INVALID_FORMAT',
      isRetryable: false,
      metadata: {
        'actual_format': actualFormat,
        'allowed_formats': allowedFormats,
      },
    );
  }

  /// Image dimensions error
  factory StorageException.invalidDimensions({
    required int width,
    required int height,
    required int minDimension,
    required int maxDimension,
  }) {
    return StorageException(
      'Invalid image dimensions ${width}x${height}. '
      'Dimensions must be between ${minDimension}x${minDimension} and ${maxDimension}x${maxDimension}.',
      code: 'INVALID_DIMENSIONS',
      isRetryable: false,
      metadata: {
        'width': width,
        'height': height,
        'min_dimension': minDimension,
        'max_dimension': maxDimension,
      },
    );
  }

  /// Upload failed error
  factory StorageException.uploadFailed({
    String? reason,
    bool isRetryable = true,
    Duration? retryAfter,
  }) {
    return StorageException(
      reason ?? 'Upload failed. Please try again.',
      code: 'UPLOAD_FAILED',
      isRetryable: isRetryable,
      retryAfter: retryAfter,
    );
  }

  /// Processing failed error
  factory StorageException.processingFailed({
    String? reason,
  }) {
    return StorageException(
      reason ?? 'Failed to process image. Please try a different image.',
      code: 'PROCESSING_FAILED',
      isRetryable: false,
    );
  }

  /// Storage quota exceeded error
  factory StorageException.quotaExceeded() {
    return const StorageException(
      'Storage quota exceeded. Please free up space or upgrade your plan.',
      code: 'QUOTA_EXCEEDED',
      isRetryable: false,
    );
  }

  /// Permission denied error
  factory StorageException.permissionDenied() {
    return const StorageException(
      'Permission denied. You do not have access to perform this operation.',
      code: 'PERMISSION_DENIED',
      isRetryable: false,
    );
  }

  /// File not found error
  factory StorageException.fileNotFound({String? fileName}) {
    return StorageException(
      fileName != null
          ? 'File "$fileName" not found.'
          : 'File not found.',
      code: 'FILE_NOT_FOUND',
      isRetryable: false,
    );
  }

  /// Corrupted file error
  factory StorageException.corruptedFile() {
    return const StorageException(
      'File appears to be corrupted. Please try uploading a different file.',
      code: 'CORRUPTED_FILE',
      isRetryable: false,
    );
  }
}

/// Network-related exceptions for connectivity issues
class NetworkException extends AppException {
  const NetworkException(
    String message, {
    String? code,
    bool isRetryable = true,
    Duration? retryAfter,
    Map<String, dynamic>? metadata,
  }) : super(
          message,
          code: code,
          isRetryable: isRetryable,
          retryAfter: retryAfter,
          metadata: metadata,
        );

  /// Connection timeout error
  factory NetworkException.timeout() {
    return const NetworkException(
      'Connection timeout. Please check your internet connection and try again.',
      code: 'TIMEOUT',
      isRetryable: true,
      retryAfter: Duration(seconds: 5),
    );
  }

  /// No internet connection error
  factory NetworkException.noConnection() {
    return const NetworkException(
      'No internet connection. Please check your network settings.',
      code: 'NO_CONNECTION',
      isRetryable: true,
      retryAfter: Duration(seconds: 10),
    );
  }

  /// Server error (5xx)
  factory NetworkException.serverError({int? statusCode}) {
    return NetworkException(
      'Server error${statusCode != null ? ' ($statusCode)' : ''}. Please try again later.',
      code: 'SERVER_ERROR',
      isRetryable: true,
      retryAfter: const Duration(seconds: 30),
      metadata: statusCode != null ? {'status_code': statusCode} : null,
    );
  }

  /// Client error (4xx)
  factory NetworkException.clientError({
    required int statusCode,
    String? reason,
  }) {
    return NetworkException(
      reason ?? 'Request failed with status $statusCode.',
      code: 'CLIENT_ERROR',
      isRetryable: statusCode == 429, // Only retry for rate limiting
      retryAfter: statusCode == 429 ? const Duration(seconds: 60) : null,
      metadata: {'status_code': statusCode},
    );
  }

  /// Authentication error
  factory NetworkException.unauthorized() {
    return const NetworkException(
      'Authentication failed. Please log in again.',
      code: 'UNAUTHORIZED',
      isRetryable: false,
    );
  }

  /// Rate limiting error
  factory NetworkException.rateLimited({Duration? retryAfter}) {
    return NetworkException(
      'Too many requests. Please wait before trying again.',
      code: 'RATE_LIMITED',
      isRetryable: true,
      retryAfter: retryAfter ?? const Duration(minutes: 1),
    );
  }
}
