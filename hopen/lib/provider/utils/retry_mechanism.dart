import 'dart:async';
import 'dart:io';
import 'dart:math';

import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../exceptions/network_exception.dart';

/// A utility class that provides retry functionality with exponential backoff.
class RetryMechanism {

  /// Creates a new [RetryMechanism] instance.
  ///
  /// [maxRetries] - The maximum number of retry attempts (default: 3).
  /// [initialDelayMs] - The initial delay in milliseconds before the first retry (default: 500).
  /// [maxDelayMs] - The maximum delay in milliseconds between retries (default: 5000).
  /// [backoffFactor] - The factor by which the delay increases with each retry (default: 1.5).
  RetryMechanism({
    this.maxRetries = 50,
    this.initialDelayMs = 500,
    this.maxDelayMs = 5000,
    this.backoffFactor = 1.5,
  });
  /// The maximum number of retry attempts.
  final int maxRetries;

  /// The initial delay in milliseconds before the first retry.
  final int initialDelayMs;

  /// The maximum delay in milliseconds between retries.
  final int maxDelayMs;

  /// The factor by which the delay increases with each retry.
  final double backoffFactor;

  /// Executes the given [operation] with retry logic.
  ///
  /// If the operation fails with a retryable exception, it will be retried
  /// up to [maxRetries] times with exponential backoff.
  ///
  /// Returns the result of the operation if successful.
  /// Throws the last exception if all retry attempts fail.
  Future<T> execute<T>(Future<T> Function() operation) async {
    var attempts = 0;
    Exception? lastException;

    while (attempts <= maxRetries) {
      try {
        if (attempts > 0) {
          LoggingService.error('Retry attempt $attempts of $maxRetries');
        }
        return await operation();
      } on SocketException catch (e) {
        lastException = NoConnectionException(originalError: e);
        LoggingService.error('Network connection error: ${e.message}');
      } on TimeoutException catch (e) {
        lastException = TimeoutException(originalError: e);
        LoggingService.error('Request timed out');
      } on NetworkException catch (e) {
        // Don't retry validation errors or unauthorized errors
        if (e is ValidationException || e is UnauthorizedException) {
          LoggingService.error('Non-retryable error: ${e.message}');
          rethrow;
        }

        // Don't retry server errors with 4xx status codes (except 408 Request Timeout)
        if (e is ServerException &&
            e.statusCode != null &&
            e.statusCode! >= 400 &&
            e.statusCode! < 500 &&
            e.statusCode != 408) {
          LoggingService.error('Non-retryable server error: ${e.message} (${e.statusCode})');
          rethrow;
        }

        lastException = e;
        LoggingService.error('Retryable network error: ${e.message}');
      } catch (e) {
        // For unknown exceptions, wrap in NetworkException for consistency
        lastException = NetworkException(
          message: 'Unknown error occurred: ${e.toString()}',
          originalError: e,
        );
        LoggingService.error('Unknown error: $e');
      }

      attempts++;

      if (attempts <= maxRetries) {
        // Calculate delay with exponential backoff and jitter
        final delay = _calculateBackoffDelay(attempts);
        LoggingService.error('Waiting $delay ms before retry');
        await Future.delayed(Duration(milliseconds: delay));
      }
    }

    // If we've exhausted all retries, throw the last exception
    LoggingService.error('All retry attempts failed');
    throw lastException!;
  }

  /// Calculates the delay for the next retry attempt with exponential backoff and jitter.
  ///
  /// [attempt] - The current retry attempt (1-based).
  ///
  /// Returns the delay in milliseconds.
  int _calculateBackoffDelay(int attempt) {
    // Calculate exponential backoff
    final exponentialDelay = initialDelayMs * pow(backoffFactor, attempt - 1).toInt();

    // Apply maximum delay cap
    final cappedDelay = min(exponentialDelay, maxDelayMs);

    // Add jitter (±20%) to prevent thundering herd problem
    final jitter = Random().nextDouble() * 0.4 - 0.2; // Random value between -0.2 and 0.2
    final delayWithJitter = (cappedDelay * (1 + jitter)).toInt();

    return max(delayWithJitter, 0); // Ensure delay is not negative
  }
}
