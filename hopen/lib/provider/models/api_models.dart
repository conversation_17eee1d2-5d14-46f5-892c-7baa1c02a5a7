import 'package:json_annotation/json_annotation.dart';

part 'api_models.g.dart';

// ============================================================================
// API Request/Response Models for Hopen Backend
// ============================================================================

// -----------------------------------------------------------------------------
// Authentication Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class LoginRequest {
  LoginRequest({required this.email, required this.password});

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);
  final String email;
  final String password;
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class SignupRequest {
  SignupRequest({
    required this.email,
    required this.password,
    required this.firstName,
    this.lastName,
    this.username,
    this.birthday,
    this.profilePictureUrl,
    this.notificationsEnabled = false,
  });

  factory SignupRequest.fromJson(Map<String, dynamic> json) =>
      _$SignupRequestFromJson(json);
  final String email;
  final String password;
  @JsonKey(name: 'first_name')
  final String firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  final String? username;
  final DateTime? birthday;
  @JsonKey(name: 'avatar_url')
  final String? profilePictureUrl;
  @JsonKey(name: 'notifications_enabled')
  final bool notificationsEnabled;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{
      'email': email,
      'password': password,
      'first_name': firstName,
      'username': username,
      'notifications_enabled': notificationsEnabled,
    };
    if (lastName != null) map['last_name'] = lastName;
    if (birthday != null) {
      map['date_of_birth'] = birthday!.toIso8601String().split('T').first;
    }
    if (profilePictureUrl != null) {
      map['avatar_url'] = profilePictureUrl;
    }
    return map;
  }
}

@JsonSerializable()
class AuthResponse {
  AuthResponse({
    this.token,
    this.accessToken,
    this.refreshToken,
    this.userId,
    this.expiresIn,
    this.user,
    this.message,
    this.success,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);
  final String? token;
  @JsonKey(name: 'access_token')
  final String? accessToken;
  @JsonKey(name: 'refresh_token')
  final String? refreshToken;
  @JsonKey(name: 'user_id')
  final String? userId;
  @JsonKey(name: 'expires_in')
  final int? expiresIn;
  final ApiUserProfile? user;
  final String? message;
  final bool? success;
  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

@JsonSerializable()
class RefreshTokenRequest {
  RefreshTokenRequest({required this.refreshToken});

  factory RefreshTokenRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenRequestFromJson(json);
  @JsonKey(name: 'refresh_token')
  final String refreshToken;
  Map<String, dynamic> toJson() => _$RefreshTokenRequestToJson(this);
}

// -----------------------------------------------------------------------------
// User Profile Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiUserProfile {
  ApiUserProfile({
    required this.id,
    required this.email,
    this.firstName,
    this.lastName,
    this.username,
    this.profilePictureUrl,
    this.onlineStatus,
    this.bubbleStatus,
    this.birthday,
    this.friendIds,
    this.contactIds,
    this.blockedUserIds,
    this.pendingSentContactRequestIds,
    this.pendingReceivedContactRequestIds,
    this.pendingSentBubbleRequestUserIds,
    this.pendingReceivedBubbleRequestUserIds,
    this.bubbleId,
    this.isOnline,
    this.lastSeen,
    this.createdAt,
    this.updatedAt,
  });

  factory ApiUserProfile.fromJson(Map<String, dynamic> json) =>
      _$ApiUserProfileFromJson(json);
  final String id;
  final String email;
  @JsonKey(name: 'first_name')
  final String? firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  final String? username;
  @JsonKey(name: 'avatar_url')
  final String? profilePictureUrl;
  @JsonKey(name: 'online_status')
  final String? onlineStatus;
  @JsonKey(name: 'bubble_status')
  final String? bubbleStatus;
  final DateTime? birthday;
  @JsonKey(name: 'friend_ids')
  final List<String>? friendIds;
  @JsonKey(name: 'contact_ids')
  final List<String>? contactIds;
  @JsonKey(name: 'blocked_user_ids')
  final List<String>? blockedUserIds;
  @JsonKey(name: 'pending_sent_contact_request_ids')
  final List<String>? pendingSentContactRequestIds;
  @JsonKey(name: 'pending_received_contact_request_ids')
  final List<String>? pendingReceivedContactRequestIds;
  @JsonKey(name: 'pending_sent_bubble_request_user_ids')
  final List<String>? pendingSentBubbleRequestUserIds;
  @JsonKey(name: 'pending_received_bubble_request_user_ids')
  final List<String>? pendingReceivedBubbleRequestUserIds;
  @JsonKey(name: 'bubble_id')
  final String? bubbleId;
  @JsonKey(name: 'is_online')
  final bool? isOnline;
  @JsonKey(name: 'last_seen')
  final DateTime? lastSeen;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  Map<String, dynamic> toJson() => _$ApiUserProfileToJson(this);
}

@JsonSerializable()
class UpdateUserProfileRequest {
  UpdateUserProfileRequest({
    this.firstName,
    this.lastName,
    this.username,
    this.profilePictureUrl,
  });

  factory UpdateUserProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateUserProfileRequestFromJson(json);
  @JsonKey(name: 'first_name')
  final String? firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  final String? username;
  @JsonKey(name: 'avatar_url')
  final String? profilePictureUrl;
  Map<String, dynamic> toJson() => _$UpdateUserProfileRequestToJson(this);
}

@JsonSerializable()
class UpdateOnboardingStatusRequest {
  UpdateOnboardingStatusRequest({required this.hasCompletedOnboarding});

  factory UpdateOnboardingStatusRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateOnboardingStatusRequestFromJson(json);
  @JsonKey(name: 'hasCompletedOnboarding')
  final bool hasCompletedOnboarding;
  Map<String, dynamic> toJson() => _$UpdateOnboardingStatusRequestToJson(this);
}

// -----------------------------------------------------------------------------
// Bubble Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiBubble {
  ApiBubble({
    required this.id,
    required this.name,
    this.maxMembers,
    this.currentMembers,
    this.currentMemberCount,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.expiresAt,
  });

  factory ApiBubble.fromJson(Map<String, dynamic> json) =>
      _$ApiBubbleFromJson(json);
  final String id;
  final String name;
  @JsonKey(name: 'max_members')
  final int? maxMembers;
  @JsonKey(name: 'current_members')
  final int? currentMembers;
  @JsonKey(name: 'current_member_count')
  final int? currentMemberCount;
  final String? status;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;
  Map<String, dynamic> toJson() => _$ApiBubbleToJson(this);
}

@JsonSerializable()
class CreateBubbleRequest {
  CreateBubbleRequest({
    required this.name,
    this.maxMembers,
    this.invitedUserIds,
  });

  factory CreateBubbleRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateBubbleRequestFromJson(json);
  final String name;
  @JsonKey(name: 'max_members')
  final int? maxMembers;
  @JsonKey(name: 'invited_user_ids')
  final List<String>? invitedUserIds;
  Map<String, dynamic> toJson() => _$CreateBubbleRequestToJson(this);
}

@JsonSerializable()
class JoinBubbleRequest {
  JoinBubbleRequest({required this.bubbleId});

  factory JoinBubbleRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinBubbleRequestFromJson(json);
  @JsonKey(name: 'bubble_id')
  final String bubbleId;
  Map<String, dynamic> toJson() => _$JoinBubbleRequestToJson(this);
}

// -----------------------------------------------------------------------------
// Contact & Friendship Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiContact {
  ApiContact({
    required this.id,
    this.firstName,
    this.lastName,
    this.username,
    this.email,
    this.profilePictureUrl,
    this.relationshipType,
    this.isOnline,
    this.lastSeen,
    this.userId,
    this.contactUserId,
    this.senderId,
    this.receiverId,
    this.senderName,
    this.receiverName,
    this.sentAt,
    this.respondedAt,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory ApiContact.fromJson(Map<String, dynamic> json) =>
      _$ApiContactFromJson(json);
  final String id;
  @JsonKey(name: 'first_name')
  final String? firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  final String? username;
  final String? email;
  @JsonKey(name: 'avatar_url')
  final String? profilePictureUrl;
  @JsonKey(name: 'relationship_type')
  final String? relationshipType;
  @JsonKey(name: 'is_online')
  final bool? isOnline;
  @JsonKey(name: 'last_seen')
  final DateTime? lastSeen;
  @JsonKey(name: 'user_id')
  final String? userId;
  @JsonKey(name: 'contact_user_id')
  final String? contactUserId;
  @JsonKey(name: 'senderId')
  final String? senderId;
  @JsonKey(name: 'receiverId')
  final String? receiverId;
  @JsonKey(name: 'senderName')
  final String? senderName;
  @JsonKey(name: 'receiverName')
  final String? receiverName;
  @JsonKey(name: 'sentAt')
  final DateTime? sentAt;
  @JsonKey(name: 'respondedAt')
  final DateTime? respondedAt;
  final String? status;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  Map<String, dynamic> toJson() => _$ApiContactToJson(this);
}

@JsonSerializable()
class SendContactRequestRequest {
  SendContactRequestRequest({required this.recipientId, this.message});

  factory SendContactRequestRequest.fromJson(Map<String, dynamic> json) =>
      _$SendContactRequestRequestFromJson(json);
  @JsonKey(name: 'recipient_id')
  final String recipientId;
  final String? message;
  Map<String, dynamic> toJson() => _$SendContactRequestRequestToJson(this);
}

@JsonSerializable()
class AcceptContactRequestRequest {
  AcceptContactRequestRequest({required this.contactRequestId});

  factory AcceptContactRequestRequest.fromJson(Map<String, dynamic> json) =>
      _$AcceptContactRequestRequestFromJson(json);
  @JsonKey(name: 'contact_request_id')
  final String contactRequestId;
  Map<String, dynamic> toJson() => _$AcceptContactRequestRequestToJson(this);
}

// -----------------------------------------------------------------------------
// Chat Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiChatMessage {
  ApiChatMessage({
    required this.id,
    required this.senderId,
    required this.bubbleId,
    required this.content,
    required this.messageType,
    required this.createdAt,
  });

  factory ApiChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ApiChatMessageFromJson(json);
  final String id;
  @JsonKey(name: 'sender_id')
  final String senderId;
  @JsonKey(name: 'bubble_id')
  final String bubbleId;
  final String content;
  @JsonKey(name: 'message_type')
  final String messageType;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  Map<String, dynamic> toJson() => _$ApiChatMessageToJson(this);
}

@JsonSerializable()
class SendMessageRequest {
  SendMessageRequest({
    required this.bubbleId,
    required this.content,
    this.messageType = 'text',
  });

  factory SendMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$SendMessageRequestFromJson(json);
  @JsonKey(name: 'bubble_id')
  final String bubbleId;
  final String content;
  @JsonKey(name: 'message_type')
  final String messageType;
  Map<String, dynamic> toJson() => _$SendMessageRequestToJson(this);
}

@JsonSerializable()
class GetChatMessagesRequest {
  GetChatMessagesRequest({
    required this.chatId,
    this.limit,
    this.beforeMessageId,
  });

  factory GetChatMessagesRequest.fromJson(Map<String, dynamic> json) =>
      _$GetChatMessagesRequestFromJson(json);
  @JsonKey(name: 'chat_id')
  final String chatId;
  final int? limit;
  @JsonKey(name: 'before_message_id')
  final String? beforeMessageId;
  Map<String, dynamic> toJson() => _$GetChatMessagesRequestToJson(this);
}

@JsonSerializable()
class GetChatMessagesResponse {
  GetChatMessagesResponse({
    required this.messages,
    required this.hasMore,
    this.nextCursor,
  });

  factory GetChatMessagesResponse.fromJson(Map<String, dynamic> json) =>
      _$GetChatMessagesResponseFromJson(json);
  final List<ApiChatMessage> messages;
  @JsonKey(name: 'has_more')
  final bool hasMore;
  @JsonKey(name: 'next_cursor')
  final String? nextCursor;
  Map<String, dynamic> toJson() => _$GetChatMessagesResponseToJson(this);
}

// -----------------------------------------------------------------------------
// Notification Model
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiNotification {
  ApiNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.isRead,
    required this.createdAt,
  });

  factory ApiNotification.fromJson(Map<String, dynamic> json) =>
      _$ApiNotificationFromJson(json);
  final String id;
  final String title;
  final String message;
  final String type;
  @JsonKey(name: 'is_read')
  final bool isRead;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  Map<String, dynamic> toJson() => _$ApiNotificationToJson(this);
}

// -----------------------------------------------------------------------------
// Call/WebRTC Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class CallInitiateResponse {
  CallInitiateResponse({required this.callId, this.offerSdp, this.iceServers});

  factory CallInitiateResponse.fromJson(Map<String, dynamic> json) =>
      _$CallInitiateResponseFromJson(json);
  @JsonKey(name: 'call_id')
  final String callId;
  @JsonKey(name: 'offer_sdp')
  final String? offerSdp;
  @JsonKey(name: 'ice_servers')
  final List<Map<String, dynamic>>? iceServers;
  Map<String, dynamic> toJson() => _$CallInitiateResponseToJson(this);
}

// -----------------------------------------------------------------------------
// Health Check Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class HealthCheckResponse {
  HealthCheckResponse({
    required this.status,
    this.version,
    this.timestamp,
    this.details,
  });

  factory HealthCheckResponse.fromJson(Map<String, dynamic> json) =>
      _$HealthCheckResponseFromJson(json);
  final String status;
  final String? version;
  final DateTime? timestamp;
  final Map<String, dynamic>? details;
  Map<String, dynamic> toJson() => _$HealthCheckResponseToJson(this);
}

// -----------------------------------------------------------------------------
// Common Response Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiResponseString {
  ApiResponseString({
    required this.success,
    this.data,
    this.message,
    this.code,
  });

  factory ApiResponseString.fromJson(Map<String, dynamic> json) =>
      _$ApiResponseStringFromJson(json);
  final bool success;
  final String? data;
  final String? message;
  final int? code;
  Map<String, dynamic> toJson() => _$ApiResponseStringToJson(this);
}

@JsonSerializable()
class ApiResponseMap {
  ApiResponseMap({required this.success, this.data, this.message, this.code});

  factory ApiResponseMap.fromJson(Map<String, dynamic> json) =>
      _$ApiResponseMapFromJson(json);
  final bool success;
  final Map<String, dynamic>? data;
  final String? message;
  final int? code;
  Map<String, dynamic> toJson() => _$ApiResponseMapToJson(this);
}

@JsonSerializable()
class ApiError {
  ApiError({required this.code, required this.message, this.details});

  factory ApiError.fromJson(Map<String, dynamic> json) =>
      _$ApiErrorFromJson(json);
  final String code;
  final String message;
  final Map<String, dynamic>? details;
  Map<String, dynamic> toJson() => _$ApiErrorToJson(this);
}

@JsonSerializable()
class PaginatedUserResponse {
  PaginatedUserResponse({
    required this.data,
    required this.page,
    required this.perPage,
    required this.total,
    required this.totalPages,
  });

  factory PaginatedUserResponse.fromJson(Map<String, dynamic> json) =>
      _$PaginatedUserResponseFromJson(json);
  final List<ApiUserProfile> data;
  final int page;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int total;
  @JsonKey(name: 'total_pages')
  final int totalPages;
  Map<String, dynamic> toJson() => _$PaginatedUserResponseToJson(this);
}

@JsonSerializable()
class PaginatedChatMessageResponse {
  PaginatedChatMessageResponse({
    required this.data,
    required this.page,
    required this.perPage,
    required this.total,
    required this.totalPages,
  });

  factory PaginatedChatMessageResponse.fromJson(Map<String, dynamic> json) =>
      _$PaginatedChatMessageResponseFromJson(json);
  final List<ApiChatMessage> data;
  final int page;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int total;
  @JsonKey(name: 'total_pages')
  final int totalPages;
  Map<String, dynamic> toJson() => _$PaginatedChatMessageResponseToJson(this);
}

@JsonSerializable()
class PaginatedNotificationResponse {
  PaginatedNotificationResponse({
    required this.data,
    required this.page,
    required this.perPage,
    required this.total,
    required this.totalPages,
  });

  factory PaginatedNotificationResponse.fromJson(Map<String, dynamic> json) =>
      _$PaginatedNotificationResponseFromJson(json);
  final List<ApiNotification> data;
  final int page;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int total;
  @JsonKey(name: 'total_pages')
  final int totalPages;
  Map<String, dynamic> toJson() => _$PaginatedNotificationResponseToJson(this);
}

// -----------------------------------------------------------------------------
// Blocked User Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiBlockedUser {
  ApiBlockedUser({
    required this.id,
    required this.userId,
    required this.blockedUserId,
    required this.blockedAt,
    this.blockedUserName,
    this.blockedUserUsername,
    this.reason,
  });

  factory ApiBlockedUser.fromJson(Map<String, dynamic> json) =>
      _$ApiBlockedUserFromJson(json);
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'blocked_user_id')
  final String blockedUserId;
  @JsonKey(name: 'blocked_user_name')
  final String? blockedUserName;
  @JsonKey(name: 'blocked_user_username')
  final String? blockedUserUsername;
  @JsonKey(name: 'blocked_at')
  final DateTime blockedAt;
  final String? reason;
  Map<String, dynamic> toJson() => _$ApiBlockedUserToJson(this);
}

// -----------------------------------------------------------------------------
// Support Ticket Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class ApiSupportTicket {
  ApiSupportTicket({
    required this.id,
    required this.userId,
    required this.subject,
    required this.description,
    required this.status,
    required this.priority,
    required this.category,
    required this.createdAt,
    this.updatedAt,
    this.resolvedAt,
    this.assignedTo,
  });

  factory ApiSupportTicket.fromJson(Map<String, dynamic> json) =>
      _$ApiSupportTicketFromJson(json);
  final String id;
  @JsonKey(name: 'user_id')
  final String userId;
  final String subject;
  final String description;
  final String status;
  final String priority;
  final String category;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  @JsonKey(name: 'resolved_at')
  final DateTime? resolvedAt;
  @JsonKey(name: 'assigned_to')
  final String? assignedTo;
  Map<String, dynamic> toJson() => _$ApiSupportTicketToJson(this);
}

// -----------------------------------------------------------------------------
// Enhanced Bubble Response Models
// -----------------------------------------------------------------------------

/// API model for bubble member response
@JsonSerializable()
class BubbleMemberResponse {
  const BubbleMemberResponse({
    required this.userId,
    required this.joinedAt,
    required this.status,
    required this.isOnline,
    required this.unreadMessageCount,
    this.leftAt,
    this.leaveReason,
  });

  factory BubbleMemberResponse.fromJson(Map<String, dynamic> json) =>
      _$BubbleMemberResponseFromJson(json);
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'joined_at')
  final DateTime joinedAt;
  final String status;
  @JsonKey(name: 'is_online')
  final bool isOnline;
  @JsonKey(name: 'left_at')
  final DateTime? leftAt;
  @JsonKey(name: 'leave_reason')
  final String? leaveReason;
  @JsonKey(name: 'unread_message_count')
  final int unreadMessageCount;
  Map<String, dynamic> toJson() => _$BubbleMemberResponseToJson(this);
}

/// API model for bubble response
@JsonSerializable()
class BubbleResponse {
  const BubbleResponse({
    required this.id,
    required this.name,
    required this.capacity,
    required this.members,
    required this.createdAt,
    required this.status,
    this.endDate,
  });

  factory BubbleResponse.fromJson(Map<String, dynamic> json) =>
      _$BubbleResponseFromJson(json);
  final String id;
  final String name;
  final int capacity;
  final List<BubbleMemberResponse> members;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'end_date')
  final DateTime? endDate;
  final String status;
  Map<String, dynamic> toJson() => _$BubbleResponseToJson(this);
}

/// Generic API Response wrapper
class ApiResponse<T> {
  const ApiResponse({
    required this.data,
    this.success = true,
    this.message,
    this.statusCode,
  });

  factory ApiResponse.success(T data, {String? message, int? statusCode}) =>
      ApiResponse(data: data, message: message, statusCode: statusCode);

  factory ApiResponse.error(String message, {int? statusCode}) => ApiResponse(
    data: null as T,
    success: false,
    message: message,
    statusCode: statusCode,
  );
  final T data;
  final bool success;
  final String? message;
  final int? statusCode;
}

// -----------------------------------------------------------------------------
// Media/File Models
// -----------------------------------------------------------------------------

@JsonSerializable()
class MediaFile {
  MediaFile({
    required this.id,
    @JsonKey(name: 'user_id') required this.userId,
    @JsonKey(name: 'file_name') required this.fileName,
    @JsonKey(name: 'original_name') required this.originalName,
    @JsonKey(name: 'content_type') required this.contentType,
    required this.size,
    @JsonKey(name: 'bucket_name') required this.bucketName,
    @JsonKey(name: 'object_key') required this.objectKey,
    required this.url,
    required this.category,
    @JsonKey(name: 'is_public') required this.isPublic,
    @JsonKey(name: 'created_at') this.createdAt,
    @JsonKey(name: 'updated_at') this.updatedAt,
  });

  factory MediaFile.fromJson(Map<String, dynamic> json) => _$MediaFileFromJson(json);
  final String id;
  final String userId;
  final String fileName;
  final String originalName;
  final String contentType;
  final int size;
  final String bucketName;
  final String objectKey;
  final String url;
  final String category;
  final bool isPublic;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Map<String, dynamic> toJson() => _$MediaFileToJson(this);
}

@JsonSerializable()
class UploadParams {
  UploadParams({required this.category, @JsonKey(name: 'is_public') this.isPublic = false});

  factory UploadParams.fromJson(Map<String, dynamic> json) => _$UploadParamsFromJson(json);
  final String category;
  @JsonKey(name: 'is_public')
  final bool isPublic;
  Map<String, dynamic> toJson() => _$UploadParamsToJson(this);
}

@JsonSerializable()
class UploadResponse {
  UploadResponse({required this.success, required this.file, required this.message});

  factory UploadResponse.fromJson(Map<String, dynamic> json) => _$UploadResponseFromJson(json);
  final bool success;
  final MediaFile file;
  final String message;
  Map<String, dynamic> toJson() => _$UploadResponseToJson(this);
}
