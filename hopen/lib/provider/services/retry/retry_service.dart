import 'dart:async';
import 'dart:math';

/// Service for handling retry logic with exponential backoff
class RetryService {
  /// Execute a function with retry logic and exponential backoff
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    Duration maxDelay = const Duration(seconds: 30),
    bool Function(dynamic error)? shouldRetry,
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt <= maxRetries) {
      try {
        print('🔄 RetryService: Attempt ${attempt + 1}/${maxRetries + 1}');
        final result = await operation();
        
        if (attempt > 0) {
          print('✅ RetryService: Operation succeeded after ${attempt + 1} attempts');
        }
        
        return result;
      } catch (error) {
        attempt++;
        
        // Check if we should retry this error
        if (shouldRetry != null && !shouldRetry(error)) {
          print('❌ RetryService: Error not retryable: $error');
          rethrow;
        }
        
        // If this was the last attempt, rethrow the error
        if (attempt > maxRetries) {
          print('❌ RetryService: Max retries exceeded. Final error: $error');
          rethrow;
        }
        
        print('⚠️ RetryService: Attempt ${attempt} failed: $error');
        print('⏳ RetryService: Waiting ${currentDelay.inMilliseconds}ms before retry...');
        
        // Wait before retrying
        await Future.delayed(currentDelay);
        
        // Calculate next delay with exponential backoff
        currentDelay = Duration(
          milliseconds: min(
            (currentDelay.inMilliseconds * backoffMultiplier).round(),
            maxDelay.inMilliseconds,
          ),
        );
      }
    }
    
    // This should never be reached, but just in case
    throw Exception('RetryService: Unexpected end of retry loop');
  }

  /// Execute with retry specifically for network operations
  static Future<T> executeNetworkOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
  }) async {
    return executeWithRetry(
      operation,
      maxRetries: maxRetries,
      initialDelay: const Duration(seconds: 1),
      backoffMultiplier: 2.0,
      maxDelay: const Duration(seconds: 10),
      shouldRetry: (error) {
        // Retry on network-related errors
        final errorString = error.toString().toLowerCase();
        return errorString.contains('socket') ||
               errorString.contains('network') ||
               errorString.contains('connection') ||
               errorString.contains('timeout') ||
               errorString.contains('unreachable');
      },
    );
  }

  /// Execute with retry specifically for storage operations
  static Future<T> executeStorageOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 2,
  }) async {
    return executeWithRetry(
      operation,
      maxRetries: maxRetries,
      initialDelay: const Duration(milliseconds: 500),
      backoffMultiplier: 1.5,
      maxDelay: const Duration(seconds: 5),
      shouldRetry: (error) {
        // Retry on storage-related errors but not validation errors
        final errorString = error.toString().toLowerCase();
        return (errorString.contains('storage') ||
                errorString.contains('upload') ||
                errorString.contains('server')) &&
               !errorString.contains('validation') &&
               !errorString.contains('format') &&
               !errorString.contains('size');
      },
    );
  }

  /// Execute with retry for API operations
  static Future<T> executeApiOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
  }) async {
    return executeWithRetry(
      operation,
      maxRetries: maxRetries,
      initialDelay: const Duration(milliseconds: 800),
      backoffMultiplier: 2.0,
      maxDelay: const Duration(seconds: 15),
      shouldRetry: (error) {
        // Retry on 5xx errors and network issues, but not 4xx client errors
        final errorString = error.toString().toLowerCase();
        
        // Don't retry client errors (4xx)
        if (errorString.contains('400') ||
            errorString.contains('401') ||
            errorString.contains('403') ||
            errorString.contains('404') ||
            errorString.contains('422')) {
          return false;
        }
        
        // Retry server errors (5xx) and network issues
        return errorString.contains('500') ||
               errorString.contains('502') ||
               errorString.contains('503') ||
               errorString.contains('504') ||
               errorString.contains('network') ||
               errorString.contains('connection') ||
               errorString.contains('timeout');
      },
    );
  }

  /// Calculate delay for a specific retry attempt
  static Duration calculateDelay(
    int attempt,
    Duration initialDelay,
    double backoffMultiplier,
    Duration maxDelay,
  ) {
    final delayMs = initialDelay.inMilliseconds * pow(backoffMultiplier, attempt);
    return Duration(
      milliseconds: min(delayMs.round(), maxDelay.inMilliseconds),
    );
  }

  /// Check if an error is retryable based on common patterns
  static bool isRetryableError(dynamic error) {
    if (error == null) return false;
    
    final errorString = error.toString().toLowerCase();
    
    // Network-related errors are usually retryable
    if (errorString.contains('socket') ||
        errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('unreachable')) {
      return true;
    }
    
    // Server errors (5xx) are retryable
    if (errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('504')) {
      return true;
    }
    
    // Client errors (4xx) are usually not retryable
    if (errorString.contains('400') ||
        errorString.contains('401') ||
        errorString.contains('403') ||
        errorString.contains('404') ||
        errorString.contains('422')) {
      return false;
    }
    
    // Validation errors are not retryable
    if (errorString.contains('validation') ||
        errorString.contains('format') ||
        errorString.contains('invalid')) {
      return false;
    }
    
    // Default to retryable for unknown errors
    return true;
  }
}

/// Result of a retry operation
class RetryResult<T> {
  const RetryResult({
    required this.success,
    this.data,
    this.error,
    this.attempts = 0,
    this.totalDuration,
  });

  final bool success;
  final T? data;
  final dynamic error;
  final int attempts;
  final Duration? totalDuration;

  factory RetryResult.success(T data, {int attempts = 1, Duration? duration}) {
    return RetryResult(
      success: true,
      data: data,
      attempts: attempts,
      totalDuration: duration,
    );
  }

  factory RetryResult.failure(dynamic error, {int attempts = 1, Duration? duration}) {
    return RetryResult(
      success: false,
      error: error,
      attempts: attempts,
      totalDuration: duration,
    );
  }
}
