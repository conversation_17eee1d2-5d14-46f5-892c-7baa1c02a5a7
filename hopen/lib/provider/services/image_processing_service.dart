import 'dart:io';
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

import 'avif_processing_service.dart';

import 'avif_processing_service.dart';

/// Service for handling image processing operations in isolates
/// This prevents UI blocking during intensive image operations
class ImageProcessingService {
  const ImageProcessingService._();

  /// Process an image file with AVIF as primary format
  static Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  }) async {
    try {
      // First, validate image dimensions and basic requirements
      final validationResult = await _validateImageRequirements(
        imagePath,
        minResolution,
        maxResolution,
      );

      if (!validationResult.isValid) {
        return ImageProcessingResult.error(validationResult.error!);
      }

      // Use FFmpeg-based AVIF processing (mobile-optimized)
      final avifResult = await EnhancedImageProcessor.processImage(
        inputPath: imagePath,
        maxFileSizeBytes: maxFileSizeBytes,
        initialQuality: compressionQuality.round(),
        maxWidth: maxResolution,
        maxHeight: maxResolution,
      );

      if (avifResult.isSuccess && avifResult.bestData != null) {
        return ImageProcessingResult.success(avifResult.bestData!);
      }

      // If AVIF processing fails, return error (no fallbacks needed for mobile)
      return ImageProcessingResult.error('AVIF processing failed: ${avifResult.errorMessage}');
    } catch (e) {
      return ImageProcessingResult.error('Failed to process image: $e');
    }
  }

  /// Fast image validation with minimal memory usage
  /// Note: Large images are allowed and will be automatically resized during processing
  static Future<_ValidationResult> _validateImageRequirements(
    String imagePath,
    int minResolution,
    int maxResolution,
  ) async {
    final stopwatch = Stopwatch()..start();
    print('⏱️ Starting fast image validation');

    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        return _ValidationResult.error('Image file not found');
      }

      // Fast validation: only read image header for dimensions (performance optimization)
      final fileSize = await file.length();
      print('📁 Image file size: ${(fileSize / 1024 / 1024).toStringAsFixed(2)}MB');

      // Read only first 64KB for header analysis (much faster than full file)
      final headerBytes = Uint8List.fromList(await file.openRead(0, 65536).first);
      final image = img.decodeImage(headerBytes);

      if (image == null) {
        // If header decode fails, try with more data but limit to 1MB max
        final limitedBytes = Uint8List.fromList(await file.openRead(0, 1024 * 1024).first);
        final fullImage = img.decodeImage(limitedBytes);

        if (fullImage == null) {
          return _ValidationResult.error('Invalid image format');
        }

        // Use dimensions from limited decode
        final width = fullImage.width;
        final height = fullImage.height;
        final minDimension = width < height ? width : height;

        print('📐 Image dimensions: ${width}x${height} (min: $minDimension)');

        // Only check minimum resolution - large images will be automatically resized
        if (minDimension < minResolution) {
          return _ValidationResult.error(
            'Image resolution too small. Minimum: ${minResolution}x${minResolution}px'
          );
        }
      } else {
        // Use dimensions from header decode
        final width = image.width;
        final height = image.height;
        final minDimension = width < height ? width : height;

        print('📐 Image dimensions: ${width}x${height} (min: $minDimension)');

        // Only check minimum resolution - large images will be automatically resized
        if (minDimension < minResolution) {
          return _ValidationResult.error(
            'Image resolution too small. Minimum: ${minResolution}x${minResolution}px'
          );
        }
      }

      stopwatch.stop();
      print('⏱️ Fast validation completed in ${stopwatch.elapsedMilliseconds}ms');
      return _ValidationResult.success();
    } catch (e) {
      stopwatch.stop();
      print('❌ Validation failed in ${stopwatch.elapsedMilliseconds}ms: $e');
      return _ValidationResult.error('Image validation failed: $e');
    }
  }

  /// Compress image data using AVIF processing
  static Future<Uint8List> compressImageInIsolate({
    required Uint8List imageBytes,
    required String extension,
    required int maxFileSizeBytes,
    required double initialQuality,
  }) async {
    try {
      // Create temporary file for AVIF processing
      final tempDir = Directory.systemTemp;
      final tempFile = File('${tempDir.path}/temp_${DateTime.now().millisecondsSinceEpoch}.$extension');
      await tempFile.writeAsBytes(imageBytes);

      // Use AVIF processing service
      final result = await EnhancedImageProcessor.processImage(
        inputPath: tempFile.path,
        maxFileSizeBytes: maxFileSizeBytes,
        initialQuality: (initialQuality * 100).round(),
      );

      // Clean up temp file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }

      if (result.isSuccess && result.bestData != null) {
        return result.bestData!;
      } else {
        throw Exception('AVIF compression failed: ${result.errorMessage}');
      }
    } catch (e) {
      throw Exception('Failed to compress image: $e');
    }
  }
}

/// Data class for image processing parameters
class ImageProcessingData {
  const ImageProcessingData({
    required this.imagePath,
    required this.minResolution,
    required this.maxResolution,
    required this.maxFileSizeBytes,
    required this.compressionQuality,
  });

  final String imagePath;
  final int minResolution;
  final int maxResolution;
  final int maxFileSizeBytes;
  final double compressionQuality;
}

/// Data class for image compression parameters
class ImageCompressionData {
  const ImageCompressionData({
    required this.imageBytes,
    required this.extension,
    required this.maxFileSizeBytes,
    required this.initialQuality,
  });

  final Uint8List imageBytes;
  final String extension;
  final int maxFileSizeBytes;
  final double initialQuality;
}

/// Result of image processing operation
class ImageProcessingResult {
  const ImageProcessingResult._({
    required this.isSuccess,
    this.processedBytes,
    this.errorMessage,
  });

  factory ImageProcessingResult.success(Uint8List processedBytes) {
    return ImageProcessingResult._(
      isSuccess: true,
      processedBytes: processedBytes,
    );
  }

  factory ImageProcessingResult.error(String errorMessage) {
    return ImageProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? processedBytes;
  final String? errorMessage;
}

/// Internal validation result
class _ValidationResult {
  const _ValidationResult._({
    required this.isValid,
    this.error,
  });

  factory _ValidationResult.success() {
    return const _ValidationResult._(isValid: true);
  }

  factory _ValidationResult.error(String error) {
    return _ValidationResult._(isValid: false, error: error);
  }

  final bool isValid;
  final String? error;
}

// Removed isolate processing methods - using FFmpeg AVIF processing directly

// Removed fallback compression methods - using AVIF processing directly

// Removed fallback compression methods - AVIF-only for mobile apps
