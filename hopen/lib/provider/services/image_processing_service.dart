import 'dart:io';
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

import 'avif_processing_service.dart';

import 'avif_processing_service.dart';

/// Service for handling image processing operations in isolates
/// This prevents UI blocking during intensive image operations
class ImageProcessingService {
  const ImageProcessingService._();

  /// Process an image file with AVIF as primary format
  static Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  }) async {
    try {
      // First, validate image dimensions and basic requirements
      final validationResult = await _validateImageRequirements(
        imagePath,
        minResolution,
        maxResolution,
      );

      if (!validationResult.isValid) {
        return ImageProcessingResult.error(validationResult.error!);
      }

      // Use FFmpeg-based AVIF processing (mobile-optimized)
      final avifResult = await EnhancedImageProcessor.processImage(
        inputPath: imagePath,
        maxFileSizeBytes: maxFileSizeBytes,
        initialQuality: compressionQuality.round(),
        maxWidth: maxResolution,
        maxHeight: maxResolution,
      );

      if (avifResult.isSuccess && avifResult.bestData != null) {
        return ImageProcessingResult.success(avifResult.bestData!);
      }

      // If AVIF processing fails, return error (no fallbacks needed for mobile)
      return ImageProcessingResult.error('AVIF processing failed: ${avifResult.errorMessage}');
    } catch (e) {
      return ImageProcessingResult.error('Failed to process image: $e');
    }
  }

  /// Validate image requirements before processing
  static Future<_ValidationResult> _validateImageRequirements(
    String imagePath,
    int minResolution,
    int maxResolution,
  ) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        return _ValidationResult.error('Image file not found');
      }

      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        return _ValidationResult.error('Invalid image format');
      }

      final width = image.width;
      final height = image.height;
      final minDimension = width < height ? width : height;
      final maxDimension = width > height ? width : height;

      if (minDimension < minResolution) {
        return _ValidationResult.error(
          'Image resolution too small. Minimum: ${minResolution}x${minResolution}px'
        );
      }

      if (maxDimension > maxResolution) {
        return _ValidationResult.error(
          'Image resolution too large. Maximum: ${maxResolution}x${maxResolution}px'
        );
      }

      return _ValidationResult.success();
    } catch (e) {
      return _ValidationResult.error('Image validation failed: $e');
    }
  }

  /// Compress image data in an isolate
  static Future<Uint8List> compressImageInIsolate({
    required Uint8List imageBytes,
    required String extension,
    required int maxFileSizeBytes,
    required double initialQuality,
  }) async {
    final data = ImageCompressionData(
      imageBytes: imageBytes,
      extension: extension,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
    );

    try {
      return await compute(_compressImageIsolate, data);
    } catch (e) {
      throw Exception('Failed to compress image: $e');
    }
  }
}

/// Data class for image processing parameters
class ImageProcessingData {
  const ImageProcessingData({
    required this.imagePath,
    required this.minResolution,
    required this.maxResolution,
    required this.maxFileSizeBytes,
    required this.compressionQuality,
  });

  final String imagePath;
  final int minResolution;
  final int maxResolution;
  final int maxFileSizeBytes;
  final double compressionQuality;
}

/// Data class for image compression parameters
class ImageCompressionData {
  const ImageCompressionData({
    required this.imageBytes,
    required this.extension,
    required this.maxFileSizeBytes,
    required this.initialQuality,
  });

  final Uint8List imageBytes;
  final String extension;
  final int maxFileSizeBytes;
  final double initialQuality;
}

/// Result of image processing operation
class ImageProcessingResult {
  const ImageProcessingResult._({
    required this.isSuccess,
    this.processedBytes,
    this.errorMessage,
  });

  factory ImageProcessingResult.success(Uint8List processedBytes) {
    return ImageProcessingResult._(
      isSuccess: true,
      processedBytes: processedBytes,
    );
  }

  factory ImageProcessingResult.error(String errorMessage) {
    return ImageProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? processedBytes;
  final String? errorMessage;
}

/// Internal validation result
class _ValidationResult {
  const _ValidationResult._({
    required this.isValid,
    this.error,
  });

  factory _ValidationResult.success() {
    return const _ValidationResult._(isValid: true);
  }

  factory _ValidationResult.error(String error) {
    return _ValidationResult._(isValid: false, error: error);
  }

  final bool isValid;
  final String? error;
}

// Removed isolate processing methods - using FFmpeg AVIF processing directly

/// Compress image with AVIF as primary format and intelligent fallbacks
Future<List<int>> _compressImageToTargetSizeAsync(
  img.Image image,
  String extension,
  int maxFileSizeBytes,
  double initialQuality,
) async {
  // Convert image to raw RGBA data for AVIF processing
  final width = image.width;
  final height = image.height;
  final imageData = Uint8List.fromList(img.encodePng(image));

  // Try AVIF first (best compression)
  try {
    final avifResult = await EnhancedImageProcessor.processImage(
      imageData: imageData,
      width: width,
      height: height,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality.round(),
    );

    if (avifResult.isSuccess && avifResult.bestData != null) {
      return avifResult.bestData!.toList();
    }
  } catch (e) {
    // AVIF processing failed, continue with fallbacks
  }

  // Fallback to WebP
  double quality = initialQuality;
  List<int> compressedBytes;

  try {
    do {
      compressedBytes = img.encodeWebP(image, quality: (quality * 100).round());

      if (compressedBytes.length <= maxFileSizeBytes) {
        return compressedBytes; // WebP success
      }

      if (quality > 0.3) {
        quality -= 0.1;
      } else {
        break;
      }
    } while (quality > 0.3);
  } catch (e) {
    // WebP encoding failed, fall back to JPEG
  }

  // Final fallback to JPEG
  quality = initialQuality;
  do {
    if (extension == 'png' && quality == initialQuality) {
      // Try PNG first for PNG inputs, but only at initial quality
      compressedBytes = img.encodePng(image);
    } else {
      // Use JPEG for all other cases
      compressedBytes = img.encodeJpg(image, quality: (quality * 100).round());
    }

    if (compressedBytes.length <= maxFileSizeBytes || quality <= 0.3) {
      break;
    }

    quality -= 0.1;
  } while (quality > 0.3);

  return compressedBytes;
}

// Removed fallback compression methods - AVIF-only for mobile apps
