import 'dart:io';
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

/// Service for handling image processing operations in isolates
/// This prevents UI blocking during intensive image operations
class ImageProcessingService {
  const ImageProcessingService._();

  /// Process an image file in an isolate to prevent UI blocking
  static Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  }) async {
    final data = ImageProcessingData(
      imagePath: imagePath,
      minResolution: minResolution,
      maxResolution: maxResolution,
      maxFileSizeBytes: maxFileSizeBytes,
      compressionQuality: compressionQuality,
    );

    try {
      return await compute(_processImageIsolate, data);
    } catch (e) {
      return ImageProcessingResult.error('Failed to process image: $e');
    }
  }

  /// Compress image data in an isolate
  static Future<Uint8List> compressImageInIsolate({
    required Uint8List imageBytes,
    required String extension,
    required int maxFileSizeBytes,
    required double initialQuality,
  }) async {
    final data = ImageCompressionData(
      imageBytes: imageBytes,
      extension: extension,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
    );

    try {
      return await compute(_compressImageIsolate, data);
    } catch (e) {
      throw Exception('Failed to compress image: $e');
    }
  }
}

/// Data class for image processing parameters
class ImageProcessingData {
  const ImageProcessingData({
    required this.imagePath,
    required this.minResolution,
    required this.maxResolution,
    required this.maxFileSizeBytes,
    required this.compressionQuality,
  });

  final String imagePath;
  final int minResolution;
  final int maxResolution;
  final int maxFileSizeBytes;
  final double compressionQuality;
}

/// Data class for image compression parameters
class ImageCompressionData {
  const ImageCompressionData({
    required this.imageBytes,
    required this.extension,
    required this.maxFileSizeBytes,
    required this.initialQuality,
  });

  final Uint8List imageBytes;
  final String extension;
  final int maxFileSizeBytes;
  final double initialQuality;
}

/// Result of image processing operation
class ImageProcessingResult {
  const ImageProcessingResult._({
    required this.isSuccess,
    this.processedBytes,
    this.errorMessage,
  });

  factory ImageProcessingResult.success(Uint8List processedBytes) {
    return ImageProcessingResult._(
      isSuccess: true,
      processedBytes: processedBytes,
    );
  }

  factory ImageProcessingResult.error(String errorMessage) {
    return ImageProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? processedBytes;
  final String? errorMessage;
}

/// Image processing function that runs in an isolate
ImageProcessingResult _processImageIsolate(ImageProcessingData data) {
  try {
    // Read and decode image
    final file = File(data.imagePath);
    final bytes = file.readAsBytesSync();
    final image = img.decodeImage(bytes);
    
    if (image == null) {
      return ImageProcessingResult.error('Failed to decode image');
    }

    // Validate resolution
    final width = image.width;
    final height = image.height;
    final minDimension = width < height ? width : height;
    final maxDimension = width > height ? width : height;

    if (minDimension < data.minResolution) {
      return ImageProcessingResult.error(
        'Image resolution too small. Minimum: ${data.minResolution}x${data.minResolution}px'
      );
    }

    if (maxDimension > data.maxResolution) {
      return ImageProcessingResult.error(
        'Image resolution too large. Maximum: ${data.maxResolution}x${data.maxResolution}px'
      );
    }

    // Process image (resize and crop to square)
    final processedImage = _processImage(image);

    // Compress image with adaptive quality
    final extension = data.imagePath.toLowerCase().split('.').last;
    final processedBytes = _compressImageToTargetSize(
      processedImage, 
      extension, 
      data.maxFileSizeBytes,
      data.compressionQuality,
    );

    return ImageProcessingResult.success(Uint8List.fromList(processedBytes));
  } catch (e) {
    return ImageProcessingResult.error('Image processing failed: $e');
  }
}

/// Image compression function that runs in an isolate
Uint8List _compressImageIsolate(ImageCompressionData data) {
  try {
    final image = img.decodeImage(data.imageBytes);
    if (image == null) {
      throw Exception('Failed to decode image for compression');
    }

    return Uint8List.fromList(_compressImageToTargetSize(
      image,
      data.extension,
      data.maxFileSizeBytes,
      data.initialQuality,
    ));
  } catch (e) {
    throw Exception('Image compression failed: $e');
  }
}

/// Process image to square format with center cropping
img.Image _processImage(img.Image image) {
  final width = image.width;
  final height = image.height;
  final size = width < height ? width : height;

  // Crop to square (center crop)
  final x = (width - size) ~/ 2;
  final y = (height - size) ~/ 2;
  
  return img.copyCrop(image, x: x, y: y, width: size, height: size);
}

/// Compress image to target size with adaptive quality
List<int> _compressImageToTargetSize(
  img.Image image,
  String extension,
  int maxFileSizeBytes,
  double initialQuality,
) {
  double quality = initialQuality;
  List<int> compressedBytes;

  do {
    if (extension == 'png') {
      compressedBytes = img.encodePng(image);
    } else {
      // Convert to JPEG for better compression
      compressedBytes = img.encodeJpg(image, quality: (quality * 100).round());
    }

    // If still too large, reduce quality
    if (compressedBytes.length > maxFileSizeBytes && quality > 0.3) {
      quality -= 0.1;
    } else {
      break;
    }
  } while (quality > 0.3);

  return compressedBytes;
}
