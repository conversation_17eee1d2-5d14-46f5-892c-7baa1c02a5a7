import 'dart:io';
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

import 'avif_processing_service.dart';

import 'avif_processing_service.dart';

/// Service for handling image processing operations in isolates
/// This prevents UI blocking during intensive image operations
class ImageProcessingService {
  const ImageProcessingService._();

  /// Process an image file with AVIF as primary format
  static Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  }) async {
    try {
      // First, validate image dimensions and basic requirements
      final validationResult = await _validateImageRequirements(
        imagePath,
        minResolution,
        maxResolution,
      );

      if (!validationResult.isValid) {
        return ImageProcessingResult.error(validationResult.error!);
      }

      // Use FFmpeg-based AVIF processing for optimal compression
      final avifResult = await EnhancedImageProcessor.processImage(
        inputPath: imagePath,
        maxFileSizeBytes: maxFileSizeBytes,
        initialQuality: compressionQuality.round(),
        maxWidth: maxResolution,
        maxHeight: maxResolution,
      );

      if (avifResult.isSuccess && avifResult.bestData != null) {
        return ImageProcessingResult.success(avifResult.bestData!);
      }

      // Fallback to traditional processing if AVIF fails
      final data = ImageProcessingData(
        imagePath: imagePath,
        minResolution: minResolution,
        maxResolution: maxResolution,
        maxFileSizeBytes: maxFileSizeBytes,
        compressionQuality: compressionQuality,
      );

      return await compute(_processImageIsolate, data);
    } catch (e) {
      return ImageProcessingResult.error('Failed to process image: $e');
    }
  }

  /// Validate image requirements before processing
  static Future<_ValidationResult> _validateImageRequirements(
    String imagePath,
    int minResolution,
    int maxResolution,
  ) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        return _ValidationResult.error('Image file not found');
      }

      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        return _ValidationResult.error('Invalid image format');
      }

      final width = image.width;
      final height = image.height;
      final minDimension = width < height ? width : height;
      final maxDimension = width > height ? width : height;

      if (minDimension < minResolution) {
        return _ValidationResult.error(
          'Image resolution too small. Minimum: ${minResolution}x${minResolution}px'
        );
      }

      if (maxDimension > maxResolution) {
        return _ValidationResult.error(
          'Image resolution too large. Maximum: ${maxResolution}x${maxResolution}px'
        );
      }

      return _ValidationResult.success();
    } catch (e) {
      return _ValidationResult.error('Image validation failed: $e');
    }
  }

  /// Compress image data in an isolate
  static Future<Uint8List> compressImageInIsolate({
    required Uint8List imageBytes,
    required String extension,
    required int maxFileSizeBytes,
    required double initialQuality,
  }) async {
    final data = ImageCompressionData(
      imageBytes: imageBytes,
      extension: extension,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
    );

    try {
      return await compute(_compressImageIsolate, data);
    } catch (e) {
      throw Exception('Failed to compress image: $e');
    }
  }
}

/// Data class for image processing parameters
class ImageProcessingData {
  const ImageProcessingData({
    required this.imagePath,
    required this.minResolution,
    required this.maxResolution,
    required this.maxFileSizeBytes,
    required this.compressionQuality,
  });

  final String imagePath;
  final int minResolution;
  final int maxResolution;
  final int maxFileSizeBytes;
  final double compressionQuality;
}

/// Data class for image compression parameters
class ImageCompressionData {
  const ImageCompressionData({
    required this.imageBytes,
    required this.extension,
    required this.maxFileSizeBytes,
    required this.initialQuality,
  });

  final Uint8List imageBytes;
  final String extension;
  final int maxFileSizeBytes;
  final double initialQuality;
}

/// Result of image processing operation
class ImageProcessingResult {
  const ImageProcessingResult._({
    required this.isSuccess,
    this.processedBytes,
    this.errorMessage,
  });

  factory ImageProcessingResult.success(Uint8List processedBytes) {
    return ImageProcessingResult._(
      isSuccess: true,
      processedBytes: processedBytes,
    );
  }

  factory ImageProcessingResult.error(String errorMessage) {
    return ImageProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? processedBytes;
  final String? errorMessage;
}

/// Internal validation result
class _ValidationResult {
  const _ValidationResult._({
    required this.isValid,
    this.error,
  });

  factory _ValidationResult.success() {
    return const _ValidationResult._(isValid: true);
  }

  factory _ValidationResult.error(String error) {
    return _ValidationResult._(isValid: false, error: error);
  }

  final bool isValid;
  final String? error;
}

/// Image processing function that runs in an isolate
ImageProcessingResult _processImageIsolate(ImageProcessingData data) {
  try {
    // Read and decode image
    final file = File(data.imagePath);
    final bytes = file.readAsBytesSync();
    final image = img.decodeImage(bytes);
    
    if (image == null) {
      return ImageProcessingResult.error('Failed to decode image');
    }

    // Validate resolution
    final width = image.width;
    final height = image.height;
    final minDimension = width < height ? width : height;
    final maxDimension = width > height ? width : height;

    if (minDimension < data.minResolution) {
      return ImageProcessingResult.error(
        'Image resolution too small. Minimum: ${data.minResolution}x${data.minResolution}px'
      );
    }

    if (maxDimension > data.maxResolution) {
      return ImageProcessingResult.error(
        'Image resolution too large. Maximum: ${data.maxResolution}x${data.maxResolution}px'
      );
    }

    // Process image (resize and crop to square)
    final processedImage = _processImage(image);

    // Compress image with adaptive quality
    final extension = data.imagePath.toLowerCase().split('.').last;
    final processedBytes = _compressImageToTargetSize(
      processedImage, 
      extension, 
      data.maxFileSizeBytes,
      data.compressionQuality,
    );

    return ImageProcessingResult.success(Uint8List.fromList(processedBytes));
  } catch (e) {
    return ImageProcessingResult.error('Image processing failed: $e');
  }
}

/// Image compression function that runs in an isolate
Uint8List _compressImageIsolate(ImageCompressionData data) {
  try {
    final image = img.decodeImage(data.imageBytes);
    if (image == null) {
      throw Exception('Failed to decode image for compression');
    }

    return Uint8List.fromList(_compressImageToTargetSize(
      image,
      data.extension,
      data.maxFileSizeBytes,
      data.initialQuality,
    ));
  } catch (e) {
    throw Exception('Image compression failed: $e');
  }
}

/// Process image to square format with center cropping
img.Image _processImage(img.Image image) {
  final width = image.width;
  final height = image.height;
  final size = width < height ? width : height;

  // Crop to square (center crop)
  final x = (width - size) ~/ 2;
  final y = (height - size) ~/ 2;
  
  return img.copyCrop(image, x: x, y: y, width: size, height: size);
}

/// Compress image with AVIF as primary format and intelligent fallbacks
Future<List<int>> _compressImageToTargetSizeAsync(
  img.Image image,
  String extension,
  int maxFileSizeBytes,
  double initialQuality,
) async {
  // Convert image to raw RGBA data for AVIF processing
  final width = image.width;
  final height = image.height;
  final imageData = Uint8List.fromList(img.encodePng(image));

  // Try AVIF first (best compression)
  try {
    final avifResult = await EnhancedImageProcessor.processImage(
      imageData: imageData,
      width: width,
      height: height,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality.round(),
    );

    if (avifResult.isSuccess && avifResult.bestData != null) {
      return avifResult.bestData!.toList();
    }
  } catch (e) {
    // AVIF processing failed, continue with fallbacks
  }

  // Fallback to WebP
  double quality = initialQuality;
  List<int> compressedBytes;

  try {
    do {
      compressedBytes = img.encodeWebP(image, quality: (quality * 100).round());

      if (compressedBytes.length <= maxFileSizeBytes) {
        return compressedBytes; // WebP success
      }

      if (quality > 0.3) {
        quality -= 0.1;
      } else {
        break;
      }
    } while (quality > 0.3);
  } catch (e) {
    // WebP encoding failed, fall back to JPEG
  }

  // Final fallback to JPEG
  quality = initialQuality;
  do {
    if (extension == 'png' && quality == initialQuality) {
      // Try PNG first for PNG inputs, but only at initial quality
      compressedBytes = img.encodePng(image);
    } else {
      // Use JPEG for all other cases
      compressedBytes = img.encodeJpg(image, quality: (quality * 100).round());
    }

    if (compressedBytes.length <= maxFileSizeBytes || quality <= 0.3) {
      break;
    }

    quality -= 0.1;
  } while (quality > 0.3);

  return compressedBytes;
}

/// Synchronous fallback compression (for isolate compatibility)
List<int> _compressImageToTargetSize(
  img.Image image,
  String extension,
  int maxFileSizeBytes,
  double initialQuality,
) {
  double quality = initialQuality;
  List<int> compressedBytes;

  // Try WebP first (best available compression in img package)
  try {
    do {
      compressedBytes = img.encodeWebP(image, quality: (quality * 100).round());

      if (compressedBytes.length <= maxFileSizeBytes) {
        return compressedBytes; // WebP success
      }

      if (quality > 0.65) {  // Don't go below 65% for 2MB target
        quality -= 0.05;     // Smaller steps for fine-tuning
      } else {
        break;
      }
    } while (quality > 0.65);
  } catch (e) {
    // WebP encoding failed, fall back to JPEG
  }

  // Fallback to JPEG if WebP fails or is too large
  quality = initialQuality;
  do {
    if (extension == 'png' && quality == initialQuality) {
      // Try PNG first for PNG inputs, but only at initial quality
      compressedBytes = img.encodePng(image);
    } else {
      // Use JPEG for all other cases
      compressedBytes = img.encodeJpg(image, quality: (quality * 100).round());
    }

    if (compressedBytes.length <= maxFileSizeBytes || quality <= 0.70) {  // Don't go below 70% for JPEG
      break;
    }

    quality -= 0.05;  // Smaller steps for fine-tuning
  } while (quality > 0.70);

  return compressedBytes;
}
