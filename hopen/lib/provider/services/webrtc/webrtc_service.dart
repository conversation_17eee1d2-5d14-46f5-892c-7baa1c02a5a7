import 'dart:async';

import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:uuid/uuid.dart';

import '../../../config/app_config.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/error/failures.dart';
import '../../../statefulbusinesslogic/core/models/call_model.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../../di/injection_container_refactored.dart' as di;
import '../http3_client_service.dart';
import 'dart:convert';

class ConnectionQuality {
  String iceState = 'new';
  double connectionScore = 0;
  double roundTripTime = 0;
  double audioJitter = 0;
  double videoJitter = 0;
  int audioPacketsSent = 0;
  int audioPacketsReceived = 0;
  int audioPacketsLost = 0;
  int videoPacketsSent = 0;
  int videoPacketsReceived = 0;
  int videoPacketsLost = 0;
  int videoFramesReceived = 0;
  int videoFramesDropped = 0;
  int audioBitrate = 0;
  int videoBitrate = 0;
  int availableOutgoingBitrate = 0;
  DateTime lastUpdated = DateTime.now();
}

class ConnectionQualityUpdate {
  ConnectionQualityUpdate({required this.callId, required this.quality});
  final String callId;
  final ConnectionQuality quality;
}

class CallRecordingState {
  CallRecordingState({
    required this.callId,
    required this.status,
    this.recordingId,
    this.startedAt,
    this.duration,
    this.fileUrl,
    this.fileSize,
    this.format,
    this.quality,
    this.initiatedBy,
  });

  final String callId;
  final String status; // recording, paused, stopped, processing, completed, failed
  final String? recordingId;
  final DateTime? startedAt;
  final int? duration; // in seconds
  final String? fileUrl;
  final int? fileSize; // in bytes
  final String? format; // webm, mp4
  final String? quality; // low, medium, high
  final String? initiatedBy;
}

class GroupCallUpdate {
  GroupCallUpdate({
    required this.callId,
    this.layout,
    this.activeSpeaker,
    this.presentationMode,
    this.presenterId,
    this.screenShareActive,
    this.screenShareUserId,
    this.maxParticipants,
  });

  final String callId;
  final String? layout; // grid, spotlight, presentation
  final String? activeSpeaker;
  final bool? presentationMode;
  final String? presenterId;
  final bool? screenShareActive;
  final String? screenShareUserId;
  final int? maxParticipants;
}

class WebRTCService {
  static const String _tag = 'WebRTCService';

  Map<String, dynamic> get _configuration => AppConfig.webrtcConfiguration;

  final Map<String, RTCPeerConnection> _peerConnections = {};
  final Map<String, MediaStream> _localStreams = {};
  final Map<String, MediaStream> _remoteStreams = {};
  final Map<String, ConnectionQuality> _connectionQuality = {};

  final Map<String, CallModel> _activeCalls = {};
  final Map<String, bool> _audioMuted = {};
  final Map<String, bool> _videoMuted = {};
  final Map<String, Timer> _qualityMonitoringTimers = {};

  final StreamController<CallModel> _callStateController = StreamController<CallModel>.broadcast();
  final StreamController<String> _callEndedController = StreamController<String>.broadcast();
  final StreamController<ConnectionQualityUpdate> _qualityController = StreamController<ConnectionQualityUpdate>.broadcast();
  final StreamController<CallRecordingState> _recordingController = StreamController<CallRecordingState>.broadcast();
  final StreamController<GroupCallUpdate> _groupFeaturesController = StreamController<GroupCallUpdate>.broadcast();
  final StreamController<String?> _activeSpeakerController = StreamController<String?>.broadcast();

  Stream<CallModel> get callStateStream => _callStateController.stream;
  Stream<String> get callEndedStream => _callEndedController.stream;
  Stream<ConnectionQualityUpdate> get qualityStream => _qualityController.stream;
  Stream<CallRecordingState> get recordingStream => _recordingController.stream;
  Stream<GroupCallUpdate> get groupFeaturesStream => _groupFeaturesController.stream;
  Stream<String?> get activeSpeakerStream => _activeSpeakerController.stream;

  /// Initialize the WebRTC service with enhanced error handling
  Future<Result<bool>> initialize() async {
    try {
              LoggingService.info('Initializing WebRTC service');
      // await WebRTC.initialize(); // Often not needed in newer versions
      LoggingService.success('WebRTC service initialized successfully');
      return Result.success(true);
    } catch (e, stackTrace) {
      LoggingService.error('Failed to initialize WebRTC service', 
          error: e, stackTrace: stackTrace,);
      return Result.failure(UnexpectedFailure(
        message: 'WebRTC initialization failed: ${e.toString()}',
      ));
    }
  }

  Future<Result<CallModel>> startVoiceCall({
    required String callerId,
    required String calleeId,
    String quality = 'medium',
  }) async => _startCall(
      callerId: callerId,
      calleeId: calleeId,
      callType: CallType.voice,
      quality: quality,
    );

  Future<Result<CallModel>> startVideoCall({
    required String callerId,
    required String calleeId,
    String quality = 'medium',
    bool enableScreenShare = false,
  }) async => _startCall(
      callerId: callerId,
      calleeId: calleeId,
      callType: CallType.video,
      quality: quality,
      enableScreenShare: enableScreenShare,
    );

  Future<Result<CallModel>> _startCall({
    required String callerId,
    required String calleeId,
    required CallType callType,
    String quality = 'medium',
    bool enableScreenShare = false,
  }) async {
    try {
      LoggingService.info('Starting ${callType.toString()} call from $callerId to $calleeId with $quality quality');
      
      final callId = const Uuid().v4();
      final call = CallModel(
        id: callId,
        callerId: callerId,
        calleeId: calleeId,
        type: callType,
        status: CallStatus.ringing,
        createdAt: DateTime.now(),
      );
      
      _activeCalls[callId] = call;
      
      final peerConnection = await createPeerConnection(_configuration);
      _peerConnections[callId] = peerConnection;
      
      _setupConnectionMonitoring(callId, peerConnection);
      
      final mediaConstraints = AppConfig.getWebrtcConstraintsForQuality(quality);
      
      MediaStream stream;
      if (enableScreenShare) {
        stream = await navigator.mediaDevices.getDisplayMedia({'video': true, 'audio': true});
      } else {
        stream = await navigator.mediaDevices.getUserMedia({
          'audio': mediaConstraints['audio'],
          'video': callType == CallType.video ? mediaConstraints['video'] : false,
        });
      }
      
      _localStreams[callId] = stream;
      
      stream.getTracks().forEach((track) {
        peerConnection.addTrack(track, stream);
      });
      
      _startQualityMonitoring(callId, peerConnection);
      
      _callStateController.add(call);
      
      LoggingService.success('${callType.toString()} call started successfully');
      return Result.success(call);
    } catch (e, stackTrace) {
      LoggingService.error('Failed to start ${callType.toString()} call', error: e, stackTrace: stackTrace);
      return Result.failure(NetworkError(message: 'Failed to start ${callType.toString()} call: $e'));
    }
  }

  void _setupConnectionMonitoring(String callId, RTCPeerConnection peerConnection) {
    peerConnection.onConnectionState = (state) {
      LoggingService.info('Call $callId connection state: $state');
      switch (state) {
        case RTCPeerConnectionState.RTCPeerConnectionStateConnecting:
          _updateCallStatus(callId, CallStatus.initiating);
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateConnected:
          _updateCallStatus(callId, CallStatus.connected);
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateFailed:
        case RTCPeerConnectionState.RTCPeerConnectionStateClosed:
        case RTCPeerConnectionState.RTCPeerConnectionStateDisconnected:
          endCall(callId, reason: 'Connection lost');
          break;
        default:
          break;
      }
    };

    peerConnection.onIceConnectionState = (state) {
      LoggingService.info('Call $callId ICE connection state: $state');
      final quality = _connectionQuality[callId] ?? ConnectionQuality();
      switch (state) {
        case RTCIceConnectionState.RTCIceConnectionStateConnected:
        case RTCIceConnectionState.RTCIceConnectionStateCompleted:
          quality.iceState = 'connected';
          quality.connectionScore = 1.0;
          break;
        case RTCIceConnectionState.RTCIceConnectionStateChecking:
          quality.iceState = 'checking';
          quality.connectionScore = 0.5;
          break;
        case RTCIceConnectionState.RTCIceConnectionStateFailed:
        case RTCIceConnectionState.RTCIceConnectionStateDisconnected:
          quality.iceState = 'failed';
          quality.connectionScore = 0.0;
          break;
        default:
          quality.iceState = state.toString();
          break;
      }
      _connectionQuality[callId] = quality;
      _qualityController.add(ConnectionQualityUpdate(callId: callId, quality: quality));
    };

    peerConnection.onTrack = (event) {
      LoggingService.info('Track received for call $callId: ${event.track.kind}');
      if (event.streams.isNotEmpty) {
        _remoteStreams[callId] = event.streams[0];
        _callStateController.add(_activeCalls[callId]!);
      }
    };

    peerConnection.onDataChannel = (dataChannel) {
      LoggingService.info('Data channel opened for call $callId: ${dataChannel.label}');
      dataChannel.onMessage = (message) {
        try {
          if (message.text.contains('quality_metrics')) {
            LoggingService.info('Received quality metrics: ${message.text}');
          }
        } catch (e, stackTrace) {
          LoggingService.error('Error handling data channel message', error: e, stackTrace: stackTrace);
        }
      };
    };
  }

  void _startQualityMonitoring(String callId, RTCPeerConnection peerConnection) {
    _qualityMonitoringTimers[callId] = Timer.periodic(const Duration(seconds: 5), (timer) async {
      final pc = _peerConnections[callId];
      if (pc == null) return;

      try {
        final stats = await pc.getStats();

        final quality = _connectionQuality[callId] ?? ConnectionQuality();

        for (final report in stats) {
          switch (report.type) {
            case 'outbound-rtp':
              final bytesSent = report.values['bytesSent'] as int? ?? 0;
              final packetsSent = report.values['packetsSent'] as int? ?? 0;
              quality.videoPacketsSent = packetsSent;
              quality.videoBitrate = bytesSent;
              break;
            case 'inbound-rtp':
              final bytesReceived = report.values['bytesReceived'] as int? ?? 0;
              final packetsReceived = report.values['packetsReceived'] as int? ?? 0;
              final packetsLost = report.values['packetsLost'] as int? ?? 0;
              quality.videoPacketsReceived = packetsReceived;
              quality.videoPacketsLost = packetsLost;
              quality.availableOutgoingBitrate = bytesReceived;
              break;
            case 'candidate-pair':
              final rtt = report.values['currentRoundTripTime'] as double? ?? 0.0;
              quality.roundTripTime = rtt;
              break;
          }
        }

        quality.lastUpdated = DateTime.now();
        _connectionQuality[callId] = quality;
        _qualityController.add(ConnectionQualityUpdate(callId: callId, quality: quality));
      } catch (e, s) {
        LoggingService.error('Error collecting WebRTC stats', error: e, stackTrace: s);
      }
    });
  }

  void _stopQualityMonitoring(String callId) {
    _qualityMonitoringTimers[callId]?.cancel();
    _qualityMonitoringTimers.remove(callId);
    _connectionQuality.remove(callId);
  }

  Future<Result<void>> endCall(String callId, {String reason = 'ended_by_user'}) async {
    try {
      LoggingService.info('Ending call $callId, reason: $reason');
      _stopQualityMonitoring(callId);
      
      await _peerConnections[callId]?.close();
      _peerConnections.remove(callId);
      
      await _localStreams[callId]?.dispose();
      _localStreams.remove(callId);
      
      await _remoteStreams[callId]?.dispose();
      _remoteStreams.remove(callId);
      
      _updateCallStatus(callId, CallStatus.ended);
      _activeCalls.remove(callId);
      _audioMuted.remove(callId);
      _videoMuted.remove(callId);
      
      LoggingService.success('Call $callId ended successfully');
      return Result.success(null);
    } catch (e, stackTrace) {
      LoggingService.error('Failed to end call', error: e, stackTrace: stackTrace);
      return Result.failure(NetworkError(message: 'Failed to end call: $e'));
    }
  }

  void _updateCallStatus(String callId, CallStatus status) {
    final call = _activeCalls[callId];
    if (call != null) {
      final updatedCall = call.copyWith(status: status);
      _activeCalls[callId] = updatedCall;
      _callStateController.add(updatedCall);
      if (status == CallStatus.ended) {
        _callEndedController.add(callId);
      }
    }
  }

  // Call Recording Methods
  Future<Result<CallRecordingState>> startRecording(String callId, {
    String quality = 'medium',
    String format = 'webm',
  }) async {
    try {
      LoggingService.info('Starting call recording', tag: _tag);

      final httpClient = Http3ClientService();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/recording/start',
        headers: {'Content-Type': 'application/json'},
        body: {
          'quality': quality,
          'format': format,
        },
      );

      final CallRecordingState recordingState;
      if (response.statusCode == 200) {
        final recordingData = jsonDecode(response.body) as Map<String, dynamic>? ?? {};
        recordingState = CallRecordingState(
          callId: callId,
          status: recordingData['status'] ?? 'recording',
          recordingId: recordingData['id'] ?? const Uuid().v4(),
          startedAt: DateTime.now(),
          format: format,
          quality: quality,
          initiatedBy: recordingData['initiated_by'] ?? 'current_user',
        );
      } else {
        return Result.failure(NetworkError(message: 'Failed to start recording: ${response.reasonPhrase}'));
      }

      _recordingController.add(recordingState);
      LoggingService.success('Call recording started successfully', tag: _tag);
      return Result.success(recordingState);
    } catch (e, stackTrace) {
      LoggingService.error('Failed to start recording', error: e, stackTrace: stackTrace, tag: _tag);
      return Result.failure(NetworkError(message: 'Failed to start recording: $e'));
    }
  }

  Future<Result<CallRecordingState>> stopRecording(String callId) async {
    try {
      LoggingService.info('Stopping call recording', tag: _tag);

      final httpClient = Http3ClientService();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/recording/stop',
        headers: {'Content-Type': 'application/json'},
      );

      final CallRecordingState recordingState;
      if (response.statusCode == 200) {
        final recordingData = jsonDecode(response.body) as Map<String, dynamic>? ?? {};
        recordingState = CallRecordingState(
          callId: callId,
          status: recordingData['status'] ?? 'stopped',
          recordingId: recordingData['id'] ?? const Uuid().v4(),
          startedAt: DateTime.now(),
          duration: recordingData['duration'],
          fileUrl: recordingData['file_url'],
          format: recordingData['format'] ?? 'webm',
          quality: recordingData['quality'] ?? 'medium',
          initiatedBy: recordingData['initiated_by'] ?? 'current_user',
        );
      } else {
        return Result.failure(NetworkError(message: 'Failed to stop recording: ${response.reasonPhrase}'));
      }

      _recordingController.add(recordingState);
      LoggingService.success('Call recording stopped successfully', tag: _tag);
      return Result.success(recordingState);
    } catch (e, stackTrace) {
      LoggingService.error('Failed to stop recording', error: e, stackTrace: stackTrace, tag: _tag);
      return Result.failure(NetworkError(message: 'Failed to stop recording: $e'));
    }
  }

  Future<Result<CallRecordingState>> pauseRecording(String callId) async {
    try {
      final httpClient = Http3ClientService();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/recording/pause',
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final recordingData = jsonDecode(response.body) as Map<String, dynamic>? ?? {};
        final recordingState = CallRecordingState(
          callId: callId,
          status: recordingData['status'] ?? 'paused',
          recordingId: recordingData['id'] ?? const Uuid().v4(),
        );
        _recordingController.add(recordingState);
        return Result.success(recordingState);
      } else {
        return Result.failure(NetworkError(message: 'Failed to pause recording: ${response.reasonPhrase}'));
      }
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to pause recording: $e'));
    }
  }

  Future<Result<CallRecordingState>> resumeRecording(String callId) async {
    try {
      final httpClient = Http3ClientService();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/recording/resume',
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final recordingData = jsonDecode(response.body) as Map<String, dynamic>? ?? {};
        final recordingState = CallRecordingState(
          callId: callId,
          status: recordingData['status'] ?? 'recording',
          recordingId: recordingData['id'] ?? const Uuid().v4(),
        );
        _recordingController.add(recordingState);
        return Result.success(recordingState);
      } else {
        return Result.failure(NetworkError(message: 'Failed to resume recording: ${response.reasonPhrase}'));
      }
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to resume recording: $e'));
    }
  }

  // Group Call Features
  Future<Result<void>> setCallLayout(String callId, String layout) async {
    try {
      final httpClient = Http3ClientService();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/layout',
        headers: {'Content-Type': 'application/json'},
        body: {'layout': layout},
      );

      if (response.statusCode == 200) {
        final groupUpdate = GroupCallUpdate(
          callId: callId,
          layout: layout,
        );
        _groupFeaturesController.add(groupUpdate);
        return Result.success(null);
      } else {
        return Result.failure(NetworkError(message: 'Failed to set layout: ${response.reasonPhrase}'));
      }
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to set layout: $e'));
    }
  }

  Future<Result<void>> setActiveSpeaker(String callId, String speakerId) async {
    try {
      final httpClient = Http3ClientService();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/speaker',
        headers: {'Content-Type': 'application/json'},
        body: {'speaker_id': speakerId},
      );

      if (response.statusCode == 200) {
        final groupUpdate = GroupCallUpdate(
          callId: callId,
          activeSpeaker: speakerId,
        );
        _groupFeaturesController.add(groupUpdate);
        _activeSpeakerController.add(speakerId);
        return Result.success(null);
      } else {
        return Result.failure(NetworkError(message: 'Failed to set active speaker: ${response.reasonPhrase}'));
      }
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to set active speaker: $e'));
    }
  }

  Future<Result<void>> startPresentation(String callId) async {
    try {
      final httpClient = await di.sl.getAsync<Http3ClientService>();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/presentation/start',
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final groupUpdate = GroupCallUpdate(
          callId: callId,
          presentationMode: true,
          layout: 'presentation',
        );
        _groupFeaturesController.add(groupUpdate);
        return Result.success(null);
      } else {
        return Result.failure(NetworkError(message: 'Failed to start presentation: ${response.reasonPhrase}'));
      }
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to start presentation: $e'));
    }
  }

  Future<Result<void>> stopPresentation(String callId) async {
    try {
      final httpClient = Http3ClientService();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/presentation/stop',
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final groupUpdate = GroupCallUpdate(
          callId: callId,
          presentationMode: false,
          layout: 'grid',
        );
        _groupFeaturesController.add(groupUpdate);
        return Result.success(null);
      } else {
        return Result.failure(NetworkError(message: 'Failed to stop presentation: ${response.reasonPhrase}'));
      }
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to stop presentation: $e'));
    }
  }

  Future<Result<void>> setBandwidthLimits(String callId, Map<String, int> limits) async {
    try {
      final httpClient = Http3ClientService();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/bandwidth',
        headers: {'Content-Type': 'application/json'},
        body: {'limits': limits},
      );

      if (response.statusCode == 200) {
        return Result.success(null);
      } else {
        return Result.failure(NetworkError(message: 'Failed to set bandwidth limits: ${response.reasonPhrase}'));
      }
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to set bandwidth limits: $e'));
    }
  }

  Future<Result<void>> setQualityProfiles(String callId, Map<String, String> profiles) async {
    try {
      final httpClient = Http3ClientService();
      final response = await httpClient.post(
        '${AppConfig.backendUrl}/calls/$callId/quality',
        headers: {'Content-Type': 'application/json'},
        body: {'profiles': profiles},
      );

      if (response.statusCode == 200) {
        return Result.success(null);
      } else {
        return Result.failure(NetworkError(message: 'Failed to set quality profiles: ${response.reasonPhrase}'));
      }
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to set quality profiles: $e'));
    }
  }

  Future<Result<Map<String, dynamic>>> getCallAnalytics(String callId) async {
    try {
      final httpClient = await di.sl.getAsync<Http3ClientService>();
      final response = await httpClient.get(
        '${AppConfig.backendUrl}/calls/$callId/analytics',
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return Result.success(jsonDecode(response.body) as Map<String, dynamic>);
      } else {
        return Result.failure(NetworkError(message: 'Failed to get call analytics: ${response.reasonPhrase}'));
      }
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get call analytics: $e'));
    }
  }

  void dispose() {
    _activeCalls.keys.toList().forEach((callId) => endCall(callId, reason: 'service_disposed'));
    _callStateController.close();
    _callEndedController.close();
    _qualityController.close();
    _recordingController.close();
    _groupFeaturesController.close();
    _activeSpeakerController.close();
    LoggingService.info('WebRTCService disposed');
  }
} 