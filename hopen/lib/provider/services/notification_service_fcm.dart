import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

/// FCM-only notification service for background push notifications
/// This service handles Firebase Cloud Messaging for push notifications
/// when the app is backgrounded or terminated.
class NotificationServiceFCM {
  factory NotificationServiceFCM() => _instance;
  NotificationServiceFCM._internal();
  static final NotificationServiceFCM _instance = NotificationServiceFCM._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    try {
      // Initialize local notifications for better native integration
      await _initializeLocalNotifications();

      // Set up Firebase message handlers
      FirebaseMessaging.onBackgroundMessage(
        _firebaseMessagingBackgroundHandler,
      );

      FirebaseMessaging.onMessage.listen((message) {
        _handleForegroundMessage(message);
      });

      FirebaseMessaging.onMessageOpenedApp.listen((message) {
        _handleMessageOpenedApp(message);
      });
    } catch (e, stackTrace) {
      // Use basic print for logging to avoid layer violations
      print('!!! FAILED TO INITIALIZE NotificationServiceFCM: $e');
      print('Stack trace: $stackTrace');
    }
  }

  /// Initialize local notifications for better native integration
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: false, // We'll request this manually through FCM
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(initSettings);
    print('Local notifications initialized for native integration');
  }

  /// Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    print('Handling foreground message: ${message.messageId}');
    // Show local notification when app is in foreground
    _showLocalNotification(message);
  }

  /// Handle messages when app is opened from notification
  void _handleMessageOpenedApp(RemoteMessage message) {
    print('Handling message opened app: ${message.messageId}');
    // Handle navigation or other actions when notification is tapped
  }

  /// Show local notification for foreground messages
  Future<void> _showLocalNotification(RemoteMessage message) async {
    const androidDetails = AndroidNotificationDetails(
      'default_channel',
      'Default Channel',
      channelDescription: 'Default notification channel',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Hopen',
      message.notification?.body ?? 'You have a new notification',
      details,
    );
  }

  /// Explicitly ask the user for notification permission using native system dialogs.
  /// This method ensures the native iOS/Android permission dialog appears.
  /// Returns true if granted, false if denied.
  Future<bool> requestSystemPermission() async {
    try {
      print('Requesting native notification permission...');

      AuthorizationStatus fcmAuthStatus = AuthorizationStatus.notDetermined;

      if (Platform.isAndroid) {
        // On Android 13+ the permission dialog is surfaced via permission_handler.
        final status = await Permission.notification.status;
        print('Current Android notification permission status: $status');

        if (status.isDenied || status.isRestricted) {
          print('Showing Android system permission dialog...');
          final reqResult = await Permission.notification.request();
          print('Android permission request result: $reqResult');

          if (reqResult.isPermanentlyDenied) {
            print('Permission denied by user');
            // Do not automatically redirect the user to the application settings. Simply
            // return `false` so the UI can react the same way as a regular denial and the
            // user can manually enable notifications later if desired.
            return false;
          }

          if (!reqResult.isGranted) {
            return false;
          }
        }

        // Even if permission_handler reports granted, call FCM to ensure token retrieval.
        final settings = await _firebaseMessaging.requestPermission();
        fcmAuthStatus = settings.authorizationStatus;
      } else {
        // iOS – FCM handles the native dialog.
        final settings = await _firebaseMessaging.requestPermission(
          alert: true,
          announcement: false,
          badge: true,
          carPlay: false,
          criticalAlert: false,
          provisional: false,
          sound: true,
        );
        fcmAuthStatus = settings.authorizationStatus;
      }

      print('FCM permission result: $fcmAuthStatus');

      // Determine authorization result
      bool isAuthorized = false;
      switch (fcmAuthStatus) {
        case AuthorizationStatus.authorized:
          print('NotificationServiceFCM: Permission GRANTED');
          isAuthorized = true;
          break;
        case AuthorizationStatus.provisional:
          print('NotificationServiceFCM: Permission PROVISIONAL (iOS quiet notifications)');
          isAuthorized = true;
          break;
        case AuthorizationStatus.denied:
          print('NotificationServiceFCM: Permission DENIED by user');
          isAuthorized = false;
          break;
        case AuthorizationStatus.notDetermined:
          print('NotificationServiceFCM: Permission NOT DETERMINED');
          isAuthorized = false;
          break;
        default:
          print('NotificationServiceFCM: Unknown permission status: $fcmAuthStatus');
          isAuthorized = false;
      }

      // If permission granted, get FCM token for verification
      if (isAuthorized) {
        try {
          final token = await _firebaseMessaging.getToken();
          if (token != null) {
            print('FCM token obtained successfully: ${token.substring(0, 20)}...');

            // Store token or send to backend if needed
            await _handleTokenReceived(token);
          } else {
            print('FCM token is null - Firebase connection may be unstable');
          }
        } catch (tokenError) {
          print('Failed to get FCM token after permission granted: $tokenError');
          // Don't fail the permission request just because token retrieval failed
        }
      }

      return isAuthorized;
    } catch (e) {
      print('Failed to request notification permission: $e');

      // Re-throw with more context for better error handling upstream
      if (e.toString().contains('MissingPluginException')) {
        throw Exception('Firebase Messaging plugin not properly configured');
      } else if (e.toString().contains('PlatformException')) {
        throw Exception('Platform-specific error occurred during permission request');
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        throw Exception('Network error during Firebase permission request');
      } else {
        throw Exception('Failed to request notification permission: ${e.toString()}');
      }
    }
  }

  /// Handle FCM token received
  Future<void> _handleTokenReceived(String token) async {
    print('Handling FCM token: ${token.substring(0, 20)}...');
    // Here you can send the token to your backend server
    // or store it locally for later use
  }

  Future<String?> getToken() async => _firebaseMessaging.getToken();
}

// Handle background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  // Handle background message - FCM will show notification automatically
  print('Handling background message: ${message.messageId}');
} 