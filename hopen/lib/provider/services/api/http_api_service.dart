import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../config/app_config.dart';
import '../../../di/injection_container_refactored.dart' as di;
import '../http3_client_service.dart';
import '../../models/api_models.dart';
import '../../exceptions/api_exceptions.dart';
import '../../../statefulbusinesslogic/core/error/exceptions.dart' as core_exceptions;
import '../auth/ory_auth_service.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';

/// HTTP API Service replacing legacy API layer; relies on Http3ClientService
class HttpApiService {
  static final HttpApiService _instance = HttpApiService._internal();
  factory HttpApiService() => _instance;
  HttpApiService._internal();

  Http3ClientService? _httpClient;
  String get baseUrl => AppConfig.backendUrl;

  // Prefix added to every backend route so callers only specify the path
  static const String _apiPrefix = '/api/v1';

  bool _isInitialized = false;

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      // Get the singleton instance from dependency injection
      _httpClient = await di.sl.getAsync<Http3ClientService>();
      _isInitialized = true;
    }
  }

  /// Normalise outdated routes to current backend routes
  String _normalizePath(String path) {
    // No bubble path rewriting; backend uses plural '/bubbles'
    // Legacy pluralised contact requests prefix
    if (path.startsWith('/contacts/requests')) {
      return path.replaceFirst('/contacts/requests', '/contact/requests');
    }
    return path;
  }

  /// Get authentication headers if available
  Future<Map<String, String>> _getAuthHeaders() async {
    try {
      print('🔍 HttpApiService._getAuthHeaders: Starting...');
      final oryAuthService = di.sl<OryAuthService>();
      print('🔍 HttpApiService._getAuthHeaders: Got OryAuthService');

      final token = await oryAuthService.getValidToken();
      print('🔍 HttpApiService._getAuthHeaders: Got token: ${token?.substring(0, 20)}...');

      if (token != null && token.isNotEmpty) {
        LoggingService.debug('🔑 HttpApiService: Adding Authorization header');
        print('🔍 HttpApiService._getAuthHeaders: Returning auth headers with Bearer $token');
        return {'Authorization': 'Bearer $token'};
      } else {
        LoggingService.debug('🔑 HttpApiService: No valid token available');
        print('🔍 HttpApiService._getAuthHeaders: No valid token, returning empty headers');
        return {};
      }
    } catch (e) {
      LoggingService.warning('🔑 HttpApiService: Failed to get auth token: $e');
      print('🔍 HttpApiService._getAuthHeaders: ERROR: $e');
      return {};
    }
  }

  /// Make a GET request
  Future<T> get<T>(
    String path, {
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
    Map<String, String>? queryParameters,
  }) async {
    await _ensureInitialized();
    final authHeaders = await _getAuthHeaders();

    // Apply route normalisation
    final normalizedPath = _normalizePath(path);
    
    // Build URL with query parameters
    String fullUrl = '$baseUrl$_apiPrefix$normalizedPath';
    if (queryParameters != null && queryParameters.isNotEmpty) {
      final uri = Uri.parse(fullUrl);
      final updatedUri = uri.replace(queryParameters: queryParameters);
      fullUrl = updatedUri.toString();
    }
    
    print('🔍 HttpApiService.get: baseUrl=$baseUrl, _apiPrefix=$_apiPrefix, path=$path, fullUrl=$fullUrl');
    print('🔍 HttpApiService.get: authHeaders=$authHeaders');
    final response = await _httpClient!.get(
      fullUrl,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...authHeaders,
        ...?headers,
      },
    );

    print('🔍 HttpApiService.get: response.statusCode=${response.statusCode}, response.body=${response.body}');
    return _handleResponse<T>(response, fromJson);
  }

  /// Make a POST request
  Future<T> post<T>(
    String path, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    await _ensureInitialized();
    final authHeaders = await _getAuthHeaders();
    final requestBody = body != null ? jsonEncode(body) : null;

    // Apply route normalisation
    final normalizedPath = _normalizePath(path);
    print('🔍 HttpApiService.post: path=$normalizedPath, body=$requestBody');
    final response = await _httpClient!.post(
      '$baseUrl$_apiPrefix$normalizedPath',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...authHeaders,
        ...?headers,
      },
      body: requestBody,
    );

    print('🔍 HttpApiService.post: response.statusCode=${response.statusCode}, response.body=${response.body}');
    return _handleResponse<T>(response, fromJson);
  }

  /// Make a PUT request
  Future<T> put<T>(
    String path, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    await _ensureInitialized();
    final authHeaders = await _getAuthHeaders();

    final normalizedPath = _normalizePath(path);
    final response = await _httpClient!.request(
      'PUT',
      '$baseUrl$_apiPrefix$normalizedPath',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...authHeaders,
        ...?headers,
      },
      body: body,
    );

    return _handleResponse<T>(response, fromJson);
  }

  /// Make a DELETE request
  Future<T> delete<T>(
    String path, {
    Map<String, String>? headers,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    await _ensureInitialized();
    final authHeaders = await _getAuthHeaders();

    final normalizedPath = _normalizePath(path);
    final response = await _httpClient!.request(
      'DELETE',
      '$baseUrl$_apiPrefix$normalizedPath',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...authHeaders,
        ...?headers,
      },
    );

    return _handleResponse<T>(response, fromJson);
  }

  /// Handle HTTP response and convert to typed result
  T _handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) {
    print('🔍 HttpApiService._handleResponse: statusCode=${response.statusCode}, T=${T.toString()}');
    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (T.toString() == 'void') {
        print('🔍 HttpApiService._handleResponse: returning null for void type');
        return null as T;
      }

      if (response.body.isEmpty) {
        print('🔍 HttpApiService._handleResponse: response body is empty, returning empty map');
        return {} as T;
      }

      print('🔍 HttpApiService._handleResponse: parsing JSON from response body');
      final data = jsonDecode(response.body);
      print('🔍 HttpApiService._handleResponse: parsed data=$data, type=${data.runtimeType}');

      if (fromJson != null && data is Map<String, dynamic>) {
        print('🔍 HttpApiService._handleResponse: using fromJson to convert data');
        return fromJson(data);
      }

      print('🔍 HttpApiService._handleResponse: returning data as T');
      return data as T;
    } else {
      String errorMessage = 'HTTP ${response.statusCode}: ${response.reasonPhrase}';

      if (response.body.isNotEmpty) {
        try {
          final dynamic decoded = jsonDecode(response.body);
          if (decoded is Map<String, dynamic>) {
            // Prefer "error" field, then "message"
            if (decoded.containsKey('error')) {
              errorMessage = decoded['error'].toString();
            } else if (decoded.containsKey('message')) {
              errorMessage = decoded['message'].toString();
            }
          }
        } catch (_) {
          // Ignore JSON parse errors and fall back to default message
        }
      }

      throw core_exceptions.ServerException(message: errorMessage);
    }
  }

  // ==========================================================================
  // Authentication Methods - REMOVED: Using OryAuthService exclusively
  // ==========================================================================
  // All authentication is handled by OryAuthService via Ory Kratos

  // ==========================================================================
  // User Profile Methods
  // ==========================================================================

  Future<ApiUserProfile> getCurrentUserProfile() async {
    return get<ApiUserProfile>(
      '/auth/profile',
      fromJson: ApiUserProfile.fromJson,
    );
  }

  Future<ApiUserProfile> getUserProfile(String userId) async {
    return get<ApiUserProfile>(
      '/users/$userId',
      fromJson: ApiUserProfile.fromJson,
    );
  }

  Future<void> updateUserProfile(UpdateUserProfileRequest request) async {
    await put<Map<String, dynamic>>(
      '/auth/profile',
      body: request.toJson(),
      fromJson: (json) => json as Map<String, dynamic>,
    );
    // Backend returns {"message": "Profile updated successfully"}
    // We don't need to return the user profile, just confirm success
  }

  // ==========================================================================
  // Bubble Methods
  // ==========================================================================

  Future<List<ApiBubble>> getUserBubbles() async {
    final response = await get<List<dynamic>>('/bubbles');
    return response.map((json) => ApiBubble.fromJson(json as Map<String, dynamic>)).toList();
  }

  Future<ApiBubble> createBubble(CreateBubbleRequest request) async {
    return post<ApiBubble>(
      '/bubbles',
      body: request.toJson(),
      fromJson: ApiBubble.fromJson,
    );
  }

  Future<ApiBubble> getBubble(String bubbleId) async {
    return get<ApiBubble>(
      '/bubbles/$bubbleId',
      fromJson: ApiBubble.fromJson,
    );
  }

  Future<void> joinBubble(String bubbleId) async {
    return post<void>('/bubbles/$bubbleId/join');
  }

  Future<void> leaveBubble(String bubbleId) async {
    return delete<void>('/bubbles/$bubbleId/leave');
  }

  Future<void> deleteBubble(String bubbleId) async {
    return delete<void>('/bubbles/$bubbleId');
  }

  // ==========================================================================
  // Contact Methods
  // ==========================================================================

  Future<List<ApiContact>> getContacts() async {
    final response = await get<Map<String, dynamic>>('/contact/contacts');
    final contactsData = response['contacts'] as List<dynamic>? ?? [];
    return contactsData.map((json) => ApiContact.fromJson(json as Map<String, dynamic>)).toList();
  }

  Future<void> sendContactRequest(SendContactRequestRequest request) async {
    return post<void>(
      '/contact/requests',
      body: request.toJson(),
    );
  }

  Future<void> acceptContactRequest(AcceptContactRequestRequest request) async {
    return post<void>(
      '/contact/requests/${request.contactRequestId}/accept',
    );
  }



  Future<void> removeContact(String contactId) async {
    return delete<void>('/contact/$contactId');
  }

  Future<List<ApiContact>> getPendingContactRequests() async {
    final response = await get<List<dynamic>>('/contact/requests/received');
    return response.map((json) => ApiContact.fromJson(json as Map<String, dynamic>)).toList();
  }

  Future<List<dynamic>> getSentContactRequests() async {
    return get<List<dynamic>>('/contact/requests/sent');
  }



  Future<List<ApiUserProfile>> getMutualContacts(String userId) async {
    final response = await get<List<dynamic>>('/contact/mutual/$userId');
    return response.map((json) => ApiUserProfile.fromJson(json as Map<String, dynamic>)).toList();
  }

  // ==========================================================================
  // Additional Bubble Methods
  // ==========================================================================

  Future<List<ApiUserProfile>> getBubbleMembers(String bubbleId) async {
    final response = await get<List<dynamic>>('/bubbles/$bubbleId/members');
    return response.map((json) => ApiUserProfile.fromJson(json as Map<String, dynamic>)).toList();
  }

  Future<void> inviteToBubble(String bubbleId, Map<String, String> inviteData) async {
    return post<void>('/bubbles/$bubbleId/invite', body: inviteData);
  }

  Future<void> voteOnBubbleMember(String bubbleId, Map<String, dynamic> voteData) async {
    return post<void>('/bubbles/$bubbleId/vote', body: voteData);
  }

  // ==========================================================================
  // Additional User Methods
  // ==========================================================================

  Future<List<ApiUserProfile>> searchUsers({
    String query = '',
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await get<Map<String, dynamic>>('/users/search', queryParameters: {
      'q': query.isEmpty ? 'a' : query, // Use 'a' as broad search query to get all users
      'page': page.toString(),
      'page_size': pageSize.toString(),
    });
    
    final usersData = response['users'] as List<dynamic>? ?? [];
    return usersData.map((json) => ApiUserProfile.fromJson(json as Map<String, dynamic>)).toList();
  }

  // Keep the old getUsers method for backward compatibility but use searchUsers internally
  Future<PaginatedUserResponse> getUsers({
    int limit = 20,
    int offset = 0,
  }) async {
    final page = (offset ~/ limit) + 1;
    final users = await searchUsers(query: '', page: page, pageSize: limit);
    
    return PaginatedUserResponse(
      data: users,
      page: page,
      perPage: limit,
      total: users.length,
      totalPages: 1,
    );
  }

  Future<void> blockContact(String contactId) async {
    return post<void>('/contacts/$contactId/block');
  }

  // ==========================================================================
  // Additional Auth Methods - REMOVED: Using OryAuthService exclusively
  // ==========================================================================
  // All authentication is handled by OryAuthService via Ory Kratos

  // ==========================================================================
  // Health Check
  // ==========================================================================

  Future<HealthCheckResponse> healthCheck() async {
    return get<HealthCheckResponse>(
      '/health',
      fromJson: HealthCheckResponse.fromJson,
    );
  }
}
