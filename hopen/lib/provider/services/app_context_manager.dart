import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../statefulbusinesslogic/core/services/dialog_service.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../di/injection_container_refactored.dart' as di;
import 'real_time_notification_service.dart';
import 'real_time_service_manager.dart';
import 'fcm/fcm_service.dart';

/// Manages app-wide context for services that need access to the current BuildContext
/// Handles proper initialization and cleanup of context-dependent services
class AppContextManager extends StatefulWidget {
  final Widget child;

  const AppContextManager({
    super.key,
    required this.child,
  });

  @override
  State<AppContextManager> createState() => _AppContextManagerState();
}

class _AppContextManagerState extends State<AppContextManager> with WidgetsBindingObserver {
  DialogService? _dialogService;
  RealTimeServiceManager? _realTimeServiceManager;
  FcmService? _fcmService;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // Initialize services after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeServices();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cleanupServices();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        _onAppResumed();
        break;
      case AppLifecycleState.paused:
        _onAppPaused();
        break;
      case AppLifecycleState.detached:
        _onAppDetached();
        break;
      case AppLifecycleState.inactive:
        // App is transitioning between states
        break;
      case AppLifecycleState.hidden:
        // App is hidden but still running
        break;
    }
  }

  /// Initialize context-dependent services
  Future<void> _initializeServices() async {
    if (_isInitialized) return;
    
    try {
      LoggingService.info('AppContextManager: Initializing context-dependent services');
      
      // Get services from dependency injection
      _dialogService = di.sl<DialogService>();
      
      try {
        _realTimeServiceManager = di.sl<RealTimeServiceManager>();
      } catch (e) {
        LoggingService.warning('AppContextManager: RealTimeServiceManager not available: $e');
      }
      
      try {
        _fcmService = FcmService();
      } catch (e) {
        LoggingService.warning('AppContextManager: FCM Service not available: $e');
      }
      
      // Set context for dialog service
      if (_dialogService != null && mounted) {
        (_dialogService as dynamic).setContext(context);
        LoggingService.info('AppContextManager: Dialog service context set');
      }
      
      // Initialize real-time service manager
      if (_realTimeServiceManager != null) {
        await _realTimeServiceManager!.initialize();
        LoggingService.info('AppContextManager: Real-time service manager initialized');
      }
      
      // Initialize FCM service
      if (_fcmService != null) {
        await _initializeFCMService();
      }
      
      _isInitialized = true;
      LoggingService.success('AppContextManager: All services initialized successfully');
      
    } catch (e, stackTrace) {
      LoggingService.error('AppContextManager: Error initializing services: $e', stackTrace: stackTrace);
    }
  }

  /// Initialize FCM service
  Future<void> _initializeFCMService() async {
    try {
      if (!_fcmService!.isInitialized) {
        await _fcmService!.initialize();
        LoggingService.info('AppContextManager: FCM service initialized');
      }
    } catch (e) {
      LoggingService.error('AppContextManager: Error initializing FCM service: $e');
    }
  }

  /// Handle app resumed state
  void _onAppResumed() {
    LoggingService.info('AppContextManager: App resumed');
    
    // Update dialog service context if needed
    if (_dialogService != null && mounted) {
      (_dialogService as dynamic).setContext(context);
    }
    
    // Real-time service manager handles reconnection internally
  }

  /// Handle app paused state
  void _onAppPaused() {
    LoggingService.info('AppContextManager: App paused');
    
    // Services will handle background state internally
    // No need to clear context as dialogs shouldn't show when paused
  }

  /// Handle app detached state
  void _onAppDetached() {
    LoggingService.info('AppContextManager: App detached');
    _cleanupServices();
  }

  /// Cleanup services when app is closing
  void _cleanupServices() {
    try {
      LoggingService.info('AppContextManager: Cleaning up services');
      
      // Clear dialog service context
      if (_dialogService != null) {
        (_dialogService as dynamic).clearContext();
      }
      
      // Dispose real-time service manager
      if (_realTimeServiceManager != null) {
        _realTimeServiceManager!.dispose();
      }
      
      _isInitialized = false;
      LoggingService.info('AppContextManager: Services cleaned up');
      
    } catch (e) {
      LoggingService.error('AppContextManager: Error cleaning up services: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Widget that provides app context management
/// Wrap your app's main content with this widget
class AppContextProvider extends StatelessWidget {
  final Widget child;

  const AppContextProvider({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return AppContextManager(
      child: child,
    );
  }
}
