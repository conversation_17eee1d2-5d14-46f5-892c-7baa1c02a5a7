import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:path/path.dart' as path;

/// Service for handling AVIF image processing using FFmpeg
/// This service provides AVIF encoding capabilities using FFmpeg
class AvifProcessingService {
  /// Check if AVIF encoding is supported (FFmpeg always supports AVIF)
  static Future<bool> isAvifSupported() async {
    try {
      // FFmpeg with libavif support should always be available
      return true;
    } catch (e) {
      debugPrint('AVIF support check failed: $e');
      return false;
    }
  }

  /// Encode image file to AVIF format using FFmpeg
  /// Returns null if AVIF encoding fails
  static Future<Uint8List?> encodeToAvif({
    required String inputPath,
    int quality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      if (!await isAvifSupported()) {
        return null;
      }

      final tempDir = Directory.systemTemp;
      final outputPath = path.join(tempDir.path, 'output_${DateTime.now().millisecondsSinceEpoch}.avif');

      // Build FFmpeg command for AVIF encoding
      String command = '-i "$inputPath"';

      // Add scaling if dimensions are specified
      // For profile pictures, we want to resize to exact dimensions (1440x1440)
      // while maintaining aspect ratio and cropping to square if needed
      if (maxWidth != null && maxHeight != null) {
        // Use scale and crop to ensure exact square output
        // This will scale the image to fit within the bounds, then crop to exact size
        command += ' -vf "scale=$maxWidth:$maxHeight:force_original_aspect_ratio=increase,crop=$maxWidth:$maxHeight"';
      }

      // AVIF encoding parameters
      command += ' -c:v libaom-av1 -crf ${_qualityToCrf(quality)} -b:v 0 -pix_fmt yuv420p';
      command += ' -f avif "$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        final outputFile = File(outputPath);
        if (await outputFile.exists()) {
          final bytes = await outputFile.readAsBytes();
          await outputFile.delete(); // Clean up temp file
          return bytes;
        }
      }

      return null;
    } catch (e) {
      debugPrint('AVIF encoding failed: $e');
      return null;
    }
  }

  /// Convert quality percentage to CRF value for AV1 (optimized for 2MB target)
  static int _qualityToCrf(int quality) {
    // CRF range for AV1: 0-63 (lower = better quality)
    // Optimized for 2MB profile pictures:
    // Quality 100% = CRF 18 (excellent quality, ~1.2MB)
    // Quality 80% = CRF 23 (very good quality, ~800KB)
    // Quality 60% = CRF 28 (good quality, ~500KB)
    // Quality 40% = CRF 33 (acceptable quality, ~300KB)
    return (40 - (quality * 22 / 100)).round().clamp(18, 40);
  }

  /// Encode image to AVIF with optimized single-pass processing
  static Future<Uint8List?> encodeToAvifWithTargetSize({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final stopwatch = Stopwatch()..start();
    print('⏱️ Starting AVIF encoding with quality $initialQuality');

    if (!await isAvifSupported()) {
      print('❌ AVIF not supported');
      return null;
    }

    // For 2MB target, use optimized quality based on image size
    int targetQuality = initialQuality;

    // Estimate optimal quality based on target size (performance optimization)
    if (maxFileSizeBytes >= 2 * 1024 * 1024) { // 2MB+
      targetQuality = 85; // High quality for large targets
    } else if (maxFileSizeBytes >= 1 * 1024 * 1024) { // 1MB+
      targetQuality = 75; // Medium quality
    } else {
      targetQuality = 65; // Lower quality for small targets
    }

    print('🎯 Using optimized quality: $targetQuality for target size: ${(maxFileSizeBytes / 1024 / 1024).toStringAsFixed(1)}MB');

    // Single-pass encoding with optimized quality
    final result = await encodeToAvif(
      inputPath: inputPath,
      quality: targetQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    stopwatch.stop();
    print('⏱️ AVIF encoding completed in ${stopwatch.elapsedMilliseconds}ms');

    if (result != null) {
      final resultSizeMB = (result.length / 1024 / 1024);
      print('📊 AVIF result: ${resultSizeMB.toStringAsFixed(2)}MB (target: ${(maxFileSizeBytes / 1024 / 1024).toStringAsFixed(1)}MB)');

      // Accept result if within reasonable range (allow 10% over target for performance)
      if (result.length <= maxFileSizeBytes * 1.1) {
        return result;
      }

      // Only try one fallback if significantly over target
      if (result.length > maxFileSizeBytes * 1.5) {
        print('🔄 Result too large, trying fallback quality 60');
        final fallbackResult = await encodeToAvif(
          inputPath: inputPath,
          quality: 60,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
        );
        return fallbackResult;
      }
    }

    return result;
  }

  /// Fast AVIF encoding with mobile-optimized settings
  static Future<Uint8List?> fastEncodeToAvif({
    required String inputPath,
    required int maxFileSizeBytes,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final stopwatch = Stopwatch()..start();
    print('⚡ Starting fast AVIF encoding');

    if (!await isAvifSupported()) {
      return null;
    }

    // Mobile-optimized quality based on research
    int quality = 75; // Sweet spot for mobile: good quality, fast encoding

    // For profile pictures, use fixed 1440x1440 for consistency
    final targetWidth = maxWidth ?? 1440;
    final targetHeight = maxHeight ?? 1440;

    print('🎯 Fast encoding: quality=$quality, size=${targetWidth}x${targetHeight}');

    final result = await encodeToAvif(
      inputPath: inputPath,
      quality: quality,
      maxWidth: targetWidth,
      maxHeight: targetHeight,
    );

    stopwatch.stop();
    print('⚡ Fast AVIF encoding completed in ${stopwatch.elapsedMilliseconds}ms');

    return result; // Return last attempt even if it exceeds size limit
  }

  /// Get optimal quality setting for target file size using binary search
  static Future<int> getOptimalQuality({
    required String inputPath,
    required int targetFileSizeBytes,
    int? maxWidth,
    int? maxHeight,
  }) async {
    if (!await isAvifSupported()) {
      return 80; // Default quality
    }

    // Binary search for optimal quality (optimized for 2MB target)
    int minQuality = 60;  // Don't go below 60% for 2MB target
    int maxQuality = 95;
    int optimalQuality = 80;

    while (minQuality <= maxQuality) {
      final int testQuality = (minQuality + maxQuality) ~/ 2;

      final Uint8List? result = await encodeToAvif(
        inputPath: inputPath,
        quality: testQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (result == null) break;

      if (result.length <= targetFileSizeBytes) {
        optimalQuality = testQuality;
        minQuality = testQuality + 1;
      } else {
        maxQuality = testQuality - 1;
      }
    }

    return optimalQuality;
  }

  /// Convert image to AVIF (mobile-optimized, no fallbacks needed)
  static Future<Uint8List?> convertToAvif({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    // Use fast encoding for better performance (profile pictures)
    if (maxFileSizeBytes >= 2 * 1024 * 1024) { // 2MB+ targets
      return await fastEncodeToAvif(
        inputPath: inputPath,
        maxFileSizeBytes: maxFileSizeBytes,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );
    }

    // Use optimized encoding for smaller targets
    return await encodeToAvifWithTargetSize(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );
  }

  // Removed WebP and JPEG fallback methods - AVIF-only for mobile apps
}

/// Result of AVIF processing operation
class AvifProcessingResult {
  const AvifProcessingResult._({
    required this.isSuccess,
    this.avifData,
    this.fallbackData,
    this.format,
    this.quality,
    this.originalSize,
    this.compressedSize,
    this.errorMessage,
  });

  factory AvifProcessingResult.success({
    required Uint8List data,
    required String format,
    required int quality,
    required int originalSize,
    required int compressedSize,
  }) {
    return AvifProcessingResult._(
      isSuccess: true,
      avifData: format == 'avif' ? data : null,
      fallbackData: format != 'avif' ? data : null,
      format: format,
      quality: quality,
      originalSize: originalSize,
      compressedSize: compressedSize,
    );
  }

  factory AvifProcessingResult.error(String errorMessage) {
    return AvifProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? avifData;
  final Uint8List? fallbackData;
  final String? format;
  final int? quality;
  final int? originalSize;
  final int? compressedSize;
  final String? errorMessage;

  /// Get the best available data (AVIF preferred, fallback if needed)
  Uint8List? get bestData => avifData ?? fallbackData;

  /// Get compression ratio (0.0 to 1.0)
  double get compressionRatio {
    if (originalSize == null || compressedSize == null || originalSize == 0) {
      return 0.0;
    }
    return (originalSize! - compressedSize!) / originalSize!;
  }

  /// Get file size reduction percentage
  double get sizeReductionPercentage => compressionRatio * 100;
}

/// AVIF-only image processing for mobile apps
class EnhancedImageProcessor {
  /// Process image with AVIF format (mobile-optimized)
  static Future<AvifProcessingResult> processImage({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final inputFile = File(inputPath);
    final originalSize = await inputFile.length();

    // AVIF-only processing for mobile apps
    final avifData = await AvifProcessingService.convertToAvif(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    if (avifData != null) {
      return AvifProcessingResult.success(
        data: avifData,
        format: 'avif',
        quality: initialQuality,
        originalSize: originalSize,
        compressedSize: avifData.length,
      );
    }

    return AvifProcessingResult.error(
      'AVIF processing failed'
    );
  }

  /// Get supported formats for mobile apps (AVIF-only)
  static Future<List<String>> getSupportedFormats() async {
    // Mobile apps only need AVIF with FFmpeg support
    return ['avif'];
  }
}
