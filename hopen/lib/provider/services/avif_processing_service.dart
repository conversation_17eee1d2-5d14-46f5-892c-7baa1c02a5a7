import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:path/path.dart' as path;

/// Service for handling AVIF image processing using FFmpeg
/// This service provides AVIF encoding capabilities using FFmpeg
class AvifProcessingService {
  /// Check if AVIF encoding is supported (FFmpeg always supports AVIF)
  static Future<bool> isAvifSupported() async {
    try {
      // FFmpeg with libavif support should always be available
      return true;
    } catch (e) {
      debugPrint('AVIF support check failed: $e');
      return false;
    }
  }

  /// Encode image file to AVIF format using FFmpeg
  /// Returns null if AVIF encoding fails
  static Future<Uint8List?> encodeToAvif({
    required String inputPath,
    int quality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      if (!await isAvifSupported()) {
        return null;
      }

      final tempDir = Directory.systemTemp;
      final outputPath = path.join(tempDir.path, 'output_${DateTime.now().millisecondsSinceEpoch}.avif');

      // Build FFmpeg command for AVIF encoding
      String command = '-i "$inputPath"';

      // Add scaling if dimensions are specified
      // For profile pictures, we want to resize to exact dimensions (1440x1440)
      // while maintaining aspect ratio and cropping to square if needed
      if (maxWidth != null && maxHeight != null) {
        // Use scale and crop to ensure exact square output
        // This will scale the image to fit within the bounds, then crop to exact size
        command += ' -vf "scale=$maxWidth:$maxHeight:force_original_aspect_ratio=increase,crop=$maxWidth:$maxHeight"';
      }

      // AVIF encoding parameters
      command += ' -c:v libaom-av1 -crf ${_qualityToCrf(quality)} -b:v 0 -pix_fmt yuv420p';
      command += ' -f avif "$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        final outputFile = File(outputPath);
        if (await outputFile.exists()) {
          final bytes = await outputFile.readAsBytes();
          await outputFile.delete(); // Clean up temp file
          return bytes;
        }
      }

      return null;
    } catch (e) {
      debugPrint('AVIF encoding failed: $e');
      return null;
    }
  }

  /// Convert quality percentage to CRF value for AV1 (optimized for 2MB target)
  static int _qualityToCrf(int quality) {
    // CRF range for AV1: 0-63 (lower = better quality)
    // Optimized for 2MB profile pictures:
    // Quality 100% = CRF 18 (excellent quality, ~1.2MB)
    // Quality 80% = CRF 23 (very good quality, ~800KB)
    // Quality 60% = CRF 28 (good quality, ~500KB)
    // Quality 40% = CRF 33 (acceptable quality, ~300KB)
    return (40 - (quality * 22 / 100)).round().clamp(18, 40);
  }

  /// Encode image to AVIF with size-based compression strategy (single-pass)
  /// Includes robust validation that handles progressive JPEG and other formats
  static Future<Uint8List?> encodeToAvifWithTargetSize({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final stopwatch = Stopwatch()..start();

    if (!await isAvifSupported()) {
      print('❌ AVIF not supported');
      return null;
    }

    // Get original file size to determine compression strategy
    final originalFile = File(inputPath);
    final originalSizeBytes = await originalFile.length();
    final originalSizeMB = originalSizeBytes / 1024 / 1024;

    print('📁 Original image: ${originalSizeMB.toStringAsFixed(2)}MB');
    print('🎯 Target size: ${(maxFileSizeBytes / 1024 / 1024).toStringAsFixed(1)}MB');

    // Validate image using FFmpeg (handles progressive JPEG and all formats)
    print('🔍 Validating image format and dimensions using FFmpeg...');
    final validationResult = await _validateImageWithFFmpeg(inputPath);
    if (!validationResult.isValid) {
      print('❌ FFmpeg validation failed: ${validationResult.error}');
      return null;
    }
    print('✅ FFmpeg validation passed: ${validationResult.dimensions}');

    // Determine compression quality based on original file size
    int targetQuality;
    bool needsCompression = false;

    if (originalSizeBytes < 1 * 1024 * 1024) { // < 1MB
      targetQuality = 100; // NO COMPRESSION - 100% quality
      needsCompression = false;
      print('✨ Image < 1MB: NO COMPRESSION (100% quality)');
    } else if (originalSizeBytes <= 2 * 1024 * 1024) { // 1MB - 2MB
      targetQuality = 90; // 90% quality compression
      needsCompression = true;
      print('🔧 Image 1-2MB: Using 90% quality compression');
    } else { // > 2MB
      targetQuality = 75; // 75% quality compression (or lower if needed)
      needsCompression = true;
      print('🔧 Image > 2MB: Using 75% quality compression');
    }

    // Ultra-fast processing for < 1MB images (no compression needed)
    if (!needsCompression) {
      print('⚡ Ultra-fast mode: Converting to AVIF without compression');
    }

    // Single-pass encoding with determined quality
    final result = await encodeToAvif(
      inputPath: inputPath,
      quality: targetQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    stopwatch.stop();
    print('⏱️ AVIF encoding completed in ${stopwatch.elapsedMilliseconds}ms');

    if (result != null) {
      final resultSizeMB = (result.length / 1024 / 1024);
      final compressionRatio = ((originalSizeBytes - result.length) / originalSizeBytes * 100);

      print('📊 AVIF result: ${resultSizeMB.toStringAsFixed(2)}MB');
      print('📉 Compression: ${compressionRatio.toStringAsFixed(1)}% size reduction');

      // For images > 2MB, if still too large, try one fallback with lower quality
      if (needsCompression && originalSizeBytes > 2 * 1024 * 1024 && result.length > maxFileSizeBytes) {
        print('🔄 Result still too large for >2MB image, trying 60% quality fallback');
        final fallbackResult = await encodeToAvif(
          inputPath: inputPath,
          quality: 60,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
        );

        if (fallbackResult != null) {
          final fallbackSizeMB = (fallbackResult.length / 1024 / 1024);
          print('📊 Fallback result: ${fallbackSizeMB.toStringAsFixed(2)}MB');
          return fallbackResult;
        }
      }

      return result;
    }

    return result;
  }

  /// Fast AVIF encoding with size-based compression strategy
  static Future<Uint8List?> fastEncodeToAvif({
    required String inputPath,
    required int maxFileSizeBytes,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final stopwatch = Stopwatch()..start();
    print('⚡ Starting fast AVIF encoding');

    if (!await isAvifSupported()) {
      return null;
    }

    // Get original file size to determine compression strategy
    final originalFile = File(inputPath);
    final originalSizeBytes = await originalFile.length();
    final originalSizeMB = originalSizeBytes / 1024 / 1024;

    // Apply same compression strategy as main encoding
    int quality;
    if (originalSizeBytes < 1 * 1024 * 1024) { // < 1MB
      quality = 100; // NO COMPRESSION - 100% quality
      print('⚡ Fast encoding < 1MB: NO COMPRESSION (100% quality)');
    } else if (originalSizeBytes <= 2 * 1024 * 1024) { // 1MB - 2MB
      quality = 90; // 90% quality compression
      print('⚡ Fast encoding 1-2MB: 90% quality');
    } else { // > 2MB
      quality = 75; // 75% quality compression
      print('⚡ Fast encoding > 2MB: 75% quality');
    }

    // For profile pictures, use fixed 1440x1440 for consistency
    final targetWidth = maxWidth ?? 1440;
    final targetHeight = maxHeight ?? 1440;

    print('🎯 Fast encoding: ${originalSizeMB.toStringAsFixed(2)}MB → quality=$quality, size=${targetWidth}x${targetHeight}');

    final result = await encodeToAvif(
      inputPath: inputPath,
      quality: quality,
      maxWidth: targetWidth,
      maxHeight: targetHeight,
    );

    stopwatch.stop();
    print('⚡ Fast AVIF encoding completed in ${stopwatch.elapsedMilliseconds}ms');

    return result;
  }

  /// Convert image to AVIF with size-based compression strategy (single-pass)
  static Future<Uint8List?> convertToAvif({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    // Always use the size-based encoding strategy for consistent behavior
    return await encodeToAvifWithTargetSize(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );
  }

  /// Validate image using FFmpeg (handles all formats including progressive JPEG)
  static Future<_FFmpegValidationResult> _validateImageWithFFmpeg(String inputPath) async {
    try {
      // Use FFmpeg to get image information (handles all formats)
      final session = await FFmpegKit.execute('-i "$inputPath" -f null -');
      final returnCode = await session.getReturnCode();
      final output = await session.getOutput();

      if (returnCode != null && !ReturnCode.isSuccess(returnCode)) {
        return _FFmpegValidationResult.error('Invalid image format or corrupted file');
      }

      // Parse dimensions from FFmpeg output
      final dimensionRegex = RegExp(r'(\d+)x(\d+)');
      final match = dimensionRegex.firstMatch(output ?? '');

      if (match != null) {
        final width = int.parse(match.group(1)!);
        final height = int.parse(match.group(2)!);
        final minDimension = width < height ? width : height;

        // Check minimum resolution requirement (640x640)
        if (minDimension < 640) {
          return _FFmpegValidationResult.error(
            'Image resolution too small. Minimum: 640x640px, found: ${width}x${height}'
          );
        }

        return _FFmpegValidationResult.success('${width}x${height}');
      } else {
        return _FFmpegValidationResult.error('Could not determine image dimensions');
      }
    } catch (e) {
      return _FFmpegValidationResult.error('FFmpeg validation failed: $e');
    }
  }
}

/// Result class for FFmpeg validation
class _FFmpegValidationResult {
  final bool isValid;
  final String? error;
  final String? dimensions;

  _FFmpegValidationResult.success(this.dimensions) : isValid = true, error = null;
  _FFmpegValidationResult.error(this.error) : isValid = false, dimensions = null;
}

/// Result of AVIF processing operation
class AvifProcessingResult {
  const AvifProcessingResult._({
    required this.isSuccess,
    this.avifData,
    this.fallbackData,
    this.format,
    this.quality,
    this.originalSize,
    this.compressedSize,
    this.errorMessage,
  });

  factory AvifProcessingResult.success({
    required Uint8List data,
    required String format,
    required int quality,
    required int originalSize,
    required int compressedSize,
  }) {
    return AvifProcessingResult._(
      isSuccess: true,
      avifData: format == 'avif' ? data : null,
      fallbackData: format != 'avif' ? data : null,
      format: format,
      quality: quality,
      originalSize: originalSize,
      compressedSize: compressedSize,
    );
  }

  factory AvifProcessingResult.error(String errorMessage) {
    return AvifProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? avifData;
  final Uint8List? fallbackData;
  final String? format;
  final int? quality;
  final int? originalSize;
  final int? compressedSize;
  final String? errorMessage;

  /// Get the best available data (AVIF preferred, fallback if needed)
  Uint8List? get bestData => avifData ?? fallbackData;

  /// Get compression ratio (0.0 to 1.0)
  double get compressionRatio {
    if (originalSize == null || compressedSize == null || originalSize == 0) {
      return 0.0;
    }
    return (originalSize! - compressedSize!) / originalSize!;
  }

  /// Get file size reduction percentage
  double get sizeReductionPercentage => compressionRatio * 100;
}

/// AVIF-only image processing for mobile apps
class EnhancedImageProcessor {
  /// Process image with AVIF format (mobile-optimized)
  static Future<AvifProcessingResult> processImage({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final inputFile = File(inputPath);
    final originalSize = await inputFile.length();

    // AVIF-only processing for mobile apps
    final avifData = await AvifProcessingService.convertToAvif(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    if (avifData != null) {
      return AvifProcessingResult.success(
        data: avifData,
        format: 'avif',
        quality: initialQuality,
        originalSize: originalSize,
        compressedSize: avifData.length,
      );
    }

    return AvifProcessingResult.error(
      'AVIF processing failed'
    );
  }

  /// Get supported formats for mobile apps (AVIF-only)
  static Future<List<String>> getSupportedFormats() async {
    // Mobile apps only need AVIF with FFmpeg support
    return ['avif'];
  }
}
