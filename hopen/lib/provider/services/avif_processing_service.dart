import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:path/path.dart' as path;

/// Service for handling AVIF image processing using FFmpeg
/// This service provides AVIF encoding capabilities using FFmpeg
class AvifProcessingService {
  /// Check if AVIF encoding is supported (FFmpeg always supports AVIF)
  static Future<bool> isAvifSupported() async {
    try {
      // FFmpeg with libavif support should always be available
      return true;
    } catch (e) {
      debugPrint('AVIF support check failed: $e');
      return false;
    }
  }

  /// Encode image file to AVIF format using FFmpeg
  /// Returns null if AVIF encoding fails
  static Future<Uint8List?> encodeToAvif({
    required String inputPath,
    int quality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      if (!await isAvifSupported()) {
        return null;
      }

      final tempDir = Directory.systemTemp;
      final outputPath = path.join(tempDir.path, 'output_${DateTime.now().millisecondsSinceEpoch}.avif');

      // Build FFmpeg command for AVIF encoding
      String command = '-i "$inputPath"';

      // Add scaling if dimensions are specified
      if (maxWidth != null && maxHeight != null) {
        command += ' -vf "scale=$maxWidth:$maxHeight:force_original_aspect_ratio=decrease"';
      }

      // AVIF encoding parameters
      command += ' -c:v libaom-av1 -crf ${_qualityToCrf(quality)} -b:v 0 -pix_fmt yuv420p';
      command += ' -f avif "$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        final outputFile = File(outputPath);
        if (await outputFile.exists()) {
          final bytes = await outputFile.readAsBytes();
          await outputFile.delete(); // Clean up temp file
          return bytes;
        }
      }

      return null;
    } catch (e) {
      debugPrint('AVIF encoding failed: $e');
      return null;
    }
  }

  /// Convert quality percentage to CRF value for AV1 (optimized for 2MB target)
  static int _qualityToCrf(int quality) {
    // CRF range for AV1: 0-63 (lower = better quality)
    // Optimized for 2MB profile pictures:
    // Quality 100% = CRF 18 (excellent quality, ~1.2MB)
    // Quality 80% = CRF 23 (very good quality, ~800KB)
    // Quality 60% = CRF 28 (good quality, ~500KB)
    // Quality 40% = CRF 33 (acceptable quality, ~300KB)
    return (40 - (quality * 22 / 100)).round().clamp(18, 40);
  }

  /// Encode image to AVIF with multiple quality attempts to meet size target
  static Future<Uint8List?> encodeToAvifWithTargetSize({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    if (!await isAvifSupported()) {
      return null;
    }

    int quality = initialQuality;
    Uint8List? result;

    // Try different quality levels to meet size target (optimized for 2MB)
    while (quality >= 60) {  // Don't go below 60% quality for 2MB target
      result = await encodeToAvif(
        inputPath: inputPath,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (result != null && result.length <= maxFileSizeBytes) {
        return result;
      }

      quality -= 5;  // Smaller steps for fine-tuning
    }

    return result; // Return last attempt even if it exceeds size limit
  }

  /// Get optimal quality setting for target file size using binary search
  static Future<int> getOptimalQuality({
    required String inputPath,
    required int targetFileSizeBytes,
    int? maxWidth,
    int? maxHeight,
  }) async {
    if (!await isAvifSupported()) {
      return 80; // Default quality
    }

    // Binary search for optimal quality (optimized for 2MB target)
    int minQuality = 60;  // Don't go below 60% for 2MB target
    int maxQuality = 95;
    int optimalQuality = 80;

    while (minQuality <= maxQuality) {
      final int testQuality = (minQuality + maxQuality) ~/ 2;

      final Uint8List? result = await encodeToAvif(
        inputPath: inputPath,
        quality: testQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (result == null) break;

      if (result.length <= targetFileSizeBytes) {
        optimalQuality = testQuality;
        minQuality = testQuality + 1;
      } else {
        maxQuality = testQuality - 1;
      }
    }

    return optimalQuality;
  }

  /// Convert image to AVIF (mobile-optimized, no fallbacks needed)
  static Future<Uint8List?> convertToAvif({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    // AVIF-only processing for mobile apps
    return await encodeToAvifWithTargetSize(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );
  }

  // Removed WebP and JPEG fallback methods - AVIF-only for mobile apps
}

/// Result of AVIF processing operation
class AvifProcessingResult {
  const AvifProcessingResult._({
    required this.isSuccess,
    this.avifData,
    this.fallbackData,
    this.format,
    this.quality,
    this.originalSize,
    this.compressedSize,
    this.errorMessage,
  });

  factory AvifProcessingResult.success({
    required Uint8List data,
    required String format,
    required int quality,
    required int originalSize,
    required int compressedSize,
  }) {
    return AvifProcessingResult._(
      isSuccess: true,
      avifData: format == 'avif' ? data : null,
      fallbackData: format != 'avif' ? data : null,
      format: format,
      quality: quality,
      originalSize: originalSize,
      compressedSize: compressedSize,
    );
  }

  factory AvifProcessingResult.error(String errorMessage) {
    return AvifProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? avifData;
  final Uint8List? fallbackData;
  final String? format;
  final int? quality;
  final int? originalSize;
  final int? compressedSize;
  final String? errorMessage;

  /// Get the best available data (AVIF preferred, fallback if needed)
  Uint8List? get bestData => avifData ?? fallbackData;

  /// Get compression ratio (0.0 to 1.0)
  double get compressionRatio {
    if (originalSize == null || compressedSize == null || originalSize == 0) {
      return 0.0;
    }
    return (originalSize! - compressedSize!) / originalSize!;
  }

  /// Get file size reduction percentage
  double get sizeReductionPercentage => compressionRatio * 100;
}

/// AVIF-only image processing for mobile apps
class EnhancedImageProcessor {
  /// Process image with AVIF format (mobile-optimized)
  static Future<AvifProcessingResult> processImage({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final inputFile = File(inputPath);
    final originalSize = await inputFile.length();

    // AVIF-only processing for mobile apps
    final avifData = await AvifProcessingService.convertToAvif(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    if (avifData != null) {
      return AvifProcessingResult.success(
        data: avifData,
        format: 'avif',
        quality: initialQuality,
        originalSize: originalSize,
        compressedSize: avifData.length,
      );
    }

    return AvifProcessingResult.error(
      'AVIF processing failed'
    );
  }

  /// Get supported formats for mobile apps (AVIF-only)
  static Future<List<String>> getSupportedFormats() async {
    // Mobile apps only need AVIF with FFmpeg support
    return ['avif'];
  }
}
