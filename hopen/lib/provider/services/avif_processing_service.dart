import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Service for handling AVIF image processing using FFmpeg
/// This service provides AVIF encoding capabilities using FFmpeg
class AvifProcessingService {
  /// Check if AVIF encoding is supported (FFmpeg always supports AVIF)
  static Future<bool> isAvifSupported() async {
    try {
      // FFmpeg with libavif support should always be available
      return true;
    } catch (e) {
      debugPrint('AVIF support check failed: $e');
      return false;
    }
  }

  /// Encode image file to AVIF format using FFmpeg
  /// Returns null if AVIF encoding fails
  static Future<Uint8List?> encodeToAvif({
    required String inputPath,
    int quality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      if (!await isAvifSupported()) {
        return null;
      }

      final tempDir = await getTemporaryDirectory();
      final outputPath = path.join(tempDir.path, 'output_${DateTime.now().millisecondsSinceEpoch}.avif');

      // Build FFmpeg command for AVIF encoding
      String command = '-i "$inputPath"';

      // Add scaling if dimensions are specified
      if (maxWidth != null && maxHeight != null) {
        command += ' -vf "scale=$maxWidth:$maxHeight:force_original_aspect_ratio=decrease"';
      }

      // AVIF encoding parameters
      command += ' -c:v libaom-av1 -crf ${_qualityToCrf(quality)} -b:v 0 -pix_fmt yuv420p';
      command += ' -f avif "$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        final outputFile = File(outputPath);
        if (await outputFile.exists()) {
          final bytes = await outputFile.readAsBytes();
          await outputFile.delete(); // Clean up temp file
          return bytes;
        }
      }

      return null;
    } catch (e) {
      debugPrint('AVIF encoding failed: $e');
      return null;
    }
  }

  /// Convert quality percentage to CRF value for AV1 (optimized for 2MB target)
  static int _qualityToCrf(int quality) {
    // CRF range for AV1: 0-63 (lower = better quality)
    // Optimized for 2MB profile pictures:
    // Quality 100% = CRF 18 (excellent quality, ~1.2MB)
    // Quality 80% = CRF 23 (very good quality, ~800KB)
    // Quality 60% = CRF 28 (good quality, ~500KB)
    // Quality 40% = CRF 33 (acceptable quality, ~300KB)
    return (40 - (quality * 22 / 100)).round().clamp(18, 40);
  }

  /// Encode image to AVIF with multiple quality attempts to meet size target
  static Future<Uint8List?> encodeToAvifWithTargetSize({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    if (!await isAvifSupported()) {
      return null;
    }

    int quality = initialQuality;
    Uint8List? result;

    // Try different quality levels to meet size target (optimized for 2MB)
    while (quality >= 60) {  // Don't go below 60% quality for 2MB target
      result = await encodeToAvif(
        inputPath: inputPath,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (result != null && result.length <= maxFileSizeBytes) {
        return result;
      }

      quality -= 5;  // Smaller steps for fine-tuning
    }

    return result; // Return last attempt even if it exceeds size limit
  }

  /// Get optimal quality setting for target file size using binary search
  static Future<int> getOptimalQuality({
    required String inputPath,
    required int targetFileSizeBytes,
    int? maxWidth,
    int? maxHeight,
  }) async {
    if (!await isAvifSupported()) {
      return 80; // Default quality
    }

    // Binary search for optimal quality (optimized for 2MB target)
    int minQuality = 60;  // Don't go below 60% for 2MB target
    int maxQuality = 95;
    int optimalQuality = 80;

    while (minQuality <= maxQuality) {
      final int testQuality = (minQuality + maxQuality) ~/ 2;

      final Uint8List? result = await encodeToAvif(
        inputPath: inputPath,
        quality: testQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (result == null) break;

      if (result.length <= targetFileSizeBytes) {
        optimalQuality = testQuality;
        minQuality = testQuality + 1;
      } else {
        maxQuality = testQuality - 1;
      }
    }

    return optimalQuality;
  }

  /// Convert image to AVIF with WebP and JPEG fallbacks
  static Future<Uint8List?> convertWithFallbacks({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    // Try AVIF first
    final avifResult = await encodeToAvifWithTargetSize(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    if (avifResult != null && avifResult.length <= maxFileSizeBytes) {
      return avifResult;
    }

    // Fallback to WebP
    final webpResult = await _encodeToWebP(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    if (webpResult != null && webpResult.length <= maxFileSizeBytes) {
      return webpResult;
    }

    // Final fallback to JPEG
    return await _encodeToJPEG(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );
  }

  /// Encode to WebP using FFmpeg
  static Future<Uint8List?> _encodeToWebP({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final outputPath = path.join(tempDir.path, 'output_${DateTime.now().millisecondsSinceEpoch}.webp');

      int quality = initialQuality;
      while (quality >= 65) {  // Don't go below 65% for WebP with 2MB target
        String command = '-i "$inputPath"';

        if (maxWidth != null && maxHeight != null) {
          command += ' -vf "scale=$maxWidth:$maxHeight:force_original_aspect_ratio=decrease"';
        }

        command += ' -c:v libwebp -quality $quality "$outputPath"';

        final session = await FFmpegKit.execute(command);
        final returnCode = await session.getReturnCode();

        if (ReturnCode.isSuccess(returnCode)) {
          final outputFile = File(outputPath);
          if (await outputFile.exists()) {
            final bytes = await outputFile.readAsBytes();
            if (bytes.length <= maxFileSizeBytes) {
              await outputFile.delete();
              return bytes;
            }
            await outputFile.delete();
          }
        }

        quality -= 5;  // Smaller steps for fine-tuning
      }

      return null;
    } catch (e) {
      debugPrint('WebP encoding failed: $e');
      return null;
    }
  }

  /// Encode to JPEG using FFmpeg
  static Future<Uint8List?> _encodeToJPEG({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final outputPath = path.join(tempDir.path, 'output_${DateTime.now().millisecondsSinceEpoch}.jpg');

      int quality = initialQuality;
      while (quality >= 70) {  // Don't go below 70% for JPEG with 2MB target
        String command = '-i "$inputPath"';

        if (maxWidth != null && maxHeight != null) {
          command += ' -vf "scale=$maxWidth:$maxHeight:force_original_aspect_ratio=decrease"';
        }

        command += ' -c:v mjpeg -q:v ${_qualityToJpegQ(quality)} "$outputPath"';

        final session = await FFmpegKit.execute(command);
        final returnCode = await session.getReturnCode();

        if (ReturnCode.isSuccess(returnCode)) {
          final outputFile = File(outputPath);
          if (await outputFile.exists()) {
            final bytes = await outputFile.readAsBytes();
            if (bytes.length <= maxFileSizeBytes) {
              await outputFile.delete();
              return bytes;
            }
            await outputFile.delete();
          }
        }

        quality -= 5;  // Smaller steps for fine-tuning
      }

      return null;
    } catch (e) {
      debugPrint('JPEG encoding failed: $e');
      return null;
    }
  }

  /// Convert quality percentage to JPEG q value (optimized for 2MB target)
  static int _qualityToJpegQ(int quality) {
    // JPEG q range: 1-31 (lower = better quality)
    // Optimized for 2MB profile pictures:
    // Quality 95% = Q-value 2 (excellent quality, ~1.5MB)
    // Quality 85% = Q-value 4 (very good quality, ~1MB)
    // Quality 75% = Q-value 6 (good quality, ~700KB)
    // Quality 70% = Q-value 8 (acceptable quality, ~500KB)
    return (12 - (quality * 10 / 100)).round().clamp(2, 12);
  }
}

/// Result of AVIF processing operation
class AvifProcessingResult {
  const AvifProcessingResult._({
    required this.isSuccess,
    this.avifData,
    this.fallbackData,
    this.format,
    this.quality,
    this.originalSize,
    this.compressedSize,
    this.errorMessage,
  });

  factory AvifProcessingResult.success({
    required Uint8List data,
    required String format,
    required int quality,
    required int originalSize,
    required int compressedSize,
  }) {
    return AvifProcessingResult._(
      isSuccess: true,
      avifData: format == 'avif' ? data : null,
      fallbackData: format != 'avif' ? data : null,
      format: format,
      quality: quality,
      originalSize: originalSize,
      compressedSize: compressedSize,
    );
  }

  factory AvifProcessingResult.error(String errorMessage) {
    return AvifProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? avifData;
  final Uint8List? fallbackData;
  final String? format;
  final int? quality;
  final int? originalSize;
  final int? compressedSize;
  final String? errorMessage;

  /// Get the best available data (AVIF preferred, fallback if needed)
  Uint8List? get bestData => avifData ?? fallbackData;

  /// Get compression ratio (0.0 to 1.0)
  double get compressionRatio {
    if (originalSize == null || compressedSize == null || originalSize == 0) {
      return 0.0;
    }
    return (originalSize! - compressedSize!) / originalSize!;
  }

  /// Get file size reduction percentage
  double get sizeReductionPercentage => compressionRatio * 100;
}

/// Enhanced image processing with AVIF support using FFmpeg
class EnhancedImageProcessor {
  /// Process image with AVIF as primary format and intelligent fallbacks
  static Future<AvifProcessingResult> processImage({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final inputFile = File(inputPath);
    final originalSize = await inputFile.length();

    // Try AVIF first
    final avifData = await AvifProcessingService.convertWithFallbacks(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    if (avifData != null) {
      // Determine the actual format used
      String format = 'avif';
      if (avifData.length > maxFileSizeBytes * 0.8) {
        // If file is close to limit, it might be a fallback format
        format = _detectFormat(avifData);
      }

      return AvifProcessingResult.success(
        data: avifData,
        format: format,
        quality: initialQuality,
        originalSize: originalSize,
        compressedSize: avifData.length,
      );
    }

    return AvifProcessingResult.error(
      'All image processing formats failed'
    );
  }

  /// Detect image format from byte signature
  static String _detectFormat(Uint8List bytes) {
    if (bytes.length < 12) return 'unknown';

    // AVIF signature
    if (bytes.length >= 12 &&
        bytes[4] == 0x66 && bytes[5] == 0x74 && bytes[6] == 0x79 && bytes[7] == 0x70 &&
        bytes[8] == 0x61 && bytes[9] == 0x76 && bytes[10] == 0x69 && bytes[11] == 0x66) {
      return 'avif';
    }

    // WebP signature
    if (bytes.length >= 12 &&
        bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && bytes[10] == 0x42 && bytes[11] == 0x50) {
      return 'webp';
    }

    // JPEG signature
    if (bytes.length >= 2 && bytes[0] == 0xFF && bytes[1] == 0xD8) {
      return 'jpeg';
    }

    // PNG signature
    if (bytes.length >= 8 &&
        bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47 &&
        bytes[4] == 0x0D && bytes[5] == 0x0A && bytes[6] == 0x1A && bytes[7] == 0x0A) {
      return 'png';
    }

    return 'unknown';
  }

  /// Get format priority list based on platform support
  static Future<List<String>> getSupportedFormats() async {
    final formats = <String>[];

    if (await AvifProcessingService.isAvifSupported()) {
      formats.add('avif');
    }

    formats.addAll(['webp', 'jpeg', 'png']);
    return formats;
  }
}
