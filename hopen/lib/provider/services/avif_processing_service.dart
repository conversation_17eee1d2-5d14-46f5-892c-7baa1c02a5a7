import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Service for handling AVIF image processing
/// This service provides AVIF encoding capabilities using platform-specific implementations
class AvifProcessingService {
  static const MethodChannel _channel = MethodChannel('hopen/avif_processor');

  /// Check if AVIF encoding is supported on the current platform
  static Future<bool> isAvifSupported() async {
    try {
      final bool isSupported = await _channel.invokeMethod('isAvifSupported');
      return isSupported;
    } catch (e) {
      debugPrint('AVIF support check failed: $e');
      return false;
    }
  }

  /// Encode image data to AVIF format
  /// Returns null if AVIF encoding is not supported or fails
  static Future<Uint8List?> encodeToAvif({
    required Uint8List imageData,
    required int width,
    required int height,
    int quality = 80,
  }) async {
    try {
      if (!await isAvifSupported()) {
        return null;
      }

      final Map<String, dynamic> params = {
        'imageData': imageData,
        'width': width,
        'height': height,
        'quality': quality,
      };

      final Uint8List? result = await _channel.invokeMethod('encodeToAvif', params);
      return result;
    } catch (e) {
      debugPrint('AVIF encoding failed: $e');
      return null;
    }
  }

  /// Encode image to AVIF with multiple quality attempts to meet size target
  static Future<Uint8List?> encodeToAvifWithTargetSize({
    required Uint8List imageData,
    required int width,
    required int height,
    required int maxFileSizeBytes,
    int initialQuality = 80,
  }) async {
    if (!await isAvifSupported()) {
      return null;
    }

    int quality = initialQuality;
    Uint8List? result;

    // Try different quality levels to meet size target
    while (quality >= 30) {
      result = await encodeToAvif(
        imageData: imageData,
        width: width,
        height: height,
        quality: quality,
      );

      if (result != null && result.length <= maxFileSizeBytes) {
        return result;
      }

      quality -= 10;
    }

    return result; // Return last attempt even if it exceeds size limit
  }

  /// Get optimal quality setting for target file size
  static Future<int> getOptimalQuality({
    required Uint8List imageData,
    required int width,
    required int height,
    required int targetFileSizeBytes,
  }) async {
    if (!await isAvifSupported()) {
      return 80; // Default quality
    }

    // Binary search for optimal quality
    int minQuality = 30;
    int maxQuality = 95;
    int optimalQuality = 80;

    while (minQuality <= maxQuality) {
      final int testQuality = (minQuality + maxQuality) ~/ 2;
      
      final Uint8List? result = await encodeToAvif(
        imageData: imageData,
        width: width,
        height: height,
        quality: testQuality,
      );

      if (result == null) break;

      if (result.length <= targetFileSizeBytes) {
        optimalQuality = testQuality;
        minQuality = testQuality + 1;
      } else {
        maxQuality = testQuality - 1;
      }
    }

    return optimalQuality;
  }
}

/// Result of AVIF processing operation
class AvifProcessingResult {
  const AvifProcessingResult._({
    required this.isSuccess,
    this.avifData,
    this.fallbackData,
    this.format,
    this.quality,
    this.originalSize,
    this.compressedSize,
    this.errorMessage,
  });

  factory AvifProcessingResult.success({
    required Uint8List data,
    required String format,
    required int quality,
    required int originalSize,
    required int compressedSize,
  }) {
    return AvifProcessingResult._(
      isSuccess: true,
      avifData: format == 'avif' ? data : null,
      fallbackData: format != 'avif' ? data : null,
      format: format,
      quality: quality,
      originalSize: originalSize,
      compressedSize: compressedSize,
    );
  }

  factory AvifProcessingResult.error(String errorMessage) {
    return AvifProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? avifData;
  final Uint8List? fallbackData;
  final String? format;
  final int? quality;
  final int? originalSize;
  final int? compressedSize;
  final String? errorMessage;

  /// Get the best available data (AVIF preferred, fallback if needed)
  Uint8List? get bestData => avifData ?? fallbackData;

  /// Get compression ratio (0.0 to 1.0)
  double get compressionRatio {
    if (originalSize == null || compressedSize == null || originalSize == 0) {
      return 0.0;
    }
    return (originalSize! - compressedSize!) / originalSize!;
  }

  /// Get file size reduction percentage
  double get sizeReductionPercentage => compressionRatio * 100;
}

/// Enhanced image processing with AVIF support
class EnhancedImageProcessor {
  /// Process image with AVIF as primary format and intelligent fallbacks
  static Future<AvifProcessingResult> processImage({
    required Uint8List imageData,
    required int width,
    required int height,
    required int maxFileSizeBytes,
    int initialQuality = 80,
  }) async {
    final originalSize = imageData.length;

    // Try AVIF first
    final avifData = await AvifProcessingService.encodeToAvifWithTargetSize(
      imageData: imageData,
      width: width,
      height: height,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
    );

    if (avifData != null && avifData.length <= maxFileSizeBytes) {
      return AvifProcessingResult.success(
        data: avifData,
        format: 'avif',
        quality: initialQuality,
        originalSize: originalSize,
        compressedSize: avifData.length,
      );
    }

    // Fallback to WebP (handled by existing image processing service)
    return AvifProcessingResult.error(
      'AVIF processing failed, falling back to WebP/JPEG'
    );
  }

  /// Get format priority list based on platform support
  static Future<List<String>> getSupportedFormats() async {
    final formats = <String>[];
    
    if (await AvifProcessingService.isAvifSupported()) {
      formats.add('avif');
    }
    
    formats.addAll(['webp', 'jpeg', 'png']);
    return formats;
  }
}
