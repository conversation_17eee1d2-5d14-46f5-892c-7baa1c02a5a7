import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:mqtt5_client/mqtt5_client.dart';
import 'package:mqtt5_client/mqtt5_server_client.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:typed_data/typed_data.dart';

import '../../config/app_config.dart';
import '../datasources/contacts_remote_datasource.dart';
import '../datasources/http_remote_datasource.dart';
import '../services/auth/ory_auth_service.dart';
import '../../repositories/auth/auth_repository.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../di/injection_container_refactored.dart' as di;

/// Real-time notification service for MQTT + HTTP polling
/// 
/// This service handles:
/// - MQTT5 connection and message reception with <PERSON><PERSON> authentication
/// - HTTP polling fallback when MQTT is unavailable
/// - Network connectivity monitoring
/// - Message routing via callbacks (no direct dialog/bloc dependencies)
/// - Comprehensive monitoring and debugging capabilities
class RealTimeNotificationService {
  // MQTT client
  MqttServerClient? _mqttClient;
  
  // HTTP polling
  Timer? _pollingTimer;
  final Duration _pollingInterval = const Duration(seconds: 30);
  
  // Network connectivity
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  
  // Data sources
  final ContactsRemoteDataSource _contactsDataSource;
  final HttpRemoteDataSource _httpDataSource;
  
  // Authentication
  late final OryAuthService _oryAuthService;
  late final AuthRepository _authRepository;
  
  // Callbacks for upper layers
  final Function(Map<String, dynamic>)? onNotificationReceived;
  final Function(bool)? onConnectionStatusChanged;
  
  // State
  bool _isConnected = false;
  bool _isInitialized = false;
  String? _currentUserId;
  
  // Monitoring and debugging
  int _connectionAttempts = 0;
  DateTime? _lastConnectionAttempt;
  DateTime? _lastSuccessfulConnection;
  DateTime? _lastMessageReceived;
  int _messagesReceived = 0;
  int _messagesProcessed = 0;
  int _messageErrors = 0;
  final List<String> _recentErrors = [];
  static const int _maxRecentErrors = 10;
  
  // Reconnection logic
  Timer? _reconnectionTimer;
  int _reconnectionAttempts = 0;
  static const int _maxReconnectionAttempts = 5;
  static const Duration _reconnectionDelay = Duration(seconds: 5);

  RealTimeNotificationService({
    required ContactsRemoteDataSource contactsDataSource,
    required HttpRemoteDataSource httpDataSource,
    this.onNotificationReceived,
    this.onConnectionStatusChanged,
  })  : _contactsDataSource = contactsDataSource,
        _httpDataSource = httpDataSource {
    // Initialize authentication services
    _oryAuthService = di.sl<OryAuthService>();
    _authRepository = di.sl<AuthRepository>();
  }

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _isInitialized = true;
    
    LoggingService.info('RealTimeNotificationService: Initializing service');
    
    // Get current user ID from auth repository
    await _getCurrentUserId();
    
    // Start connectivity monitoring
    _startConnectivityMonitoring();
    
    // Try to connect to MQTT if user is authenticated
    if (_currentUserId != null) {
    await _connectToMqtt();
    } else {
      LoggingService.warning('RealTimeNotificationService: No authenticated user, skipping MQTT connection');
    }

      // Start HTTP polling as fallback
      _startPolling();
    
    LoggingService.success('RealTimeNotificationService: Service initialized successfully');
  }

  /// Get current user ID from auth repository
  Future<void> _getCurrentUserId() async {
    try {
      final result = await _authRepository.getCurrentUser();
      if (result.isSuccess) {
        _currentUserId = result.data.id;
        LoggingService.info('RealTimeNotificationService: Current user ID: $_currentUserId');
      } else {
        LoggingService.warning('RealTimeNotificationService: No authenticated user found');
        _currentUserId = null;
      }
    } catch (e) {
      LoggingService.error('RealTimeNotificationService: Error getting current user: $e');
      _addError('Failed to get current user: $e');
      _currentUserId = null;
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    _isInitialized = false;
    
    LoggingService.info('RealTimeNotificationService: Disposing service');
    
    await _connectivitySubscription?.cancel();
    _pollingTimer?.cancel();
    _reconnectionTimer?.cancel();
    
    if (_mqttClient?.connectionStatus?.state == MqttConnectionState.connected) {
      _mqttClient?.disconnect();
    }
    
    _isConnected = false;
    onConnectionStatusChanged?.call(false);
    
    LoggingService.info('RealTimeNotificationService: Service disposed');
  }

  /// Check if service is connected
  bool get isConnected => _isConnected;

  /// Get service statistics for monitoring
  Map<String, dynamic> get statistics => {
    'isConnected': _isConnected,
    'isInitialized': _isInitialized,
    'currentUserId': _currentUserId,
    'connectionAttempts': _connectionAttempts,
    'lastConnectionAttempt': _lastConnectionAttempt?.toIso8601String(),
    'lastSuccessfulConnection': _lastSuccessfulConnection?.toIso8601String(),
    'lastMessageReceived': _lastMessageReceived?.toIso8601String(),
    'messagesReceived': _messagesReceived,
    'messagesProcessed': _messagesProcessed,
    'messageErrors': _messageErrors,
    'reconnectionAttempts': _reconnectionAttempts,
    'recentErrors': _recentErrors.toList(),
  };

  /// Start connectivity monitoring
  void _startConnectivityMonitoring() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((result) {
      LoggingService.info('RealTimeNotificationService: Connectivity changed: $result');
      
      if (result != ConnectivityResult.none) {
        _scheduleReconnection();
      } else {
        _isConnected = false;
        onConnectionStatusChanged?.call(false);
      }
    });
  }

  /// Schedule reconnection attempt
  void _scheduleReconnection() {
    if (_reconnectionTimer?.isActive == true) return;
    
    if (_reconnectionAttempts < _maxReconnectionAttempts) {
      _reconnectionTimer = Timer(_reconnectionDelay, () {
        _connectToMqtt();
      });
      
      LoggingService.info('RealTimeNotificationService: Scheduled reconnection attempt ${_reconnectionAttempts + 1}');
    } else {
      LoggingService.warning('RealTimeNotificationService: Max reconnection attempts reached');
    }
  }

  /// Connect to MQTT broker with Ory Kratos session token authentication
  Future<void> _connectToMqtt() async {
    if (_currentUserId == null) {
      LoggingService.warning('RealTimeNotificationService: No user ID, skipping MQTT connection');
      return;
    }
    
    _connectionAttempts++;
    _lastConnectionAttempt = DateTime.now();
    
    try {
      LoggingService.info('RealTimeNotificationService: Attempting MQTT connection (attempt $_connectionAttempts)');
      
      // Get valid Ory Kratos session token
      final sessionToken = await _oryAuthService.getValidToken();
      if (sessionToken == null) {
        LoggingService.warning('RealTimeNotificationService: No valid session token, skipping MQTT connection');
        _addError('No valid session token available');
        return;
      }

      LoggingService.info('RealTimeNotificationService: Connecting to MQTT with session token');
      
      _mqttClient = MqttServerClient.withPort(
        AppConfig.mqttHost,
        'hopen_client_${_currentUserId}_${DateTime.now().millisecondsSinceEpoch}',
        AppConfig.mqttPort,
      );
      
      _mqttClient!.logging(on: kDebugMode);
      _mqttClient!.onConnected = _onMqttConnected;
      _mqttClient!.onDisconnected = _onMqttDisconnected;
      _mqttClient!.onSubscribed = (MqttSubscription subscription) => _onMqttSubscribed(subscription.topic.rawTopic ?? '');
      _mqttClient!.onUnsubscribed = (MqttSubscription subscription) => _onMqttUnsubscribed(subscription.topic.rawTopic ?? '');

      // Use Ory Kratos session token for MQTT authentication
      // Username = user ID, Password = session token (as per backend MQTT auth implementation)
      final connMessage = MqttConnectMessage()
          .withClientIdentifier('hopen_client_${_currentUserId}_${DateTime.now().millisecondsSinceEpoch}')
          .authenticateAs(_currentUserId!, sessionToken)
          .startClean()
          .withWillQos(MqttQos.atLeastOnce);
      
      _mqttClient!.connectionMessage = connMessage;
      
      await _mqttClient!.connect();
      
      LoggingService.info('RealTimeNotificationService: MQTT connection attempt completed');
    } catch (e) {
      LoggingService.error('RealTimeNotificationService: MQTT connection failed: $e');
      _addError('MQTT connection failed: $e');
      _isConnected = false;
      onConnectionStatusChanged?.call(false);
      
      _reconnectionAttempts++;
      _scheduleReconnection();
    }
  }

  /// MQTT connection callbacks
  void _onMqttConnected() {
    LoggingService.success('RealTimeNotificationService: MQTT connected successfully');
    _isConnected = true;
    _lastSuccessfulConnection = DateTime.now();
    _reconnectionAttempts = 0;
    _reconnectionTimer?.cancel();
    
    onConnectionStatusChanged?.call(true);
    _subscribeToTopics();
  }

  void _onMqttDisconnected() {
    LoggingService.warning('RealTimeNotificationService: MQTT disconnected');
    _isConnected = false;
    onConnectionStatusChanged?.call(false);
    
    // Schedule reconnection if not manually disconnected
    if (_isInitialized) {
      _scheduleReconnection();
    }
  }

  void _onMqttSubscribed(String topic) {
    LoggingService.info('RealTimeNotificationService: Subscribed to topic: $topic');
  }

  void _onMqttUnsubscribed(String topic) {
    LoggingService.info('RealTimeNotificationService: Unsubscribed from topic: $topic');
  }

  /// Subscribe to notification topics
  void _subscribeToTopics() {
    if (_currentUserId == null || _mqttClient == null) return;
    
    final topics = [
      'hopen/requests/$_currentUserId',  // Unified personal notifications & requests
      'hopen/calls/$_currentUserId',    // Call notifications
      'hopen/chat/$_currentUserId',     // Personal chat
      'hopen/system/$_currentUserId',   // System notifications
    ];

    for (final topic in topics) {
      _mqttClient!.subscribe(topic, MqttQos.atLeastOnce);
    }
    
    // Listen for messages
    _mqttClient!.updates.listen(_onMqtt5Message);
  }

  /// Handle incoming MQTT messages
  void _onMqtt5Message(List<MqttReceivedMessage<MqttMessage>> messages) {
    for (final message in messages) {
      final topic = message.topic;
      final payload = message.payload as MqttPublishMessage;
      final payloadString = String.fromCharCodes(payload.payload.message ?? Uint8List(0));
      
      _messagesReceived++;
      _lastMessageReceived = DateTime.now();
      
      try {
        final data = jsonDecode(payloadString) as Map<String, dynamic>;
        
        // Add topic info to data
        data['_topic'] = topic;
        data['_received_at'] = DateTime.now().toIso8601String();
        
        LoggingService.info('RealTimeNotificationService: Received MQTT message on topic $topic: ${data['type']}');
        
        // Route to upper layer via callback
        onNotificationReceived?.call(data);
        
        _messagesProcessed++;
        
      } catch (e) {
        LoggingService.error('RealTimeNotificationService: Error parsing MQTT message: $e');
        _addError('Error parsing MQTT message: $e');
        _messageErrors++;
      }
    }
  }

  /// Start HTTP polling for notifications
  void _startPolling() {
    _pollingTimer?.cancel();
    _pollingTimer = Timer.periodic(_pollingInterval, (_) => _pollForNotifications());
    
    LoggingService.info('RealTimeNotificationService: HTTP polling started');
  }

  /// Poll for notifications via HTTP
  Future<void> _pollForNotifications() async {
    if (_currentUserId == null) return;
    
    try {
      LoggingService.debug('RealTimeNotificationService: Polling for notifications');
      
      // Poll for contact requests
      await _pollContactRequests();
      
      // Poll for bubble requests
      await _pollBubbleRequests();
      
      // Extend polling here if additional request types need periodic refresh
    } catch (e) {
      LoggingService.error('RealTimeNotificationService: Error polling for notifications: $e');
      _addError('Error polling for notifications: $e');
    }
  }

  /// Poll for contact requests
  Future<void> _pollContactRequests() async {
    try {
      final requests = await _contactsDataSource.getPendingContactRequests();
      
      for (final request in requests) {
        final data = {
          'type': 'contact_request_received',
          'fromUserId': request.userId ?? request.id,
          'fromUserName': request.firstName ?? 'Unknown User',
          '_source': 'http_poll',
          '_received_at': DateTime.now().toIso8601String(),
        };
        
        onNotificationReceived?.call(data);
      }
    } catch (e) {
      LoggingService.error('RealTimeNotificationService: Error polling contact requests: $e');
      _addError('Error polling contact requests: $e');
    }
  }

  /// Poll for bubble requests
  Future<void> _pollBubbleRequests() async {
    try {
      // This would need to be implemented in your bubble data source
      // final requests = await _bubbleDataSource.getPendingBubbleRequests();
      
      // for (final request in requests) {
      //   final data = {
      //     'type': 'bubble_invitation_received',
      //     'fromUserId': request['fromUserId'],
      //     'fromUserName': request['fromUserName'],
      //     'bubbleId': request['bubbleId'],
      //     'bubbleName': request['bubbleName'],
      //     '_source': 'http_poll',
      //     '_received_at': DateTime.now().toIso8601String(),
      //   };
      //   
      //   onNotificationReceived?.call(data);
      // }
      } catch (e) {
      LoggingService.error('RealTimeNotificationService: Error polling bubble requests: $e');
      _addError('Error polling bubble requests: $e');
    }
  }

  /// Set current user ID and reconnect MQTT
  void setUserId(String userId) {
    LoggingService.info('RealTimeNotificationService: Setting user ID: $userId');
    _currentUserId = userId;
    
    if (_isInitialized) {
      // Reconnect with new user ID
      _connectToMqtt();
      // Perform immediate poll for pending requests
      _pollForNotifications();
    }
  }

  /// Add error to recent errors list
  void _addError(String error) {
    _recentErrors.add('${DateTime.now().toIso8601String()}: $error');
    if (_recentErrors.length > _maxRecentErrors) {
      _recentErrors.removeAt(0);
    }
  }

  /// Get debug information
  String getDebugInfo() {
    final stats = statistics;
    final buffer = StringBuffer();
    
    buffer.writeln('=== RealTimeNotificationService Debug Info ===');
    buffer.writeln('Connected: ${stats['isConnected']}');
    buffer.writeln('Initialized: ${stats['isInitialized']}');
    buffer.writeln('User ID: ${stats['currentUserId']}');
    buffer.writeln('Connection Attempts: ${stats['connectionAttempts']}');
    buffer.writeln('Last Connection: ${stats['lastConnectionAttempt']}');
    buffer.writeln('Last Success: ${stats['lastSuccessfulConnection']}');
    buffer.writeln('Messages Received: ${stats['messagesReceived']}');
    buffer.writeln('Messages Processed: ${stats['messagesProcessed']}');
    buffer.writeln('Message Errors: ${stats['messageErrors']}');
    buffer.writeln('Reconnection Attempts: ${stats['reconnectionAttempts']}');
    
    if (_recentErrors.isNotEmpty) {
      buffer.writeln('\nRecent Errors:');
      for (final error in _recentErrors) {
        buffer.writeln('  $error');
      }
    }
    
    return buffer.toString();
  }
}

/// Extension for testing
extension RealTimeNotificationServiceTest on RealTimeNotificationService {
  void handleTestMessage(String topic, String payload) {
    // For tests, rely on public service APIs or mocks; MQTT internals are not invoked here
  }
  
  bool get isPollingActive {
    return _pollingTimer != null;
  }
}
