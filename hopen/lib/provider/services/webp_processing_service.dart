import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;

/// Multi-format image processing service for mobile apps using native dart image library
/// Supports JPEG, PNG, WebP, and Bitmap uploads
/// Client uploads any supported format, backend converts to WebP for optimal storage and delivery
/// Flutter natively displays WebP images efficiently
class WebPProcessingService {

  /// Supported upload formats
  static const List<String> supportedFormats = ['jpg', 'jpeg', 'png', 'webp', 'bmp'];

  /// Check if multi-format processing is supported (always true with dart image library)
  static Future<bool> isWebPSupported() async {
    return true; // Native dart image library supports all required formats
  }

  /// Check if a file format is supported for upload
  static bool isSupportedFormat(String extension) {
    final ext = extension.toLowerCase().replaceAll('.', '');
    return supportedFormats.contains(ext);
  }

  /// Process image from any supported format (JPEG, PNG, WebP, Bitmap)
  /// Outputs JPEG for upload to backend (backend converts to WebP for storage)
  static Future<Uint8List?> encodeToWebP({
    required String inputPath,
    required int quality,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final inputFile = File(inputPath);
      final extension = inputPath.split('.').last.toLowerCase();

      print('🔄 Processing ${extension.toUpperCase()} image: quality=$quality, size=${maxWidth}x${maxHeight}');

      // Read and decode the input image (supports JPEG, PNG, WebP, BMP)
      final inputBytes = await inputFile.readAsBytes();
      final image = img.decodeImage(inputBytes);

      if (image == null) {
        print('❌ Failed to decode input image format: $extension');
        return null;
      }

      print('📐 Original dimensions: ${image.width}x${image.height}');
      print('🎨 Original format: ${extension.toUpperCase()}');

      // Resize if needed
      img.Image processedImage = image;
      if (maxWidth != null && maxHeight != null) {
        if (image.width > maxWidth || image.height > maxHeight) {
          processedImage = img.copyResize(
            image,
            width: maxWidth,
            height: maxHeight,
            interpolation: img.Interpolation.cubic,
          );
          print('📐 Resized to: ${processedImage.width}x${processedImage.height}');
        }
      }

      // Always encode to JPEG for upload (backend will convert to WebP for storage)
      // JPEG is universally supported and provides good compression
      final jpegBytes = img.encodeJpg(processedImage, quality: quality);

      print('✅ JPEG encoding successful: ${jpegBytes.length} bytes');
      print('📤 Ready for upload (backend will convert to WebP)');
      return Uint8List.fromList(jpegBytes);

    } catch (e) {
      print('❌ Image processing failed: $e');
      return null;
    }
  }

  /// Encode image to WebP with size-based compression strategy (single-pass)
  static Future<Uint8List?> encodeToWebPWithTargetSize({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    // Get original file size to determine compression strategy
    final originalFile = File(inputPath);
    final originalSizeBytes = await originalFile.length();
    final originalSizeMB = originalSizeBytes / 1024 / 1024;
    
    print('📁 Original image: ${originalSizeMB.toStringAsFixed(2)}MB');
    print('🎯 Target size: ${(maxFileSizeBytes / 1024 / 1024).toStringAsFixed(1)}MB');

    // Determine quality based on file size (optimized strategy)
    int quality;
    if (originalSizeBytes < 1 * 1024 * 1024) { // < 1MB
      quality = 100; // NO COMPRESSION - 100% quality
      print('✨ Image < 1MB: NO COMPRESSION (100% quality)');
    } else if (originalSizeBytes <= 2 * 1024 * 1024) { // 1MB - 2MB
      quality = 90; // 90% quality compression
      print('🔧 Image 1-2MB: 90% quality compression');
    } else { // > 2MB
      quality = 80; // 80% quality compression (slightly higher than AVIF for WebP)
      print('🔧 Image > 2MB: 80% quality compression');
    }
    
    // For profile pictures, use fixed 1440x1440 for consistency
    final targetWidth = maxWidth ?? 1440;
    final targetHeight = maxHeight ?? 1440;
    
    print('🎯 WebP encoding: ${originalSizeMB.toStringAsFixed(2)}MB → quality=$quality, size=${targetWidth}x${targetHeight}');

    final result = await encodeToWebP(
      inputPath: inputPath,
      quality: quality,
      maxWidth: targetWidth,
      maxHeight: targetHeight,
    );

    stopwatch.stop();
    print('⚡ WebP encoding completed in ${stopwatch.elapsedMilliseconds}ms');

    return result;
  }

  /// Fast WebP encoding with size-based compression strategy
  static Future<Uint8List?> fastEncodeToWebP({
    required String inputPath,
    required int maxFileSizeBytes,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final stopwatch = Stopwatch()..start();
    print('⚡ Starting fast WebP encoding');
    
    // Get original file size to determine compression strategy
    final originalFile = File(inputPath);
    final originalSizeBytes = await originalFile.length();
    final originalSizeMB = originalSizeBytes / 1024 / 1024;
    
    // Apply same compression strategy as main encoding
    int quality;
    if (originalSizeBytes < 1 * 1024 * 1024) { // < 1MB
      quality = 100; // NO COMPRESSION - 100% quality
      print('⚡ Fast encoding < 1MB: NO COMPRESSION (100% quality)');
    } else if (originalSizeBytes <= 2 * 1024 * 1024) { // 1MB - 2MB
      quality = 90; // 90% quality compression
      print('⚡ Fast encoding 1-2MB: 90% quality');
    } else { // > 2MB
      quality = 80; // 80% quality compression
      print('⚡ Fast encoding > 2MB: 80% quality');
    }
    
    // For profile pictures, use fixed 1440x1440 for consistency
    final targetWidth = maxWidth ?? 1440;
    final targetHeight = maxHeight ?? 1440;
    
    print('🎯 Fast encoding: ${originalSizeMB.toStringAsFixed(2)}MB → quality=$quality, size=${targetWidth}x${targetHeight}');

    final result = await encodeToWebP(
      inputPath: inputPath,
      quality: quality,
      maxWidth: targetWidth,
      maxHeight: targetHeight,
    );

    stopwatch.stop();
    print('⚡ Fast WebP encoding completed in ${stopwatch.elapsedMilliseconds}ms');

    return result;
  }

  /// Convert image to WebP with size-based compression strategy (single-pass)
  static Future<Uint8List?> convertToWebP({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    // Always use the size-based encoding strategy for consistent behavior
    return await encodeToWebPWithTargetSize(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );
  }
}

/// Result of WebP processing operation
class WebPProcessingResult {
  const WebPProcessingResult._({
    required this.isSuccess,
    this.webpData,
    this.fallbackData,
    this.format,
    this.quality,
    this.originalSize,
    this.compressedSize,
    this.errorMessage,
  });

  factory WebPProcessingResult.success({
    required Uint8List data,
    required String format,
    required int quality,
    required int originalSize,
    required int compressedSize,
  }) {
    return WebPProcessingResult._(
      isSuccess: true,
      webpData: format == 'webp' ? data : null,
      fallbackData: format != 'webp' ? data : null,
      format: format,
      quality: quality,
      originalSize: originalSize,
      compressedSize: compressedSize,
    );
  }

  factory WebPProcessingResult.error(String errorMessage) {
    return WebPProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? webpData;
  final Uint8List? fallbackData;
  final String? format;
  final int? quality;
  final int? originalSize;
  final int? compressedSize;
  final String? errorMessage;

  /// Get the best available data (WebP preferred, fallback if needed)
  Uint8List? get bestData => webpData ?? fallbackData;

  /// Get compression ratio (0.0 to 1.0)
  double get compressionRatio {
    if (originalSize == null || compressedSize == null || originalSize == 0) {
      return 0.0;
    }
    return (originalSize! - compressedSize!) / originalSize!;
  }

  /// Get file size reduction percentage
  double get sizeReductionPercentage => compressionRatio * 100;
}

/// WebP-only image processing for mobile apps
class EnhancedImageProcessor {
  /// Process image with WebP format (mobile-optimized)
  static Future<WebPProcessingResult> processImage({
    required String inputPath,
    required int maxFileSizeBytes,
    int initialQuality = 80,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final inputFile = File(inputPath);
    final originalSize = await inputFile.length();

    // WebP-only processing for mobile apps
    final webpData = await WebPProcessingService.convertToWebP(
      inputPath: inputPath,
      maxFileSizeBytes: maxFileSizeBytes,
      initialQuality: initialQuality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );

    if (webpData != null) {
      return WebPProcessingResult.success(
        data: webpData,
        format: 'webp',
        quality: initialQuality,
        originalSize: originalSize,
        compressedSize: webpData.length,
      );
    }

    return WebPProcessingResult.error(
      'WebP processing failed'
    );
  }

  /// Get supported formats for mobile apps (WebP-only)
  static Future<List<String>> getSupportedFormats() async {
    // Mobile apps only need WebP with native dart image support
    return ['webp'];
  }
}
