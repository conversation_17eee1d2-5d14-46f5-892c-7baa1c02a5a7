import 'dart:io';
import 'dart:typed_data';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:http/http.dart' as http;
import 'package:mime/mime.dart';
import '../api/http_api_service.dart';
import '../../../config/app_config.dart';

class StorageService {
  factory StorageService() => _instance; // Added for explicit initialization

  StorageService._internal();
  static final StorageService _instance = StorageService._internal();

  late final HttpApiService _apiService = HttpApiService();
  final String _bucket = 'hopen';
  bool _isInitialized = true; // No external initialization required

  // New initialize method
  Future<void> initialize({
    String? endpoint,
    int? port,
    String? accessKey,
    String? secretKey,
    bool useSSL = true,
  }) async {
    // Presigned upload flow does not require client-side credentials.
    if (_isInitialized) {
      return;
    }
    _isInitialized = true;
  }

  Future<String?> uploadFile(File file, [String? objectPath]) async {
    _checkInitialization();
    try {
      final bytes = await file.readAsBytes();
      final fileName = objectPath ?? path.basename(file.path);
      final contentType = lookupMimeType(file.path) ?? 'application/octet-stream';
      return _upload(bytes, fileName, contentType);
    } catch (_) {
      return null;
    }
  }

  Future<String?> uploadData(Uint8List data, String extension) async {
    _checkInitialization();
    try {
      final fileName = '${const Uuid().v4()}$extension';
      final contentType = lookupMimeType(fileName) ?? 'application/octet-stream';
      return _upload(data, fileName, contentType);
    } catch (_) {
      return null;
    }
  }

  Future<String?> _upload(Uint8List bytes, String fileName, String contentType) async {
    try {
      // 1. Request presigned upload URL from backend
      final generateUrlBody = {
        'file_name': fileName,
        'content_type': contentType,
        'file_size': bytes.length,
      };

      final Map<String, dynamic> response = await _apiService.post<Map<String, dynamic>>(
        '/media/generate-upload-url',
        body: generateUrlBody,
      );

      final uploadUrl = response['upload_url'] as String?;
      final objectKey = response['object_key'] as String?;
      final fileId = response['file_id'] as String?;

      if (uploadUrl == null || objectKey == null || fileId == null) {
        throw Exception('Invalid response from generate-upload-url');
      }

      // 2. Upload file bytes directly to MinIO via presigned URL (HTTP PUT)
      final putResp = await http.put(
        Uri.parse(uploadUrl),
        headers: {
          'Content-Type': contentType,
        },
        body: bytes,
      );

      if (putResp.statusCode != 200 && putResp.statusCode != 204) {
        throw Exception('Failed to PUT object: HTTP ${putResp.statusCode}');
      }

      // 3. Confirm upload and save metadata to database
      final confirmBody = {
        'file_id': fileId,
        'file_name': fileName,
        'content_type': contentType,
        'file_size': bytes.length,
        'object_key': objectKey,
      };

      final confirmResponse = await _apiService.post<Map<String, dynamic>>(
        '/media/confirm-upload',
        body: confirmBody,
      );

      final finalUrl = confirmResponse['url'] as String?;
      if (finalUrl == null) {
        throw Exception('Invalid response from confirm-upload');
      }

      return finalUrl;
    } catch (_) {
      return null;
    }
  }

  Future<bool> deleteFile(String fileName) async {
    // Operation not supported via presigned uploads without metadata; implement later
    print('deleteFile not supported in presigned flow');
    return false;
  }

  Future<List<String>> listFiles() async {
    print('listFiles not supported in presigned flow');
    return [];
  }

  // Helper to check if service is initialized
  void _checkInitialization() {
    if (!_isInitialized) {
      throw StateError(
        'StorageService is not initialized. Call initialize() first.',
      );
    }
  }
}
