import 'dart:math' as math;

import '../exceptions/storage_exception.dart';

/// Service for handling retry logic with exponential backoff
class RetryService {
  static const int defaultMaxRetries = 3;
  static const Duration defaultBaseDelay = Duration(seconds: 1);
  static const Duration defaultMaxDelay = Duration(minutes: 5);

  /// Execute a function with retry logic and exponential backoff
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = defaultMaxRetries,
    Duration baseDelay = defaultBaseDelay,
    Duration maxDelay = defaultMaxDelay,
    bool Function(dynamic error)? shouldRetry,
  }) async {
    int attempt = 0;
    dynamic lastError;

    while (attempt <= maxRetries) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        // Check if we should retry this error
        if (!_shouldRetryError(error, shouldRetry)) {
          rethrow;
        }

        // If this was the last attempt, rethrow the error
        if (attempt == maxRetries) {
          rethrow;
        }

        // Calculate delay with exponential backoff and jitter
        final delay = _calculateDelay(attempt, baseDelay, maxDelay);
        
        print('🔄 Retry attempt ${attempt + 1}/$maxRetries after ${delay.inMilliseconds}ms delay. Error: $error');
        
        await Future.delayed(delay);
        attempt++;
      }
    }

    // This should never be reached, but just in case
    throw lastError;
  }

  /// Determine if an error should be retried
  static bool _shouldRetryError(
    dynamic error,
    bool Function(dynamic error)? customShouldRetry,
  ) {
    // Use custom retry logic if provided
    if (customShouldRetry != null) {
      return customShouldRetry(error);
    }

    // Default retry logic
    if (error is StorageException) {
      return error.isRetryable;
    }
    
    if (error is NetworkException) {
      return error.isRetryable;
    }

    // For unknown errors, don't retry by default
    return false;
  }

  /// Calculate delay with exponential backoff and jitter
  static Duration _calculateDelay(
    int attempt,
    Duration baseDelay,
    Duration maxDelay,
  ) {
    // Exponential backoff: baseDelay * 2^attempt
    final exponentialDelay = Duration(
      milliseconds: (baseDelay.inMilliseconds * math.pow(2, attempt)).round(),
    );

    // Add jitter (random factor between 0.5 and 1.5)
    final jitterFactor = 0.5 + (math.Random().nextDouble());
    final delayWithJitter = Duration(
      milliseconds: (exponentialDelay.inMilliseconds * jitterFactor).round(),
    );

    // Cap at maximum delay
    return delayWithJitter > maxDelay ? maxDelay : delayWithJitter;
  }

  /// Create a retry configuration for network operations
  static RetryConfig networkRetryConfig() {
    return RetryConfig(
      maxRetries: 3,
      baseDelay: const Duration(seconds: 2),
      maxDelay: const Duration(minutes: 2),
      shouldRetry: (error) {
        if (error is NetworkException) {
          return error.isRetryable;
        }
        return false;
      },
    );
  }

  /// Create a retry configuration for storage operations
  static RetryConfig storageRetryConfig() {
    return RetryConfig(
      maxRetries: 2,
      baseDelay: const Duration(seconds: 1),
      maxDelay: const Duration(seconds: 30),
      shouldRetry: (error) {
        if (error is StorageException) {
          return error.isRetryable;
        }
        return false;
      },
    );
  }

  /// Create a retry configuration for upload operations
  static RetryConfig uploadRetryConfig() {
    return RetryConfig(
      maxRetries: 3,
      baseDelay: const Duration(seconds: 3),
      maxDelay: const Duration(minutes: 3),
      shouldRetry: (error) {
        if (error is NetworkException) {
          // Retry on server errors and timeouts, but not on client errors (except rate limiting)
          return error.code == 'SERVER_ERROR' || 
                 error.code == 'TIMEOUT' || 
                 error.code == 'NO_CONNECTION' ||
                 error.code == 'RATE_LIMITED';
        }
        if (error is StorageException) {
          // Retry on upload failures but not on validation errors
          return error.code == 'UPLOAD_FAILED';
        }
        return false;
      },
    );
  }

  /// Execute with specific retry configuration
  static Future<T> executeWithConfig<T>(
    Future<T> Function() operation,
    RetryConfig config,
  ) async {
    return executeWithRetry(
      operation,
      maxRetries: config.maxRetries,
      baseDelay: config.baseDelay,
      maxDelay: config.maxDelay,
      shouldRetry: config.shouldRetry,
    );
  }
}

/// Configuration class for retry behavior
class RetryConfig {
  final int maxRetries;
  final Duration baseDelay;
  final Duration maxDelay;
  final bool Function(dynamic error)? shouldRetry;

  const RetryConfig({
    required this.maxRetries,
    required this.baseDelay,
    required this.maxDelay,
    this.shouldRetry,
  });
}

/// Utility class for creating common retry scenarios
class RetryScenarios {
  /// Quick retry for immediate operations (UI feedback)
  static const RetryConfig quick = RetryConfig(
    maxRetries: 1,
    baseDelay: Duration(milliseconds: 500),
    maxDelay: Duration(seconds: 2),
  );

  /// Standard retry for normal operations
  static const RetryConfig standard = RetryConfig(
    maxRetries: 3,
    baseDelay: Duration(seconds: 1),
    maxDelay: Duration(seconds: 30),
  );

  /// Aggressive retry for critical operations
  static const RetryConfig aggressive = RetryConfig(
    maxRetries: 5,
    baseDelay: Duration(seconds: 2),
    maxDelay: Duration(minutes: 5),
  );

  /// Conservative retry for expensive operations
  static const RetryConfig conservative = RetryConfig(
    maxRetries: 2,
    baseDelay: Duration(seconds: 5),
    maxDelay: Duration(minutes: 1),
  );
}

/// Extension methods for easier retry usage
extension RetryExtension<T> on Future<T> {
  /// Add retry capability to any Future
  Future<T> withRetry({
    int maxRetries = RetryService.defaultMaxRetries,
    Duration baseDelay = RetryService.defaultBaseDelay,
    Duration maxDelay = RetryService.defaultMaxDelay,
    bool Function(dynamic error)? shouldRetry,
  }) {
    return RetryService.executeWithRetry(
      () => this,
      maxRetries: maxRetries,
      baseDelay: baseDelay,
      maxDelay: maxDelay,
      shouldRetry: shouldRetry,
    );
  }

  /// Add retry with predefined configuration
  Future<T> withRetryConfig(RetryConfig config) {
    return RetryService.executeWithConfig(() => this, config);
  }

  /// Add network-specific retry
  Future<T> withNetworkRetry() {
    return withRetryConfig(RetryService.networkRetryConfig());
  }

  /// Add storage-specific retry
  Future<T> withStorageRetry() {
    return withRetryConfig(RetryService.storageRetryConfig());
  }

  /// Add upload-specific retry
  Future<T> withUploadRetry() {
    return withRetryConfig(RetryService.uploadRetryConfig());
  }
}
