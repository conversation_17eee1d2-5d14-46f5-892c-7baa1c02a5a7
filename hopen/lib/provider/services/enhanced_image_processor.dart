import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;

import '../exceptions/storage_exception.dart';

/// Enhanced image processor following industry best practices
class EnhancedImageProcessor {
  static const int maxFileSizeBytes = 10 * 1024 * 1024; // 10MB
  static const int minImageDimension = 100;
  static const int maxImageDimension = 4096;
  static const int targetDimension = 1440; // Standard profile picture size
  static const List<String> allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> allowedMimeTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp'
  ];

  /// Pick image from gallery with comprehensive validation
  static Future<XFile?> pickFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: targetDimension.toDouble(),
        maxHeight: targetDimension.toDouble(),
        imageQuality: 90, // Initial quality for picker
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image == null) return null;

      // Validate the selected image
      await _validateImage(image);
      
      return image;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to select image from gallery: $e',
      );
    }
  }

  /// Take photo with camera with comprehensive validation
  static Future<XFile?> takePhoto() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: targetDimension.toDouble(),
        maxHeight: targetDimension.toDouble(),
        imageQuality: 90, // Initial quality for picker
        preferredCameraDevice: CameraDevice.front, // Front camera for selfies
      );

      if (image == null) return null;

      // Validate the captured image
      await _validateImage(image);
      
      return image;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to capture photo: $e',
      );
    }
  }

  /// Comprehensive image validation
  static Future<void> _validateImage(XFile imageFile) async {
    // Check file size
    final fileSize = await imageFile.length();
    if (fileSize > maxFileSizeBytes) {
      throw StorageException.fileTooLarge(
        maxSizeBytes: maxFileSizeBytes,
        actualSizeBytes: fileSize,
      );
    }

    // Check file extension
    final extension = path.extension(imageFile.path).toLowerCase();
    final cleanExtension = extension.startsWith('.') ? extension.substring(1) : extension;
    if (!allowedExtensions.contains(cleanExtension)) {
      throw StorageException.invalidFormat(
        actualFormat: cleanExtension,
        allowedFormats: allowedExtensions,
      );
    }

    // Read file and validate image format
    final imageBytes = await imageFile.readAsBytes();
    final image = img.decodeImage(imageBytes);
    
    if (image == null) {
      throw StorageException.corruptedFile();
    }

    // Check image dimensions
    final width = image.width;
    final height = image.height;
    
    if (width < minImageDimension || height < minImageDimension) {
      throw StorageException.invalidDimensions(
        width: width,
        height: height,
        minDimension: minImageDimension,
        maxDimension: maxImageDimension,
      );
    }

    if (width > maxImageDimension || height > maxImageDimension) {
      throw StorageException.invalidDimensions(
        width: width,
        height: height,
        minDimension: minImageDimension,
        maxDimension: maxImageDimension,
      );
    }
  }

  /// Process image with compression and format standardization
  static Future<Uint8List> processImage(XFile imageFile) async {
    try {
      // Read image bytes
      final imageBytes = await imageFile.readAsBytes();
      final originalSize = imageBytes.length;
      
      // Decode image
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw StorageException.corruptedFile();
      }

      // Process image (crop to square and resize)
      final processedImage = _cropAndResize(image);

      // Remove EXIF data for privacy
      processedImage.exif.clear();

      // Determine compression quality based on original file size
      final quality = _calculateCompressionQuality(originalSize);

      // Encode as JPEG (backend will convert to WebP)
      final processedBytes = Uint8List.fromList(
        img.encodeJpg(processedImage, quality: quality)
      );

      print('📊 Image processing: ${(originalSize / 1024 / 1024).toStringAsFixed(2)}MB → '
            '${(processedBytes.length / 1024 / 1024).toStringAsFixed(2)}MB (${quality}% quality)');

      return processedBytes;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to process image: $e',
      );
    }
  }

  /// Crop image to square and resize to target dimension
  static img.Image _cropAndResize(img.Image image) {
    // Determine the size for square crop (use the smaller dimension)
    final cropSize = image.width < image.height ? image.width : image.height;
    
    // Calculate crop position to center the crop
    final cropX = (image.width - cropSize) ~/ 2;
    final cropY = (image.height - cropSize) ~/ 2;
    
    // Crop to square
    var processedImage = img.copyCrop(
      image,
      x: cropX,
      y: cropY,
      width: cropSize,
      height: cropSize,
    );
    
    // Resize to target dimension if necessary
    if (cropSize != targetDimension) {
      processedImage = img.copyResize(
        processedImage,
        width: targetDimension,
        height: targetDimension,
        interpolation: img.Interpolation.cubic, // High quality interpolation
      );
    }
    
    return processedImage;
  }

  /// Calculate compression quality based on file size following the pipeline requirements
  static int _calculateCompressionQuality(int fileSizeBytes) {
    const oneMB = 1024 * 1024;
    const twoMB = 2 * 1024 * 1024;
    
    if (fileSizeBytes < oneMB) {
      return 100; // No compression for files < 1MB
    } else if (fileSizeBytes <= twoMB) {
      return 90; // 90% quality for files 1-2MB
    } else {
      return 80; // 80% quality for files > 2MB
    }
  }

  /// Validate image dimensions for existing files
  static Future<bool> validateImageDimensions(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        throw StorageException.fileNotFound(fileName: path.basename(imagePath));
      }

      final imageBytes = await file.readAsBytes();
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        throw StorageException.corruptedFile();
      }

      final width = image.width;
      final height = image.height;
      
      if (width < minImageDimension || height < minImageDimension ||
          width > maxImageDimension || height > maxImageDimension) {
        throw StorageException.invalidDimensions(
          width: width,
          height: height,
          minDimension: minImageDimension,
          maxDimension: maxImageDimension,
        );
      }

      return true;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to validate image dimensions: $e',
      );
    }
  }

  /// Get image info without processing
  static Future<ImageInfo> getImageInfo(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        throw StorageException.fileNotFound(fileName: path.basename(imagePath));
      }

      final imageBytes = await file.readAsBytes();
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        throw StorageException.corruptedFile();
      }

      return ImageInfo(
        width: image.width,
        height: image.height,
        fileSizeBytes: imageBytes.length,
        format: _detectImageFormat(imagePath),
      );
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to get image info: $e',
      );
    }
  }

  /// Detect image format from file extension
  static String _detectImageFormat(String imagePath) {
    final extension = path.extension(imagePath).toLowerCase();
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'JPEG';
      case '.png':
        return 'PNG';
      case '.webp':
        return 'WebP';
      default:
        return 'Unknown';
    }
  }
}

/// Image information class
class ImageInfo {
  final int width;
  final int height;
  final int fileSizeBytes;
  final String format;

  const ImageInfo({
    required this.width,
    required this.height,
    required this.fileSizeBytes,
    required this.format,
  });

  double get fileSizeMB => fileSizeBytes / (1024 * 1024);
  
  bool get isSquare => width == height;
  
  bool get isValidDimensions => 
      width >= EnhancedImageProcessor.minImageDimension &&
      height >= EnhancedImageProcessor.minImageDimension &&
      width <= EnhancedImageProcessor.maxImageDimension &&
      height <= EnhancedImageProcessor.maxImageDimension;
}
