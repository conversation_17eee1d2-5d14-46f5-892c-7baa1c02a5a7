import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../../statefulbusinesslogic/core/services/dialog_service.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../../di/injection_container_refactored.dart' as di;

/// Comprehensive FCM Service for push notifications
/// Handles Firebase Cloud Messaging integration with local notifications
class FcmService {
  factory FcmService() => _instance;
  FcmService._internal();
  static final FcmService _instance = FcmService._internal();

  // Firebase Messaging instance
  FirebaseMessaging? _firebaseMessaging;
  
  // Local notifications plugin
  final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  
  // Stream controllers for different message types
  final StreamController<Map<String, dynamic>> _messageStreamController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _notificationOpenedController =
      StreamController.broadcast();
  
  // State tracking
  bool _isInitialized = false;
  String? _fcmToken;
  
  // Cached backend API instance for token (optional)
  HttpRemoteDataSource? _apiService;

  // Dialog service for immediate request handling
  DialogService? _dialogService;

  // Notification channels
  static const String _channelId = 'hopen_notifications';
  static const String _channelName = 'Hopen Notifications';
  static const String _channelDescription = 'General notifications for Hopen app';

  static const String _requestChannelId = 'hopen_requests';
  static const String _requestChannelName = 'Request Notifications';
  static const String _requestChannelDescription = 'Contact and bubble request notifications';

  static const String _callChannelId = 'hopen_calls';
  static const String _callChannelName = 'Incoming Calls';
  static const String _callChannelDescription = 'Incoming call notifications';

  // Getters
  Stream<Map<String, dynamic>> get messageStream => _messageStreamController.stream;
  Stream<Map<String, dynamic>> get notificationOpenedStream => _notificationOpenedController.stream;
  String? get fcmToken => _fcmToken;
  bool get isInitialized => _isInitialized;

  /// Initialize FCM service
  Future<void> initialize({HttpRemoteDataSource? apiService}) async {
    if (_isInitialized) {
      LoggingService.error('FCM Service already initialized');
      return;
    }

    try {
      // Initialize Firebase if not already done
      await Firebase.initializeApp();
      
      // Initialize Firebase Messaging
      _firebaseMessaging = FirebaseMessaging.instance;
      
      // Request permissions
      await _requestPermissions();
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Configure message handling
      await _configureMessageHandling();
      
      // Get FCM token
      await _getFCMToken();
      
      // Cache apiService for later token refresh events
      _apiService = apiService;
      
      // Register token with backend if API service provided
      if (_apiService != null && _fcmToken != null) {
        await _registerTokenWithBackend(_apiService!);
      }

      // Initialize dialog service for request handling
      try {
        _dialogService = di.sl<DialogService>();
        LoggingService.info('FCM Service: Dialog service initialized for request handling');
      } catch (e) {
        LoggingService.warning('FCM Service: Dialog service not available: $e');
      }

      _isInitialized = true;
      LoggingService.info('FCM Service initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.error('Failed to initialize FCM Service: $e');
      LoggingService.error('StackTrace: $stackTrace');
      // Don't throw - allow app to continue without FCM
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    if (_firebaseMessaging == null) return;

    // Request FCM permissions
    final settings = await _firebaseMessaging!.requestPermission(
      
    );

    LoggingService.error('FCM Permission status: ${settings.authorizationStatus}');

    // Request local notification permissions for Android 13+
    if (Platform.isAndroid) {
      final status = await Permission.notification.request();
      LoggingService.error('Local notification permission: $status');
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    // Android initialization
    const initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS initialization
    const initializationSettingsIOS =
        DarwinInitializationSettings(
      
    );

    // Combined initialization
    const initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    // General notifications channel
    const generalChannel = AndroidNotificationChannel(
      _channelId,
      _channelName,
      description: _channelDescription,
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );

    // Request notifications channel
    const requestChannel = AndroidNotificationChannel(
      _requestChannelId,
      _requestChannelName,
      description: _requestChannelDescription,
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
      enableLights: true,
      enableVibration: true,
    );

    // Call notifications channel
    const callChannel = AndroidNotificationChannel(
      _callChannelId,
      _callChannelName,
      description: _callChannelDescription,
      importance: Importance.max,
      sound: RawResourceAndroidNotificationSound('ringtone'),
      enableLights: true,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(generalChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(requestChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(callChannel);
  }

  /// Configure message handling
  Future<void> _configureMessageHandling() async {
    if (_firebaseMessaging == null) return;

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background message opening
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // Handle app launch from terminated state
    final initialMessage = await _firebaseMessaging!.getInitialMessage();
    if (initialMessage != null) {
      _handleMessageOpenedApp(initialMessage);
    }

    // Configure background message handler
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  /// Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    LoggingService.error('Received foreground message: ${message.messageId}');
    
    // Add to stream for listeners
    _messageStreamController.add({
      'messageId': message.messageId,
      'data': message.data,
      'notification': message.notification?.toMap(),
      'type': 'foreground',
    });

    // Show local notification if app is in foreground
    await _showLocalNotification(message);
  }

  /// Handle message when app is opened
  void _handleMessageOpenedApp(RemoteMessage message) {
    LoggingService.info('App opened from notification: ${message.messageId}');

    // Handle request notifications with immediate dialog display
    final action = message.data['action'];
    if (action == 'show_request_dialog' && _dialogService != null) {
      _handleRequestNotificationDeepLink(message.data);
    }

    _notificationOpenedController.add({
      'messageId': message.messageId,
      'data': message.data,
      'notification': message.notification?.toMap(),
      'type': 'opened_app',
    });
  }

  /// Handle request notification deep linking
  void _handleRequestNotificationDeepLink(Map<String, dynamic> data) {
    try {
      final requestType = data['type'];
      if (requestType == null) return;

      LoggingService.info('FCM Service: Handling request deep link for type: $requestType');

      // Parse timestamp
      DateTime? timestamp;
      if (data['created_at'] != null) {
        try {
          timestamp = DateTime.parse(data['created_at']);
        } catch (e) {
          timestamp = DateTime.now();
        }
      } else {
        timestamp = DateTime.now();
      }

      // Show appropriate dialog based on request type
      switch (requestType) {
        case 'contact_request':
          _dialogService!.showContactRequest(contactData: {
            'requestId': data['notification_id'] ?? '',
            'requesterId': data['requester_id'] ?? '',
            'requesterName': data['requester_name'] ?? 'Unknown User',
            'requesterUsername': data['requester_username'],
            'requesterProfilePicUrl': data['requester_avatar_url'],
            'requestTimestamp': timestamp,
            'message': data['message'],
          });
          break;

        case 'bubble_join_request':
          _dialogService!.showBubbleJoinRequest(
            requesterId: data['requester_id'] ?? '',
            requesterName: data['requester_name'] ?? 'Unknown User',
            requesterUsername: data['requester_username'],
            requesterProfilePicUrl: data['requester_avatar_url'],
            requestTimestamp: timestamp,
            bubbleId: data['bubble_id'] ?? '',
            bubbleName: data['bubble_name'] ?? 'Unknown Bubble',
          );
          break;

        case 'bubble_invite_request':
          _dialogService!.showBubbleInviteRequest(
            inviterId: data['requester_id'] ?? '',
            inviterName: data['requester_name'] ?? 'Unknown User',
            inviterUsername: data['requester_username'],
            inviterProfilePicUrl: data['requester_avatar_url'],
            inviteTimestamp: timestamp,
            bubbleId: data['bubble_id'] ?? '',
            bubbleName: data['bubble_name'] ?? 'Unknown Bubble',
          );
          break;

        // Add other request types as needed
        default:
          LoggingService.warning('FCM Service: Unknown request type for deep link: $requestType');
      }

    } catch (e, stackTrace) {
      LoggingService.error('FCM Service: Error handling request deep link: $e', stackTrace: stackTrace);
    }
  }

  /// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    if (notification == null) return;

    // Determine notification type
    final channelId = message.data['type'] == 'call' ? _callChannelId : _channelId;
    
    // Android notification details
    final androidDetails = AndroidNotificationDetails(
      channelId,
      channelId == _callChannelId ? _callChannelName : _channelName,
      channelDescription: channelId == _callChannelId ? _callChannelDescription : _channelDescription,
      importance: channelId == _callChannelId ? Importance.max : Importance.high,
      priority: channelId == _callChannelId ? Priority.max : Priority.high,
      when: DateTime.now().millisecondsSinceEpoch,
      fullScreenIntent: channelId == _callChannelId,
      category: AndroidNotificationCategory.call,
      ongoing: channelId == _callChannelId,
      autoCancel: channelId != _callChannelId,
    );

    // iOS notification details
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      interruptionLevel: InterruptionLevel.active,
    );

    final notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Show notification
    await _localNotifications.show(
      message.hashCode,
      notification.title,
      notification.body,
      notificationDetails,
      payload: jsonEncode(message.data),
    );
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    final payload = notificationResponse.payload;
    if (payload != null) {
      try {
        final Map<String, dynamic> data = jsonDecode(payload) as Map<String, dynamic>;
        _notificationOpenedController.add({
          'data': data,
          'type': 'local_notification_tapped',
        });
      } catch (e) {
        LoggingService.error('Error parsing notification payload: $e');
      }
    }
  }

  /// Get FCM token
  Future<void> _getFCMToken() async {
    if (_firebaseMessaging == null) return;

    try {
      _fcmToken = await _firebaseMessaging!.getToken();
      LoggingService.error('FCM Token: $_fcmToken');

      // Listen for token refresh
      _firebaseMessaging!.onTokenRefresh.listen((newToken) async {
        _fcmToken = newToken;
        LoggingService.error('FCM Token refreshed: $newToken');
        if (_apiService != null) {
          await _registerTokenWithBackend(_apiService!);
        }
      });
    } catch (e) {
      LoggingService.error('Error getting FCM token: $e');
    }
  }

  /// Register FCM token with backend
  Future<void> _registerTokenWithBackend(HttpRemoteDataSource apiService) async {
    if (_fcmToken == null) return;

    try {
      await apiService.registerFCMToken(_fcmToken!);
      LoggingService.info('FCM token registered with backend');
    } catch (e) {
      LoggingService.error('Error registering FCM token with backend: $e');
    }
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    if (_firebaseMessaging == null || !_isInitialized) return;

    try {
      await _firebaseMessaging!.subscribeToTopic(topic);
      LoggingService.error('Subscribed to topic: $topic');
    } catch (e) {
      LoggingService.error('Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    if (_firebaseMessaging == null || !_isInitialized) return;

    try {
      await _firebaseMessaging!.unsubscribeFromTopic(topic);
      LoggingService.error('Unsubscribed from topic: $topic');
    } catch (e) {
      LoggingService.error('Error unsubscribing from topic $topic: $e');
    }
  }

  /// Send notification to user (via backend)
  Future<void> sendToUser(
    String userId,
    String title,
    String body, {
    Map<String, dynamic>? data,
    HttpRemoteDataSource? apiService,
  }) async {
    if (apiService == null) {
      throw Exception('API service required to send notifications');
    }

    try {
      // This would call a backend endpoint that sends FCM notifications
      // Implementation depends on your backend API structure
      LoggingService.error('Sending notification to user $userId: $title - $body');
    } catch (e) {
      throw ServerException(message: 'Failed to send notification: $e');
    }
  }

  /// Send notification to bubble members (via backend)
  Future<void> sendToBubble(
    String bubbleId,
    String title,
    String body, {
    Map<String, dynamic>? data,
    HttpRemoteDataSource? apiService,
  }) async {
    if (apiService == null) {
      throw Exception('API service required to send notifications');
    }

    try {
      // This would call a backend endpoint that sends FCM notifications to bubble members
      LoggingService.error('Sending notification to bubble $bubbleId: $title - $body');
    } catch (e) {
      throw ServerException(message: 'Failed to send bubble notification: $e');
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// Clear specific notification
  Future<void> clearNotification(int notificationId) async {
    await _localNotifications.cancel(notificationId);
  }

  /// Update FCM token with backend
  Future<void> updateTokenWithBackend(HttpRemoteDataSource apiService) async {
    await _registerTokenWithBackend(apiService);
  }

  /// Dispose resources
  Future<void> dispose() async {
    await _messageStreamController.close();
    await _notificationOpenedController.close();
    _isInitialized = false;
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase if needed
  await Firebase.initializeApp();
  
  LoggingService.error('Handling background message: ${message.messageId}');
  
  // Handle background message logic here
  // Note: This function runs in a separate isolate
} 