import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorageService {
  SharedPreferences? _preferences;
  bool _isInitialized = false;

  // Public constructor for GetIt
  LocalStorageService();

  // Async initialization method
  Future<void> init() async {
    if (!_isInitialized) {
      _preferences = await SharedPreferences.getInstance();
      _isInitialized = true;
    }
  }

  // Ensure initialized before any operation
  void _ensureInitialized() {
    if (!_isInitialized || _preferences == null) {
      throw StateError('LocalStorageService not initialized. Call init() first.');
    }
  }

  // Static method for backward compatibility (deprecated)
  @Deprecated('Use dependency injection instead')
  static Future<LocalStorageService> getInstance() async {
    final service = LocalStorageService();
    await service.init();
    return service;
  }

  Future<void> saveString(String key, String value) async {
    _ensureInitialized();
    await _preferences!.setString(key, value);
  }

  Future<String?> getString(String key) async {
    _ensureInitialized();
    return _preferences!.getString(key);
  }

  Future<void> saveBool(String key, bool value) async {
    _ensureInitialized();
    await _preferences!.setBool(key, value);
  }

  Future<bool?> getBool(String key) async {
    _ensureInitialized();
    return _preferences!.getBool(key);
  }

  Future<void> saveInt(String key, int value) async {
    _ensureInitialized();
    await _preferences!.setInt(key, value);
  }

  Future<int?> getInt(String key) async {
    _ensureInitialized();
    return _preferences!.getInt(key);
  }

  Future<void> saveDouble(String key, double value) async {
    _ensureInitialized();
    await _preferences!.setDouble(key, value);
  }

  Future<double?> getDouble(String key) async {
    _ensureInitialized();
    return _preferences!.getDouble(key);
  }

  Future<void> remove(String key) async {
    _ensureInitialized();
    await _preferences!.remove(key);
  }

  Future<void> clear() async {
    _ensureInitialized();
    await _preferences!.clear();
  }

  Future<bool> containsKey(String key) async {
    _ensureInitialized();
    return _preferences!.containsKey(key);
  }

  Future<List<String>> getAllKeys() async {
    _ensureInitialized();
    return _preferences!.getKeys().toList();
  }

  Future<void> setList(String key, List<Map<String, dynamic>> value) async {
    _ensureInitialized();
    final stringList = value.map((item) => jsonEncode(item)).toList();
    await _preferences!.setStringList(key, stringList);
  }

  Future<List<Map<String, dynamic>>> getList(String key) async {
    _ensureInitialized();
    final stringList = _preferences!.getStringList(key) ?? [];
    return stringList.map((item) {
      try {
        return jsonDecode(item) as Map<String, dynamic>;
      } catch (e) {
        return <String, dynamic>{};
      }
    }).toList();
  }
}
