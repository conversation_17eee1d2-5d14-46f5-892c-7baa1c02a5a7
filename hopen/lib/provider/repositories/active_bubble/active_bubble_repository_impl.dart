import '../../../repositories/active_bubble/active_bubble_repository.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/error/failures.dart';
import '../../../statefulbusinesslogic/core/error/base_error.dart';
import '../../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../datasources/http_remote_datasource.dart';

/// Implementation of ActiveBubbleRepository using Go micro-services backend
class ActiveBubbleRepositoryImpl implements ActiveBubbleRepository {

  ActiveBubbleRepositoryImpl({
    required HttpRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;
  final HttpRemoteDataSource _remoteDataSource;

  @override
  Future<Result<BubbleEntity?>> getActiveBubble(String userId) async {
    try {
      final response = await _remoteDataSource.get('/active-bubble/user/$userId');
      
      if (response['bubble'] != null) {
        final bubbleResult = BubbleEntity.fromJson(response['bubble'] as Map<String, dynamic>);
        return bubbleResult.fold(
          onSuccess: Result.success,
          onFailure: (error) => Result.failure(UnexpectedFailure(message: error.toString())),
        );
      }
      
      return Result.success(null);
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to get active bubble: $e'));
    }
  }

  @override
  Future<Result<BubbleEntity>> updateBubbleStatus(String bubbleId, BubbleLifecycleStatus status) async {
    try {
      final response = await _remoteDataSource.put('/active-bubble/$bubbleId/status', {
        'status': status.toString().split('.').last,
      });
      
      final bubbleResult = BubbleEntity.fromJson(response['bubble'] as Map<String, dynamic>);
      return bubbleResult.fold(
        onSuccess: Result.success,
        onFailure: (error) => Result.failure(UnexpectedFailure(message: error.toString())),
      );
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to update bubble status: $e'));
    }
  }

  @override
  Stream<BubbleEntity> getBubbleUpdates(String bubbleId) {
    // This would typically connect to a WebSocket or Server-Sent Events stream
    // For now, return an empty stream as a placeholder
    return const Stream.empty();
  }

  @override
  Future<Result<void>> updateMemberOnlineStatus(String bubbleId, String userId, bool isOnline) async {
    try {
      await _remoteDataSource.put('/active-bubble/$bubbleId/member/$userId/online-status', {
        'isOnline': isOnline,
      });
      return Result.success(null);
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to update member online status: $e'));
    }
  }

  @override
  Future<Result<int>> getOnlineMembersCount(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/active-bubble/$bubbleId/online-members-count');
      return Result.success(response['count'] as int? ?? 0);
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to get online members count: $e'));
    }
  }

  @override
  Future<Result<List<Map<String, dynamic>>>> getBubbleActivity(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/active-bubble/$bubbleId/activity');
      return Result.success(List<Map<String, dynamic>>.from(response['activities'] as List<dynamic>? ?? []));
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to get bubble activity: ${e.toString()}'));
    }
  }

  @override
  Future<Result<BubbleEntity>> updateBubbleCountdown(String bubbleId, DateTime newEndTime) async {
    try {
      final response = await _remoteDataSource.put('/active-bubble/$bubbleId/countdown', {
        'newEndTime': newEndTime.toIso8601String(),
      });
      
      final bubbleResult = BubbleEntity.fromJson(response['bubble'] as Map<String, dynamic>);
      return bubbleResult.fold(
        onSuccess: Result.success,
        onFailure: (error) => Result.failure(UnexpectedFailure(message: error.toString())),
      );
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to update bubble countdown: $e'));
    }
  }

  @override
  Future<Result<BubbleEntity>> extendBubbleDuration(String bubbleId, Duration extension) async {
    try {
      final response = await _remoteDataSource.put('/active-bubble/$bubbleId/extend-duration', {
        'extensionMinutes': extension.inMinutes,
      });
      
      final bubbleResult = BubbleEntity.fromJson(response['bubble'] as Map<String, dynamic>);
      return bubbleResult.fold(
        onSuccess: Result.success,
        onFailure: (error) => Result.failure(UnexpectedFailure(message: error.toString())),
      );
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to extend bubble duration: ${e.toString()}'));
    }
  }

  @override
  Future<Result<Duration?>> getBubbleTimeRemaining(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/active-bubble/$bubbleId/time-remaining');
      final minutes = response['remainingMinutes'] as int?;
      if (minutes != null) {
        return Result.success(Duration(minutes: minutes));
      }
      return Result.success(null);
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to get bubble time remaining: $e'));
    }
  }

  @override
  Future<Result<bool>> isBubbleAboutToExpire(String bubbleId, {Duration threshold = const Duration(minutes: 30)}) async {
    try {
      final response = await _remoteDataSource.get('/active-bubble/$bubbleId/about-to-expire?thresholdMinutes=${threshold.inMinutes}');
      return Result.success(response['aboutToExpire'] as bool? ?? false);
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to check if bubble is about to expire: $e'));
    }
  }

  @override
  Future<Result<Map<String, bool>>> getMemberActivityStatus(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/active-bubble/$bubbleId/member-activity');
      return Result.success(Map<String, bool>.from(response['memberActivity'] as Map<dynamic, dynamic>? ?? {}));
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to get member activity status: $e'));
    }
  }

  @override
  Future<Result<void>> updateMemberLastSeen(String bubbleId, String userId) async {
    try {
      await _remoteDataSource.put('/active-bubble/$bubbleId/member/$userId/last-seen', {
        'lastSeen': DateTime.now().toIso8601String(),
      });
      return Result.success(null);
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to update member last seen: $e'));
    }
  }

  @override
  Future<Result<Map<String, dynamic>>> getBubbleEngagementMetrics(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get('/active-bubble/$bubbleId/engagement-metrics');
      return Result.success(Map<String, dynamic>.from(response['metrics'] as Map<dynamic, dynamic>? ?? {}));
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: 'Failed to get bubble engagement metrics: $e'));
    }
  }

  @override
  Future<Result<BubbleEntity?>> refreshActiveBubble(String userId) async {
    // Delegate to getActiveBubble for refresh
    return getActiveBubble(userId);
  }
} 