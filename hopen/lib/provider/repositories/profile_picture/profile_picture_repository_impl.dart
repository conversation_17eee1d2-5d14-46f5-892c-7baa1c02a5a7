import 'dart:io';
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

import '../../../repositories/profile_picture/profile_picture_repository.dart';
import '../../../statefulbusinesslogic/core/models/profile_picture_result.dart';
import '../../config/minio_config.dart';
import '../../services/storage/storage_service.dart';
import '../../services/api/http_api_service.dart';
import '../../services/image_processing_service.dart';
import '../../services/retry/retry_service.dart';
import '../../exceptions/storage_exception.dart';
import '../../models/api_models.dart';

/// Implementation of ProfilePictureRepository in the provider layer
/// This handles the actual image processing and storage operations
class ProfilePictureRepositoryImpl implements ProfilePictureRepository {
  ProfilePictureRepositoryImpl({
    required StorageService storageService,
    required HttpApiService apiService,
    ImagePicker? imagePicker,
  }) : _storageService = storageService,
       _apiService = apiService,
       _imagePicker = imagePicker ?? ImagePicker();

  final StorageService _storageService;
  final HttpApiService _apiService;
  final ImagePicker _imagePicker;
  bool _isStorageInitialized = false;

  // Constants from pipeline requirements
  static const int minResolution = 100; // Minimum dimension for quality
  static const int maxResolution = 4096; // Maximum dimension before backend resize
  static const int maxFileSizeBytes = 2 * 1024 * 1024; // 2MB as per pipeline specs
  static const double compressionQuality = 90; // Quality for 1-2MB files
  static const List<String> allowedFormats = ['jpg', 'jpeg', 'png', 'webp'];
  static const String outputFormat = 'jpg'; // JPEG for upload, backend converts to WebP

  /// Ensure storage service is properly initialized
  Future<void> _ensureStorageInitialized() async {
    if (_isStorageInitialized) return;
    
    try {
      await _storageService.initialize(
        endpoint: MinioConfig.endpoint,
        port: MinioConfig.port,
        accessKey: MinioConfig.accessKey,
        secretKey: MinioConfig.secretKey,
        useSSL: MinioConfig.useSSL,
      );
      _isStorageInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize storage service: $e');
    }
  }

  @override
  Future<ProfilePictureResult> pickFromGallery() async {
    try {
      // Use consolidated image processing service with comprehensive validation
      final pickedFile = await ImageProcessingService.pickFromGallery();

      if (pickedFile == null) {
        return ProfilePictureResult.cancelled();
      }

      return await _processImageFileEnhanced(pickedFile);
    } on StorageException catch (e) {
      return ProfilePictureResult.error(e.message);
    } on NetworkException catch (e) {
      return ProfilePictureResult.error(e.message);
    } catch (e) {
      return ProfilePictureResult.error('Failed to pick image from gallery: $e');
    }
  }

  @override
  Future<ProfilePictureResult> takePhoto() async {
    try {
      // Use consolidated image processing service with comprehensive validation
      final pickedFile = await ImageProcessingService.takePhoto();

      if (pickedFile == null) {
        return ProfilePictureResult.cancelled();
      }

      return await _processImageFileEnhanced(pickedFile);
    } on StorageException catch (e) {
      return ProfilePictureResult.error(e.message);
    } on NetworkException catch (e) {
      return ProfilePictureResult.error(e.message);
    } catch (e) {
      return ProfilePictureResult.error('Failed to take photo: $e');
    }
  }

  @override
  Future<bool> removeProfilePicture(String imageUrl) async {
    try {
      await _ensureStorageInitialized();
      
      // Extract filename from URL and delete from storage
      final uri = Uri.parse(imageUrl);
      final fileName = path.basename(uri.path);
      
      return await _storageService.deleteFile(fileName);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<ProfilePictureValidationResult> validateImage(String imagePath) async {
    try {
      final file = File(imagePath);
      
      // Check if file exists
      if (!await file.exists()) {
        return ProfilePictureValidationResult.invalid('File does not exist');
      }

      // Check file size
      final fileSize = await file.length();
      if (fileSize > maxFileSizeBytes) {
        return ProfilePictureValidationResult.invalid(
          'File size too large. Maximum allowed: ${maxFileSizeBytes ~/ (1024 * 1024)}MB'
        );
      }

      // Check file format
      final extension = path.extension(imagePath).toLowerCase().replaceFirst('.', '');
      if (!allowedFormats.contains(extension)) {
        return ProfilePictureValidationResult.invalid(
          'Unsupported format. Allowed: ${allowedFormats.join(', ')}'
        );
      }

      // Validate image content and resolution
      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) {
        return ProfilePictureValidationResult.invalid('Invalid image file');
      }

      if (image.width < minResolution || image.height < minResolution) {
        return ProfilePictureValidationResult.invalid(
          'Image resolution too low. Minimum: ${minResolution}x$minResolution'
        );
      }

      return ProfilePictureValidationResult.valid();
    } catch (e) {
      return ProfilePictureValidationResult.invalid('Error validating image: $e');
    }
  }

  /// Process and upload an image file using enhanced processing with retry logic
  Future<ProfilePictureResult> _processImageFileEnhanced(XFile imageFile) async {
    final totalStopwatch = Stopwatch()..start();
    print('🚀 Starting enhanced profile picture upload process');

    try {
      await _ensureStorageInitialized();

      // Step 1: Process image using consolidated service with comprehensive validation
      final processingStopwatch = Stopwatch()..start();
      final originalSize = await imageFile.length();
      final originalSizeMB = originalSize / 1024 / 1024;

      print('🔄 Starting client-side image processing...');
      print('📁 Original image size: ${originalSizeMB.toStringAsFixed(2)}MB');

      // Log processing strategy
      if (originalSize < 1 * 1024 * 1024) {
        print('✨ Processing strategy: < 1MB → No compression, only format standardization');
      } else {
        print('🔧 Processing strategy: 1-2MB → 90% quality compression + format standardization');
      }
      print('🔄 Backend will resize to 1440x1440 and convert to WebP');

      // Process image with the consolidated service
      final processedBytes = await RetryService.executeStorageOperation(
        () => ImageProcessingService.processImageFromFile(imageFile),
        maxRetries: 2,
      );

      processingStopwatch.stop();
      print('⏱️ Client-side processing completed: ${processingStopwatch.elapsedMilliseconds}ms');
      print('📊 Client processing: ${originalSizeMB.toStringAsFixed(2)}MB → ${(processedBytes.length / 1024 / 1024).toStringAsFixed(2)}MB');

      // Step 2: Upload to storage with retry logic
      final uploadStopwatch = Stopwatch()..start();
      String? finalUrl;
      final fileName = '${const Uuid().v4()}.jpg'; // Use JPEG extension (backend converts to WebP)

      try {
        finalUrl = await RetryService.executeStorageOperation(
          () => _storageService.uploadData(processedBytes, '.jpg'),
          maxRetries: 3,
        );
      } catch (uploadError) {
        print('❌ Upload failed, storing locally for later upload: $uploadError');
        // Fallback to local storage for signup flow
        final tempDir = await Directory.systemTemp.createTemp('hopen_pp_');
        final localPath = path.join(tempDir.path, fileName);
        final localFile = File(localPath);
        await localFile.writeAsBytes(processedBytes);
        finalUrl = localFile.path; // Local path to be uploaded later
      }

      uploadStopwatch.stop();
      print('⏱️ Upload completed: ${uploadStopwatch.elapsedMilliseconds}ms');

      // Update backend with new URL if it's a remote URL (synchronous for persistence)
      if (finalUrl!.startsWith('http')) {
        final backendStopwatch = Stopwatch()..start();
        try {
          print('🔄 Updating user profile with new avatar URL: $finalUrl');
          final updateRequest = UpdateUserProfileRequest(profilePictureUrl: finalUrl);
          print('🔍 Update request: ${updateRequest.toJson()}');

          final updatedProfile = await RetryService.executeApiOperation(
            () => _apiService.updateUserProfile(updateRequest),
            maxRetries: 3,
          );

          backendStopwatch.stop();
          print('✅ Backend update completed in ${backendStopwatch.elapsedMilliseconds}ms');
          print('✅ Profile updated with avatar URL: ${updatedProfile.profilePictureUrl}');
        } catch (e) {
          backendStopwatch.stop();
          print('❌ Backend update failed after ${backendStopwatch.elapsedMilliseconds}ms: $e');
          // Don't fail the upload - the image was uploaded successfully
        }
      }

      totalStopwatch.stop();
      print('✅ Enhanced profile picture upload completed in ${totalStopwatch.elapsedMilliseconds}ms');

      return ProfilePictureResult.success(
        url: finalUrl,
        fileName: fileName,
        originalSize: ImageSize(800, 600), // Placeholder - actual size would be determined from image
        processedSize: ImageSize(1440, 1440), // Target size as per pipeline
      );
    } on StorageException catch (e) {
      totalStopwatch.stop();
      print('❌ Enhanced upload failed with StorageException: ${e.message}');
      return ProfilePictureResult.error(e.message);
    } on NetworkException catch (e) {
      totalStopwatch.stop();
      print('❌ Enhanced upload failed with NetworkException: ${e.message}');
      return ProfilePictureResult.error(e.message);
    } catch (e) {
      totalStopwatch.stop();
      print('❌ Enhanced upload failed with unexpected error: $e');
      return ProfilePictureResult.error('Failed to process profile picture: $e');
    }
  }

  /// Process and upload an image file with performance monitoring (legacy method)
  Future<ProfilePictureResult> _processImageFile(File imageFile) async {
    final totalStopwatch = Stopwatch()..start();
    print('🚀 Starting profile picture upload process');

    try {
      await _ensureStorageInitialized();

      // Step 1: Validate file format first
      final formatStopwatch = Stopwatch()..start();
      final extension = path.extension(imageFile.path).toLowerCase().replaceFirst('.', '');
      if (!allowedFormats.contains(extension)) {
        return ProfilePictureResult.error(
          'Unsupported format. Allowed: ${allowedFormats.join(', ')}',
        );
      }
      formatStopwatch.stop();
      print('⏱️ Format validation: ${formatStopwatch.elapsedMilliseconds}ms');

      // Step 2: Process image with AVIF using size-based compression strategy
      final processingStopwatch = Stopwatch()..start();
      final originalSize = await imageFile.length();
      final originalSizeMB = originalSize / 1024 / 1024;

      print('🔄 Starting image processing...');
      print('📁 Original image size: ${originalSizeMB.toStringAsFixed(2)}MB');

      // Log compression strategy that will be applied
      if (originalSize < 1 * 1024 * 1024) {
        print('✨ Compression strategy: < 1MB → NO COMPRESSION (100% quality)');
      } else if (originalSize <= 2 * 1024 * 1024) {
        print('🔧 Compression strategy: 1-2MB → 90% quality compression');
      } else {
        print('🔧 Compression strategy: > 2MB → 75% quality compression');
      }

      final processingResult = await ImageProcessingService.processImageInIsolate(
        imagePath: imageFile.path,
        minResolution: minResolution,
        maxResolution: maxResolution,
        maxFileSizeBytes: maxFileSizeBytes,
        compressionQuality: compressionQuality,
      );

      processingStopwatch.stop();
      print('⏱️ Image processing: ${processingStopwatch.elapsedMilliseconds}ms');

      if (!processingResult.isSuccess) {
        totalStopwatch.stop();
        print('❌ Upload failed after ${totalStopwatch.elapsedMilliseconds}ms: ${processingResult.errorMessage}');
        return ProfilePictureResult.error(processingResult.errorMessage!);
      }

      final processedBytes = processingResult.processedBytes!;

      // Get original file size for metrics
      final originalFileSize = await imageFile.length();
      print('📊 Size reduction: ${(originalFileSize / 1024 / 1024).toStringAsFixed(2)}MB → ${(processedBytes.length / 1024 / 1024).toStringAsFixed(2)}MB');

      // Step 3: Upload to storage with JPEG extension; backend converts to WebP
      final uploadStopwatch = Stopwatch()..start();
      String? finalUrl;
      final fileName = '${const Uuid().v4()}.jpg'; // Use JPEG extension (backend converts to WebP)

      print('☁️ Starting storage upload...');
      try {
        finalUrl = await _storageService.uploadData(processedBytes, '.jpg');
        uploadStopwatch.stop();
        print('⏱️ Storage upload: ${uploadStopwatch.elapsedMilliseconds}ms');
      } catch (e) {
        uploadStopwatch.stop();
        print('❌ Storage upload failed after ${uploadStopwatch.elapsedMilliseconds}ms: $e');
      }

      if (finalUrl == null) {
        print('💾 Falling back to local storage');
        final tempDir = await Directory.systemTemp.createTemp('hopen_pp_');
        final localPath = path.join(tempDir.path, fileName);
        final localFile = File(localPath);
        await localFile.writeAsBytes(processedBytes);
        finalUrl = localFile.path;
      }

      // Update backend with new URL if it's a remote URL (synchronous for persistence)
      if (finalUrl.startsWith('http')) {
        final backendStopwatch = Stopwatch()..start();
        try {
          print('🔄 Updating user profile with new avatar URL: $finalUrl');
          final updatedProfile = await _apiService.updateUserProfile(
            UpdateUserProfileRequest(profilePictureUrl: finalUrl),
          );
          backendStopwatch.stop();
          print('✅ Backend update completed in ${backendStopwatch.elapsedMilliseconds}ms');
          print('✅ Profile updated with avatar URL: ${updatedProfile.profilePictureUrl}');
        } catch (e) {
          backendStopwatch.stop();
          print('❌ Backend update failed after ${backendStopwatch.elapsedMilliseconds}ms: $e');
          // Don't fail the upload - the image was uploaded successfully
        }
      }

      totalStopwatch.stop();
      print('🎉 Profile picture upload completed in ${totalStopwatch.elapsedMilliseconds}ms');
      print('📊 Final URL: $finalUrl');

      return ProfilePictureResult.success(
        url: finalUrl,
        fileName: fileName,
        originalSize: ImageSize(maxResolution.toDouble(), maxResolution.toDouble()),
        processedSize: ImageSize(maxResolution.toDouble(), maxResolution.toDouble()),
      );
    } catch (e) {
      totalStopwatch.stop();
      print('❌ Upload failed after ${totalStopwatch.elapsedMilliseconds}ms: $e');
      return ProfilePictureResult.error('Failed to process image: $e');
    }
  }

  /// Update backend asynchronously (non-blocking)
  void _updateBackendAsync(String profilePictureUrl) {
    // Run in background without blocking UI
    Future.microtask(() async {
      final backendStopwatch = Stopwatch()..start();
      try {
        print('🔄 Updating user profile with new avatar URL: $profilePictureUrl');
        await _apiService.updateUserProfile(
          UpdateUserProfileRequest(profilePictureUrl: profilePictureUrl),
        );
        backendStopwatch.stop();
        print('✅ Backend update completed in ${backendStopwatch.elapsedMilliseconds}ms');
      } catch (e) {
        backendStopwatch.stop();
        print('❌ Backend update failed after ${backendStopwatch.elapsedMilliseconds}ms: $e');
      }
    });
  }

  /// Validate image specifications
  ProfilePictureValidationResult _validateImageSpecs(img.Image image) {
    if (image.width < minResolution || image.height < minResolution) {
      return ProfilePictureValidationResult.invalid(
        'Image resolution too low. Minimum: ${minResolution}x$minResolution',
      );
    }
    return ProfilePictureValidationResult.valid();
  }

  /// Process image: resize and crop to square
  img.Image _processImage(img.Image image) {
    // Determine the size for square crop (use the smaller dimension)
    final cropSize = image.width < image.height ? image.width : image.height;
    
    // Calculate crop position to center the crop
    final cropX = (image.width - cropSize) ~/ 2;
    final cropY = (image.height - cropSize) ~/ 2;
    
    // Crop to square
    var processedImage = img.copyCrop(image, 
      x: cropX, 
      y: cropY, 
      width: cropSize, 
      height: cropSize
    );
    
    // Resize if larger than max resolution
    if (cropSize > maxResolution) {
      processedImage = img.copyResize(processedImage, 
        width: maxResolution, 
        height: maxResolution
      );
    }
    
    return processedImage;
  }

  /// Encode image to bytes with compression
  List<int> _encodeImage(img.Image image, String originalExtension) {
    // Always encode as JPEG for consistency and better compression
    return img.encodeJpg(image, quality: compressionQuality.toInt());
  }

  /// Compress image with adaptive quality to meet target size requirements
  List<int> _compressImageToTargetSize(img.Image image, String originalExtension) {
    int quality = compressionQuality.toInt(); // Start with default quality (90)
    List<int> bytes;

    // Try different quality levels until we get under the size limit
    do {
      bytes = img.encodeJpg(image, quality: quality);

      // If we're under the limit, we're done
      if (bytes.length <= maxFileSizeBytes) {
        break;
      }

      // Reduce quality by 10 for next iteration
      quality -= 10;

      // Don't go below 30% quality to maintain reasonable image quality
      if (quality < 30) {
        // If even 30% quality is too large, resize the image further
        final smallerImage = img.copyResize(image,
          width: (image.width * 0.8).round(),
          height: (image.height * 0.8).round(),
        );
        bytes = img.encodeJpg(smallerImage, quality: 30);
        break;
      }
    } while (bytes.length > maxFileSizeBytes);

    return bytes;
  }

  @override
  Future<String?> uploadLocalProfilePicture(String localPath) async {
    try {
      print('📸 ProfilePictureRepository.uploadLocalProfilePicture: Starting upload for: $localPath');

      if (localPath.startsWith('http')) {
        print('🌐 ProfilePictureRepository.uploadLocalProfilePicture: Already a remote URL, returning as-is');
        return localPath;
      }

      final file = File(localPath);
      if (!await file.exists()) {
        print('❌ ProfilePictureRepository.uploadLocalProfilePicture: File does not exist: $localPath');
        return null;
      }

      print('📁 ProfilePictureRepository.uploadLocalProfilePicture: File exists, processing...');
      // Process and upload the image using the same logic as the main upload flow
      final result = await _processImageFile(file);

      if (result.isSuccess && result.url != null) {
        print('☁️ ProfilePictureRepository.uploadLocalProfilePicture: Upload successful, URL: ${result.url!}');

        // Update user profile with new URL
        try {
          print('🔄 ProfilePictureRepository.uploadLocalProfilePicture: Updating user profile with new avatar URL: ${result.url!}');
          await _apiService.updateUserProfile(
            UpdateUserProfileRequest(profilePictureUrl: result.url!),
          );
          print('✅ ProfilePictureRepository.uploadLocalProfilePicture: Successfully updated user profile with new avatar URL');
        } catch (e) {
          // Log error but still return the URL - the image was uploaded successfully
          print('❌ ProfilePictureRepository.uploadLocalProfilePicture: Failed to update user profile with new avatar URL: $e');
        }

        return result.url;
      } else {
        print('❌ ProfilePictureRepository.uploadLocalProfilePicture: Upload failed or returned null URL');
        if (!result.isSuccess) {
          print('❌ ProfilePictureRepository.uploadLocalProfilePicture: Error: ${result.error}');
        }
      }

      return null;
    } catch (e) {
      print('❌ ProfilePictureRepository.uploadLocalProfilePicture: Exception occurred: $e');
      return null;
    }
  }
}
