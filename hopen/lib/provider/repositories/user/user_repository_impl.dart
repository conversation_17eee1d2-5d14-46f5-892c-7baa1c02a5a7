import '../../../repositories/user/user_repository.dart';
import '../../../statefulbusinesslogic/core/models/bubble_membership_status.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../models/api_models.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/error/user_failure.dart';
import '../../../statefulbusinesslogic/core/error/base_error.dart';
import '../../../statefulbusinesslogic/core/error/failures.dart' as failures;

/// Real implementation of UserRepository using Go backend
class UserRepositoryImpl implements UserRepository {

  UserRepositoryImpl({
    required HttpRemoteDataSource httpDataSource,
  }) : _httpDataSource = httpDataSource;
  final HttpRemoteDataSource _httpDataSource;
  String? _currentUserId;

  @override
  String? get currentUserId => _currentUserId;

  @override
  Future<UserModel?> getCurrentUser() async {
    final result = await getCurrentUserSafe();
    return result is Success<UserModel> ? result.data : null;
  }

  @override
  Future<Result<UserModel>> getCurrentUserSafe() async {
    try {
      // Primary endpoint
      final apiUser = await _httpDataSource.getCurrentUserProfile();
      _currentUserId = apiUser.id;
      return Success(_mapApiUserToUserModel(apiUser));
    } catch (e) {
      LoggingService.error('Error getting current user via /auth/profile: $e');

      // Fallback to public user endpoint if we still have an id
      final fallbackUserId = _currentUserId;
      if (fallbackUserId != null) {
        try {
          final fallbackUser = await getUser(fallbackUserId);
          if (fallbackUser != null) {
            LoggingService.info('Successfully fetched current user via fallback /users/{id}');
            return Success(fallbackUser);
          }
        } catch (fallbackError) {
          LoggingService.error('Fallback getUser failed: $fallbackError');
        }
      }

      // Decide failure type (simple mapping for now)
      if (e.toString().contains('401')) {
        return Failure(failures.NotAuthenticatedFailure());
      }
      if (e.toString().contains('404')) {
        return Failure(failures.NotFoundFailure());
      }
      return Failure(failures.NetworkFailure(message: e.toString()));
    }
  }

  @override
  Future<UserModel?> getUser(String userId) async {
    try {
      final apiUser = await _httpDataSource.getUserProfile(userId);
      return _mapApiUserToUserModel(apiUser);
    } catch (e) {
      LoggingService.error('Error getting user $userId: $e');
      return null;
    }
  }

  @override
  Future<Map<String, dynamic>> getUserInfo(String userId) async {
    try {
      final user = await getUser(userId);
      if (user == null) {
        return {'id': userId, 'name': 'Unknown User', 'avatarUrl': null};
      }
      return {
        'id': user.id,
        'name': '${user.firstName ?? ''} ${user.lastName ?? ''}'.trim(),
        'avatarUrl': user.profilePictureUrl,
      };
    } catch (e) {
      LoggingService.error('Error getting user info for $userId: $e');
      return {'id': userId, 'name': 'Unknown User', 'avatarUrl': null};
    }
  }

  @override
  Future<List<UserModel>> getUsers(List<String> userIds) async {
    try {
      final users = <UserModel>[];
      for (final userId in userIds) {
        final user = await getUser(userId);
        if (user != null) {
          users.add(user);
        }
      }
      return users;
    } catch (e) {
      LoggingService.error('Error getting users: $e');
      return [];
    }
  }

  @override
  Future<List<UserModel>> getAllUsers() async {
    try {
      final response = await _httpDataSource.getUsers();
      return response.data.map(_mapApiUserToUserModel).toList();
    } catch (e) {
      LoggingService.error('Error getting all users: $e');
      return [];
    }
  }

  @override
  Future<List<UserModel>> getFriends(String userId) async {
    try {
      final contacts = await _httpDataSource.getContacts();
      final friends = <UserModel>[];
      
      for (final contact in contacts) {
        if (contact.status == 'accepted' && contact.contactUserId != null) {
          final friendUser = await getUser(contact.contactUserId!);
          if (friendUser != null) {
            friends.add(friendUser);
          }
        }
      }
      
      return friends;
    } catch (e) {
      LoggingService.error('Error getting friends for $userId: $e');
      return [];
    }
  }

  @override
  Future<void> createUser(UserModel user) async {
    try {
      // User creation is handled through authentication endpoints
      // This method might not be needed for the current implementation
      LoggingService.error('User creation should be handled through auth endpoints');
    } catch (e) {
      LoggingService.error('Error creating user: $e');
      throw Exception('Failed to create user: $e');
    }
  }

  @override
  Future<void> updateUser(UserModel user) async {
    try {
      final updateRequest = UpdateUserProfileRequest(
        firstName: user.firstName,
        lastName: user.lastName,
        // Add other fields as needed
      );
      
      await _httpDataSource.updateUserProfile(updateRequest);
    } catch (e) {
      LoggingService.error('Error updating user: $e');
      throw Exception('Failed to update user: $e');
    }
  }

  @override
  Future<List<UserModel>> findUsers({
    String? firstName,
    String? lastName,
    String? email,
  }) async {
    try {
      var searchQuery = '';
      if (firstName != null) searchQuery += firstName;
      if (lastName != null) searchQuery += ' $lastName';
      if (email != null) searchQuery += ' $email';
      
      if (searchQuery.trim().isEmpty) return [];
      
      final response = await _httpDataSource.getUsers(search: searchQuery.trim());
      return response.data.map(_mapApiUserToUserModel).toList();
    } catch (e) {
      LoggingService.error('Error finding users: $e');
      return [];
    }
  }

  @override
  Future<List<UserModel>> getUsersInBubble(String bubbleId) async {
    try {
      final members = await _httpDataSource.getBubbleMembers(bubbleId);
      return members.map(_mapApiUserToUserModel).toList();
    } catch (e) {
      LoggingService.error('Error getting users in bubble $bubbleId: $e');
      return [];
    }
  }

  @override
  Future<List<UserModel>> getMutualContacts(String userId1, String userId2) async {
    try {
      final mutualContacts = await _httpDataSource.getMutualContacts(userId2);
      return mutualContacts.map(_mapApiUserToUserModel).toList();
    } catch (e) {
      LoggingService.error('Error getting mutual contacts: $e');
      return [];
    }
  }

  @override
  Future<List<UserModel>> getMutualFriends(String userId1, String userId2) async {
    try {
      // For now, use the same endpoint as mutual contacts
      // This might need to be updated when a specific mutual friends endpoint is available
      final mutualFriends = await _httpDataSource.getMutualContacts(userId2);
      return mutualFriends.map(_mapApiUserToUserModel).toList();
    } catch (e) {
      LoggingService.error('Error getting mutual friends: $e');
      return [];
    }
  }

  Future<List<UserModel>> searchUsers(String query) async {
    try {
      final response = await _httpDataSource.getUsers(search: query);
      return response.data.map(_mapApiUserToUserModel).toList();
    } catch (e) {
      LoggingService.error('Error searching users: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> getUserBubbleInfo(String userId) async {
    try {
      final user = await getUser(userId);
      if (user == null) {
        return {'members': [], 'bubbleId': null};
      }
      
      if (user.bubbleId != null) {
        final members = await getUsersInBubble(user.bubbleId!);
        return {
          'members': members.map((u) => u.id).toList(),
          'bubbleId': user.bubbleId,
        };
      }
      
      return {'members': [], 'bubbleId': null};
    } catch (e) {
      LoggingService.error('Error getting user bubble info: $e');
      return {'members': [], 'bubbleId': null};
    }
  }

  @override
  Future<Map<String, dynamic>> getGroupInfo(String groupId) async {
    try {
      final members = await getUsersInBubble(groupId);
      return {
        'members': members.map((u) => u.id).toList(),
        'groupId': groupId,
      };
    } catch (e) {
      LoggingService.error('Error getting group info: $e');
      return {'members': [], 'groupId': null};
    }
  }

  @override
  Future<void> addFriend(String userId, String friendId) async {
    try {
      // This would typically be handled through contact requests
      LoggingService.error('Add friend functionality should use contact request system');
    } catch (e) {
      LoggingService.error('Error adding friend: $e');
      throw Exception('Failed to add friend: $e');
    }
  }

  void setCurrentUserId(String userId) {
    _currentUserId = userId;
  }

  @override
  Future<UserModel?> getUserById(String userId) async {
    // This is the same as getUser but with additional details as per interface comment
    return getUser(userId);
  }

  // Helper method to map API user to domain model
  UserModel _mapApiUserToUserModel(ApiUserProfile apiUser) => UserModel(
      id: apiUser.id,
      email: apiUser.email,
      username: apiUser.username,
      firstName: apiUser.firstName,
      lastName: apiUser.lastName,
      profilePictureUrl: apiUser.profilePictureUrl,
      onlineStatus: _mapOnlineStatus(apiUser.onlineStatus),
      bubbleStatus: _mapBubbleStatus(apiUser.bubbleStatus),
      birthday: apiUser.birthday,
      friendIds: apiUser.friendIds ?? [],
      contactIds: apiUser.contactIds ?? [],
      blockedUserIds: apiUser.blockedUserIds ?? [],
      pendingSentContactRequestIds: apiUser.pendingSentContactRequestIds ?? [],
      pendingReceivedContactRequestIds: apiUser.pendingReceivedContactRequestIds ?? [],
      pendingSentBubbleRequestUserIds: apiUser.pendingSentBubbleRequestUserIds ?? [],
      pendingReceivedBubbleRequestUserIds: apiUser.pendingReceivedBubbleRequestUserIds ?? [],
      bubbleId: apiUser.bubbleId,
    );

  OnlineStatus _mapOnlineStatus(String? status) {
    switch (status) {
      case 'online':
        return OnlineStatus.online;
      case 'offline':
        return OnlineStatus.offline;
      default:
        return OnlineStatus.offline;
    }
  }

  BubbleMembershipStatus _mapBubbleStatus(String? status) {
    switch (status) {
      case 'fullBubble':
        return BubbleMembershipStatus.fullBubble;
      case 'notFullBubble':
        return BubbleMembershipStatus.notFullBubble;
      case 'noBubble':
      default:
        return BubbleMembershipStatus.noBubble;
    }
  }

  // Contact request methods
  @override
  Future<bool> sendContactRequest({required String fromUserId, required String toUserId}) async {
    try {
      final request = SendContactRequestRequest(recipientId: toUserId);
      await _httpDataSource.sendContactRequest(request);
      return true;
    } catch (e) {
      LoggingService.error('Error sending contact request: $e');
      return false;
    }
  }

  @override
  Future<bool> acceptContactRequest({required String fromUserId, required String toUserId}) async {
    try {
      // Note: AcceptContactRequestRequest requires contactRequestId, not user IDs
      // For now, we'll use fromUserId as a placeholder for the request ID
      // In a real implementation, you'd need to find the actual request ID first
      final request = AcceptContactRequestRequest(contactRequestId: fromUserId);
      await _httpDataSource.acceptContactRequest(request.contactRequestId);
      return true;
    } catch (e) {
      LoggingService.error('Error accepting contact request: $e');
      return false;
    }
  }

  @override
  Future<bool> rejectContactRequest({required String fromUserId, required String toUserId}) async {
    try {
      await _httpDataSource.rejectContactRequest(fromUserId);
      return true;
    } catch (e) {
      LoggingService.error('Error rejecting contact request: $e');
      return false;
    }
  }

  // User management methods
  @override
  Future<bool> removeContact({required String userId, required String contactId}) async {
    try {
      // TODO: Implement removeContact in HTTP data source
      // await _httpDataSource.removeContact(userId: userId, contactId: contactId);
      LoggingService.info('Remove contact not yet implemented');
      return true;
    } catch (e) {
      LoggingService.error('Error removing contact: $e');
      return false;
    }
  }

  @override
  Future<bool> blockUser({required String userId, required String targetUserId}) async {
    try {
      // TODO: Implement blockUser in HTTP data source
      // await _httpDataSource.blockUser(userId: userId, targetUserId: targetUserId);
      LoggingService.info('Block user not yet implemented');
      return true;
    } catch (e) {
      LoggingService.error('Error blocking user: $e');
      return false;
    }
  }

  @override
  Future<bool> unblockUser({required String userId, required String targetUserId}) async {
    try {
      // TODO: Implement unblockUser in HTTP data source
      // await _httpDataSource.unblockUser(userId: userId, targetUserId: targetUserId);
      LoggingService.info('Unblock user not yet implemented');
      return true;
    } catch (e) {
      LoggingService.error('Error unblocking user: $e');
      return false;
    }
  }

  @override
  Future<bool> reportUser({required String reportedUserId, required String reason, required String category}) async {
    try {
      // TODO: Implement reportUser in HTTP data source
      // await _httpDataSource.reportUser(reportedUserId: reportedUserId, reason: reason, category: category);
      LoggingService.info('Report user not yet implemented');
      return true;
    } catch (e) {
      LoggingService.error('Error reporting user: $e');
      return false;
    }
  }
}