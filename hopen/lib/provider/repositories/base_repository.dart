import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/error/do_notation.dart';
import '../../statefulbusinesslogic/core/error/base_error.dart';
import '../../statefulbusinesslogic/core/error/failures.dart' as failures;
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';

/// Base repository class to eliminate redundant error handling
abstract class BaseRepository {
  /// Handle API calls with consistent error handling
  Future<Result<T>> safeApiCall<T>(Future<T> Function() apiCall) async {
    try {
      final result = await apiCall();
      return Result.success(result);
    } catch (e) {
      return Result.failure(failures.UnexpectedFailure(message: 'API call failed: ${e.toString()}'));
    }
  }

  /// Enhanced API call with custom error mapping
  Future<Result<T>> safeApiCallWithErrorMapping<T>(
    Future<T> Function() apiCall,
    failures.Failure Function(dynamic error) errorMapper,
  ) async {
    try {
      final result = await apiCall();
      return Result.success(result);
    } catch (e) {
      return Result.failure(errorMapper(e));
    }
  }

  /// Chain multiple API calls with automatic error handling
  Future<Result<R>> chainApiCalls<T, R>(
    Future<T> Function() firstCall,
    Future<R> Function(T) secondCall,
  ) async {
    try {
      final firstResult = await safeApiCall(firstCall);
      if (firstResult.isFailure) {
        return Result.failure(firstResult.error!);
      }
      final secondResult = await safeApiCall(() => secondCall(firstResult.data!));
      return secondResult;
    } catch (e) {
      return Result.failure(failures.UnexpectedFailure(message: e.toString()));
    }
  }

  /// Execute multiple API calls in parallel and combine results
  Future<Result<List<T>>> parallelApiCalls<T>(
    List<Future<T> Function()> apiCalls,
  ) async {
    final tasks = apiCalls.map((call) => safeApiCall(call)).toList();
    return ResultUtils.sequenceAsync(tasks);
  }

  /// Execute API call with retry logic
  Future<Result<T>> safeApiCallWithRetry<T>(
    Future<T> Function() apiCall, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      final result = await safeApiCall(apiCall);

      if (result.isSuccess || attempt == maxRetries) {
        return result;
      }

      if (attempt < maxRetries) {
        await Future.delayed(delay * (attempt + 1));
      }
    }

    return Result.failure(NetworkError(message: 'API call failed after $maxRetries retries'));
  }

  /// Execute API call with timeout
  Future<Result<T>> safeApiCallWithTimeout<T>(
    Future<T> Function() apiCall,
    Duration timeout,
  ) async {
    return safeApiCall(apiCall).timeout(timeout);
  }

  /// Parse list of bubbles from API response
  List<BubbleEntity> parseBubbleList(dynamic response) {
    final data = response['bubbles'] as List<dynamic>? ?? 
                 response as List<dynamic>? ?? 
                 [];
    
    final bubbles = <BubbleEntity>[];
    for (final json in data) {
      if (json is Map<String, dynamic>) {
        final bubbleResult = BubbleEntity.fromJson(json);
        if (bubbleResult.isSuccess) {
          bubbles.add(bubbleResult.data);
        }
      }
    }
    return bubbles;
  }

  /// Parse member IDs from API response
  List<String> parseMemberIds(dynamic response) {
    final data = response['members'] as List<dynamic>? ?? 
                 response as List<dynamic>? ?? 
                 [];
    return data
        .map((member) => member['id'] as String? ?? 
                        member['user_id'] as String? ?? 
                        '',)
        .where((id) => id.isNotEmpty)
        .toList();
  }
}