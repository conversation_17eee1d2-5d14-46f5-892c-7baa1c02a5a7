import 'dart:async';

import '../../../repositories/notification/notification_repository.dart';
import '../../../statefulbusinesslogic/core/models/notification_model.dart';
import '../../services/local_storage/local_storage_service.dart';

/// Implementation of NotificationRepository using local storage
class NotificationRepositoryImpl implements NotificationRepository {
  final LocalStorageService _localStorage;
  static const String _notificationsKey = 'notifications';

  NotificationRepositoryImpl({
    required LocalStorageService localStorage,
  }) : _localStorage = localStorage;

  @override
  Future<void> saveNotification(Notification notification) async {
    final notifications = await getNotifications();
    notifications.add(notification);
    await _saveNotifications(notifications);
  }

  @override
  Future<List<Notification>> getNotifications() async {
    final data = await _localStorage.getList(_notificationsKey);
    return data.map((item) => Notification.fromJson(item)).toList();
  }

  @override
  Future<int> getUnreadCount() async {
    final notifications = await getNotifications();
    return notifications.where((n) => !n.isRead).length;
  }

  @override
  Future<void> markAsRead(String notificationId) async {
    final notifications = await getNotifications();
    final index = notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      notifications[index] = notifications[index].copyWith(isRead: true);
      await _saveNotifications(notifications);
    }
  }

  @override
  Future<void> markAllAsRead() async {
    final notifications = await getNotifications();
    final updatedNotifications = notifications.map((n) => n.copyWith(isRead: true)).toList();
    await _saveNotifications(updatedNotifications);
  }

  @override
  Future<void> deleteNotification(String notificationId) async {
    final notifications = await getNotifications();
    notifications.removeWhere((n) => n.id == notificationId);
    await _saveNotifications(notifications);
  }

  @override
  Future<void> clearAll() async {
    await _localStorage.remove(_notificationsKey);
  }

  @override
  Future<List<Notification>> getNotificationsByCategory(NotificationCategory category) async {
    final notifications = await getNotifications();
    return notifications.where((n) => n.category == category).toList();
  }

  @override
  Future<List<Notification>> getNotificationsByType(String type) async {
    final notifications = await getNotifications();
    return notifications.where((n) => n.payload?['type'] == type).toList();
  }

  @override
  Stream<List<Notification>> notificationsStream() {
    // For now, return a stream that emits current notifications
    // In a real implementation, this would listen to storage changes
    return Stream.periodic(const Duration(seconds: 1), (_) async {
      return await getNotifications();
    }).asyncMap((future) => future);
  }

  @override
  Stream<int> unreadCountStream() {
    // For now, return a stream that emits current unread count
    // In a real implementation, this would listen to storage changes
    return Stream.periodic(const Duration(seconds: 1), (_) async {
      return await getUnreadCount();
    }).asyncMap((future) => future);
  }

  @override
  Future<void> markAsUnread(String notificationId) async {
    final notifications = await getNotifications();
    final index = notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      notifications[index] = notifications[index].copyWith(isRead: false);
      await _saveNotifications(notifications);
    }
  }

  @override
  Future<void> deleteAllNotifications() async {
    await _localStorage.remove(_notificationsKey);
  }

  Future<void> _saveNotifications(List<Notification> notifications) async {
    final data = notifications.map((n) => n.toJson()).toList();
    await _localStorage.setList(_notificationsKey, data);
  }
}

