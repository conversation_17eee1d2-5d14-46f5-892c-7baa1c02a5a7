import '../../../repositories/notification_permission/notification_permission_repository.dart';
import '../../services/notification_service_fcm.dart';

class NotificationPermissionRepositoryImpl
    implements NotificationPermissionRepository {
  NotificationPermissionRepositoryImpl({required this.notificationService});

  final NotificationServiceFCM notificationService;

  @override
  Future<bool> requestSystemPermission() {
    return notificationService.requestSystemPermission();
  }
} 