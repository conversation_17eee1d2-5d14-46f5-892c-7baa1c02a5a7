import 'package:equatable/equatable.dart';

import '../../core/models/relationship_type.dart';
import '../../core/models/user_model.dart';
import '../../core/models/bubble_member.dart';
import 'unified_profile_event.dart';

// States
abstract class UnifiedProfileState extends Equatable {
  const UnifiedProfileState();

  @override
  List<Object?> get props => [];
}

class UnifiedProfileInitial extends UnifiedProfileState {}

class UnifiedProfileLoading extends UnifiedProfileState {}

class UnifiedProfileLoaded extends UnifiedProfileState {
  const UnifiedProfileLoaded({
    required this.user,
    required this.relationshipType,
    required this.currentUserId,
    required this.bubbleMembers,
  });
  
  final UserModel user;
  final RelationshipType relationshipType;
  final String currentUserId;
  final List<BubbleMember> bubbleMembers;

  @override
  List<Object?> get props => [user, relationshipType, currentUserId, bubbleMembers];
}

class UnifiedProfileError extends UnifiedProfileState {
  const UnifiedProfileError(this.message);
  
  final String message;

  @override
  List<Object?> get props => [message];
}

class UnifiedProfileActionSuccess extends UnifiedProfileState {
  const UnifiedProfileActionSuccess({
    required this.message,
    required this.user,
    required this.relationshipType,
    required this.currentUserId,
    required this.bubbleMembers,
  });

  final String message;
  final UserModel user;
  final RelationshipType relationshipType;
  final String currentUserId;
  final List<BubbleMember> bubbleMembers;

  @override
  List<Object?> get props => [message, user, relationshipType, currentUserId, bubbleMembers];
}

class UnifiedProfileContactRequestSent extends UnifiedProfileState {
  const UnifiedProfileContactRequestSent({
    required this.user,
    required this.relationshipType,
    required this.currentUserId,
    required this.bubbleMembers,
  });

  final UserModel user;
  final RelationshipType relationshipType;
  final String currentUserId;
  final List<BubbleMember> bubbleMembers;

  @override
  List<Object?> get props => [user, relationshipType, currentUserId, bubbleMembers];
}

class UnifiedProfileBubbleRequestSent extends UnifiedProfileState {
  const UnifiedProfileBubbleRequestSent({
    required this.user,
    required this.relationshipType,
    required this.currentUserId,
    required this.requestType,
    required this.bubbleMembers,
  });

  final UserModel user;
  final RelationshipType relationshipType;
  final String currentUserId;
  final BubbleRequestType requestType;
  final List<BubbleMember> bubbleMembers;

  @override
  List<Object?> get props => [user, relationshipType, currentUserId, requestType, bubbleMembers];
} 