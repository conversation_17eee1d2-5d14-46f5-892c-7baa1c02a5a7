import 'package:equatable/equatable.dart';
import '../../core/error/base_error.dart';
import '../../core/models/bubble_entity.dart';

/// Enhanced bubble states with better error handling and type safety
sealed class BubbleState extends Equatable {
  const BubbleState();

  @override
  List<Object?> get props => [];
}

/// Initial state - user has no bubble
final class B<PERSON>bleInitial extends BubbleState {
  const BubbleInitial();

  @override
  String toString() => 'BubbleInitial';
}

/// Loading state with context about what operation is being performed
final class BubbleLoading extends BubbleState {
  const BubbleLoading({required this.operation, this.currentBubble});
  final String operation;
  final BubbleEntity? currentBubble;

  @override
  List<Object?> get props => [operation, currentBubble];

  @override
  String toString() => 'BubbleLoading(operation: $operation)';
}

/// Successfully loaded bubble state
final class BubbleLoaded extends BubbleState {
  const BubbleLoaded(this.bubble);
  final BubbleEntity bubble;

  /// Check if user is a member
  bool isMember(String userId) => bubble.isMember(userId);

  /// Check if user is an active member
  bool isActiveMember(String userId) => bubble.isActiveMember(userId);

  /// Check if bubble has active call
  bool get hasActiveCall => bubble.hasActiveCall;

  /// Get member by ID
  BubbleMemberEntity? getMember(String userId) => bubble.getMember(userId);

  /// Get online members count
  int get onlineMembersCount => bubble.onlineMembersCount;

  /// Get total unread messages
  int get totalUnreadMessages => bubble.totalUnreadMessages;

  @override
  List<Object?> get props => [bubble];

  @override
  String toString() =>
      'BubbleLoaded(bubbleId: ${bubble.id.value}, members: ${bubble.members.length})';
}

/// Error state with proper error handling
final class BubbleError extends BubbleState {
  const BubbleError({required this.error, this.previousBubble, this.operation});
  final BaseError error;
  final BubbleEntity? previousBubble;
  final String? operation;

  /// Get user-friendly error message
  String get userMessage => error.userMessage;

  /// Get technical error message for debugging
  String get technicalMessage => error.technicalMessage;

  /// Get error code for programmatic handling
  String? get errorCode => error.code;

  /// Check if error is recoverable
  bool get isRecoverable => previousBubble != null;

  @override
  List<Object?> get props => [error, previousBubble, operation];

  @override
  String toString() =>
      'BubbleError(error: ${error.technicalMessage}, operation: $operation)';
}

/// Success state for operations that don't return data
final class BubbleOperationSuccess extends BubbleState {
  const BubbleOperationSuccess({
    required this.operation,
    required this.message,
    this.updatedBubble,
  });
  final String operation;
  final String message;
  final BubbleEntity? updatedBubble;

  @override
  List<Object?> get props => [operation, message, updatedBubble];

  @override
  String toString() =>
      'BubbleOperationSuccess(operation: $operation, message: $message)';
}

/// State extensions for better usability
extension BubbleStateExtensions on BubbleState {
  /// Check if state represents a loading condition
  bool get isLoading => this is BubbleLoading;

  /// Check if state represents an error condition
  bool get isError => this is BubbleError;

  /// Check if state represents success with data
  bool get isLoaded => this is BubbleLoaded;

  /// Check if state represents initial condition (no bubble)
  bool get isInitial => this is BubbleInitial;

  /// Get the current bubble if available
  BubbleEntity? get currentBubble => switch (this) {
    BubbleLoaded(bubble: final bubble) => bubble,
    BubbleLoading(currentBubble: final bubble) => bubble,
    BubbleError(previousBubble: final bubble) => bubble,
    BubbleOperationSuccess(updatedBubble: final bubble) => bubble,
    _ => null,
  };

  /// Get current operation being performed
  String? get currentOperation => switch (this) {
    BubbleLoading(operation: final operation) => operation,
    BubbleError(operation: final operation) => operation,
    BubbleOperationSuccess(operation: final operation) => operation,
    _ => null,
  };

  /// Handle state with pattern matching helper
  T when<T>({
    required T Function() onInitial,
    required T Function(String operation, BubbleEntity? currentBubble)
    onLoading,
    required T Function(BubbleEntity bubble) onLoaded,
    required T Function(
      BaseError error,
      BubbleEntity? previousBubble,
      String? operation,
    )
    onError,
    required T Function(
      String operation,
      String message,
      BubbleEntity? updatedBubble,
    )
    onSuccess,
  }) => switch (this) {
    BubbleInitial() => onInitial(),
    BubbleLoading(
      operation: final operation,
      currentBubble: final currentBubble,
    ) =>
      onLoading(operation, currentBubble),
    BubbleLoaded(bubble: final bubble) => onLoaded(bubble),
    BubbleError(
      error: final error,
      previousBubble: final previousBubble,
      operation: final operation,
    ) =>
      onError(error, previousBubble, operation),
    BubbleOperationSuccess(
      operation: final operation,
      message: final message,
      updatedBubble: final updatedBubble,
    ) =>
      onSuccess(operation, message, updatedBubble),
  };
}
