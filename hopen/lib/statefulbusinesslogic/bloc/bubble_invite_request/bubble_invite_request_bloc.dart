import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../repositories/bubble/bubble_invite_request_repository.dart';
import '../../../repositories/user/user_repository.dart';
import 'bubble_invite_request_event.dart';
import 'bubble_invite_request_state.dart';

class BubbleInviteRequestBloc extends Bloc<BubbleInviteRequestEvent, BubbleInviteRequestState> {

  BubbleInviteRequestBloc({
    required BubbleInviteRequestRepository repository,
    required UserRepository userRepository,
    BuildContext? context,
  })  : _repository = repository,
        _userRepository = userRepository,
        _context = context,
        super(const BubbleInviteRequestState()) {
    on<LoadBubbleInviteRequestEvent>(_onLoadInviteRequest);
    on<AcceptBubbleInviteRequestEvent>(_onAcceptInviteRequest);
    on<DeclineBubbleInviteRequestEvent>(_onDeclineInviteRequest);
    on<SendBubbleInviteRequestEvent>(_onSendInviteRequest);
    on<ResetBubbleInviteRequestEvent>(_onResetInviteRequest);
  }
  final BubbleInviteRequestRepository _repository;
  final UserRepository _userRepository;
  final BuildContext? _context;

  void _onLoadInviteRequest(
    LoadBubbleInviteRequestEvent event,
    Emitter<BubbleInviteRequestState> emit,
  ) {
    emit(state.copyWith(
      status: BubbleInviteRequestStatus.loaded,
      bubbleId: event.bubbleId,
      bubbleName: event.bubbleName,
      inviterId: event.inviterId,
      inviterName: event.inviterName,
      inviteMessage: event.inviteMessage,
    ),);
  }

  Future<void> _onAcceptInviteRequest(
    AcceptBubbleInviteRequestEvent event,
    Emitter<BubbleInviteRequestState> emit,
  ) async {
    try {
      emit(state.copyWith(status: BubbleInviteRequestStatus.accepting));

      await _repository.acceptInviteRequest(event.requestId);

      emit(state.copyWith(status: BubbleInviteRequestStatus.accepted));
    } catch (e) {
      emit(state.copyWith(
        status: BubbleInviteRequestStatus.error,
        errorMessage: e.toString(),
      ),);
    }
  }

  Future<void> _onDeclineInviteRequest(
    DeclineBubbleInviteRequestEvent event,
    Emitter<BubbleInviteRequestState> emit,
  ) async {
    try {
      emit(state.copyWith(status: BubbleInviteRequestStatus.declining));

      await _repository.declineInviteRequest(event.requestId);

      emit(state.copyWith(status: BubbleInviteRequestStatus.declined));
    } catch (e) {
      emit(state.copyWith(
        status: BubbleInviteRequestStatus.error,
        errorMessage: e.toString(),
      ),);
    }
  }

  Future<void> _onSendInviteRequest(
    SendBubbleInviteRequestEvent event,
    Emitter<BubbleInviteRequestState> emit,
  ) async {
    try {
      emit(state.copyWith(status: BubbleInviteRequestStatus.loading));

      // Get the invitee's details
      final invitee = await _userRepository.getUserById(event.inviteeId);
      if (invitee == null) {
        throw Exception('Invitee not found');
      }

      // Get the bubble details
      final bubbleResult = await _repository.getBubbleDetails(event.bubbleId);
      
      bubbleResult.fold(
        onSuccess: (bubble) async {
          // Get the current user's details
          final currentUser = await _userRepository.getCurrentUser();
          if (currentUser == null) {
            throw Exception('Current user not found');
          }

          // Send the invite request
          await _repository.sendInviteRequest(
            event.bubbleId,
            event.inviteeId,
            event.message ?? '',
          );

          // Notify other bubble members about the invite via unified notification service
          // The notification will be handled by the real-time service and dispatched to appropriate handlers
          // This replaces the direct dialog showing with proper notification flow

          emit(state.copyWith(status: BubbleInviteRequestStatus.loaded));
        },
        onFailure: (error) {
          emit(state.copyWith(
            status: BubbleInviteRequestStatus.error,
            errorMessage: error.toString(),
          ),);
        },
      );
    } catch (e) {
      emit(state.copyWith(
        status: BubbleInviteRequestStatus.error,
        errorMessage: e.toString(),
      ),);
    }
  }

  void _onResetInviteRequest(
    ResetBubbleInviteRequestEvent event,
    Emitter<BubbleInviteRequestState> emit,
  ) {
    emit(const BubbleInviteRequestState());
  }
}