import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../repositories/user/user_repository.dart';
import '../../core/models/user_model.dart';

// Events
abstract class UserProfileEvent extends Equatable {
  const UserProfileEvent();

  @override
  List<Object> get props => [];
}

class LoadUserProfileEvent extends UserProfileEvent {

  const LoadUserProfileEvent(this.userId, {this.forceReload = false});
  final String userId;
  final bool forceReload;

  @override
  List<Object> get props => [userId, forceReload];
}

class UpdateUserProfileEvent extends UserProfileEvent {

  const UpdateUserProfileEvent(this.user);
  final UserModel user;

  @override
  List<Object> get props => [user];
}

// States
abstract class UserProfileState extends Equatable {
  const UserProfileState();

  @override
  List<Object> get props => [];
}

class UserProfileInitial extends UserProfileState {}

class UserProfileLoading extends UserProfileState {}

class UserProfileLoaded extends UserProfileState {

  const UserProfileLoaded(this.user);
  final UserModel user;

  @override
  List<Object> get props => [user];
}

class UserProfileError extends UserProfileState {

  const UserProfileError(this.message);
  final String message;

  @override
  List<Object> get props => [message];
}

// BLoC
class UserProfileBloc extends Bloc<UserProfileEvent, UserProfileState> {

  UserProfileBloc({
    required UserRepository userRepository,
  })  : _userRepository = userRepository,
        super(UserProfileInitial()) {
    on<LoadUserProfileEvent>(_onLoadUserProfile);
    on<UpdateUserProfileEvent>(_onUpdateUserProfile);
  }
  final UserRepository _userRepository;

  Future<void> _onLoadUserProfile(
    LoadUserProfileEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    // Check if we're reloading the same profile and it's already loaded
    final currentState = state;
    if (currentState is UserProfileLoaded &&
        currentState.user.id == event.userId &&
        !event.forceReload) {
      // Already loaded this profile, no need to reload
      print('DEBUG: UserProfileBloc - Profile already loaded for user ${event.userId}, skipping reload');
      return;
    }

    print('DEBUG: UserProfileBloc - Loading profile for user ${event.userId}');
    emit(UserProfileLoading());
    try {
      final user = await _userRepository.getUserById(event.userId);
      if (user != null) {
        print('DEBUG: UserProfileBloc - Profile loaded successfully for user ${event.userId}');
        print('DEBUG: UserProfileBloc - Profile picture URL: ${user.profilePictureUrl}');
        print('DEBUG: UserProfileBloc - User data: ${user.toString()}');
        emit(UserProfileLoaded(user));
      } else {
        print('DEBUG: UserProfileBloc - User not found for ID: ${event.userId}');
        emit(UserProfileError('User not found'));
      }
    } catch (e) {
      print('DEBUG: UserProfileBloc - Error loading profile: $e');
      emit(UserProfileError(e.toString()));
    }
  }

  Future<void> _onUpdateUserProfile(
    UpdateUserProfileEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    emit(UserProfileLoading());
    try {
      await _userRepository.updateUser(event.user);
      emit(UserProfileLoaded(event.user));
    } catch (e) {
      emit(UserProfileError(e.toString()));
    }
  }
} 