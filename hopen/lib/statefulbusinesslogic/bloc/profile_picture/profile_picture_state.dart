import 'package:equatable/equatable.dart';

import '../../core/models/profile_picture_result.dart';

/// Base class for all profile picture states
abstract class ProfilePictureState extends Equatable {
  const ProfilePictureState();

  @override
  List<Object?> get props => [];
}

/// Initial state when no operation has been performed
class ProfilePictureInitial extends ProfilePictureState {
  const ProfilePictureInitial();
}

/// State when a profile picture operation is in progress
class ProfilePictureLoading extends ProfilePictureState {
  const ProfilePictureLoading({this.operation});

  final String? operation; // e.g., "Uploading...", "Processing...", "Validating..."

  @override
  List<Object?> get props => [operation];
}

/// State when profile picture operation completed successfully
class ProfilePictureSuccess extends ProfilePictureState {
  const ProfilePictureSuccess({
    required this.result,
    this.message,
  });

  final ProfilePictureResult result;
  final String? message;

  @override
  List<Object?> get props => [result, message];
}

/// State when profile picture operation failed
class ProfilePictureError extends ProfilePictureState {
  const ProfilePictureError({
    required this.message,
    this.technicalDetails,
    this.isRetryable = false,
    this.retryAfter,
    this.errorCode,
  });

  final String message;
  final String? technicalDetails;
  final bool isRetryable;
  final Duration? retryAfter;
  final String? errorCode;

  @override
  List<Object?> get props => [message, technicalDetails, isRetryable, retryAfter, errorCode];
}

/// State when user cancelled the operation
class ProfilePictureCancelled extends ProfilePictureState {
  const ProfilePictureCancelled();
}

/// State when image validation completed
class ProfilePictureValidated extends ProfilePictureState {
  const ProfilePictureValidated({
    required this.isValid,
    this.error,
  });

  final bool isValid;
  final String? error;

  @override
  List<Object?> get props => [isValid, error];
}

/// State when profile picture was removed successfully
class ProfilePictureRemoved extends ProfilePictureState {
  const ProfilePictureRemoved();
}
