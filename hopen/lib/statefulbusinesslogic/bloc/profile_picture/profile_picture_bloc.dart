import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';

import '../../../provider/exceptions/storage_exception.dart';
import '../../../provider/exceptions/network_exception.dart';
import '../../../repositories/profile_picture/profile_picture_repository.dart';
import '../../core/services/connectivity_service.dart';
import 'profile_picture_event.dart';
import 'profile_picture_state.dart';

/// BLoC for managing profile picture operations
/// This follows the four-layer dependency rule by only depending on the repository interface
class ProfilePictureBloc extends Bloc<ProfilePictureEvent, ProfilePictureState> {
  ProfilePictureBloc({
    required ProfilePictureRepository profilePictureRepository,
    required ConnectivityService connectivityService,
  }) : _profilePictureRepository = profilePictureRepository,
       _connectivityService = connectivityService,
       super(const ProfilePictureInitial()) {

    on<PickFromGalleryEvent>(_onPickFromGallery);
    on<TakePhotoEvent>(_onTakePhoto);
    on<RemoveProfilePictureEvent>(_onRemoveProfilePicture);
    on<ValidateImageEvent>(_onValidateImage);
    on<ResetProfilePictureEvent>(_onResetProfilePicture);
    on<RetryUploadEvent>(_onRetryUpload);
  }

  final ProfilePictureRepository _profilePictureRepository;
  final ConnectivityService _connectivityService;

  /// Handle picking image from gallery
  Future<void> _onPickFromGallery(
    PickFromGalleryEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    // Check connectivity before starting
    await _checkConnectivityAndProceed(
      emit: emit,
      operation: 'Selecting image from gallery...',
      action: () async {
        final result = await _profilePictureRepository.pickFromGallery();
        return _handleRepositoryResult(result, emit);
      },
    );
  }

  /// Handle taking photo with camera
  Future<void> _onTakePhoto(
    TakePhotoEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    // Check connectivity before starting
    await _checkConnectivityAndProceed(
      emit: emit,
      operation: 'Taking photo...',
      action: () async {
        final result = await _profilePictureRepository.takePhoto();
        return _handleRepositoryResult(result, emit);
      },
    );
  }

  /// Handle removing profile picture
  Future<void> _onRemoveProfilePicture(
    RemoveProfilePictureEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    emit(const ProfilePictureLoading(operation: 'Removing profile picture...'));
    
    try {
      final success = await _profilePictureRepository.removeProfilePicture(event.imageUrl);
      
      if (success) {
        emit(const ProfilePictureRemoved());
      } else {
        emit(const ProfilePictureError(
          message: 'Failed to remove profile picture',
        ));
      }
    } catch (e) {
      emit(ProfilePictureError(
        message: 'An unexpected error occurred while removing profile picture',
        technicalDetails: e.toString(),
      ));
    }
  }

  /// Handle image validation
  Future<void> _onValidateImage(
    ValidateImageEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    emit(const ProfilePictureLoading(operation: 'Validating image...'));
    
    try {
      final validationResult = await _profilePictureRepository.validateImage(event.imagePath);
      
      emit(ProfilePictureValidated(
        isValid: validationResult.isValid,
        error: validationResult.error,
      ));
    } catch (e) {
      emit(ProfilePictureError(
        message: 'An unexpected error occurred while validating image',
        technicalDetails: e.toString(),
      ));
    }
  }

  /// Handle resetting profile picture state
  Future<void> _onResetProfilePicture(
    ResetProfilePictureEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    emit(const ProfilePictureInitial());
  }

  /// Handle retry upload operation
  Future<void> _onRetryUpload(
    RetryUploadEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    // Retry the last operation based on the event type
    if (event.lastOperation == ProfilePictureOperation.pickFromGallery) {
      await _onPickFromGallery(const PickFromGalleryEvent(), emit);
    } else if (event.lastOperation == ProfilePictureOperation.takePhoto) {
      await _onTakePhoto(const TakePhotoEvent(), emit);
    } else if (event.lastOperation == ProfilePictureOperation.removeProfilePicture && event.imageUrl != null) {
      await _onRemoveProfilePicture(RemoveProfilePictureEvent(imageUrl: event.imageUrl!), emit);
    }
  }

  /// Check connectivity and proceed with operation
  Future<void> _checkConnectivityAndProceed({
    required Emitter<ProfilePictureState> emit,
    required String operation,
    required Future<void> Function() action,
  }) async {
    emit(ProfilePictureLoading(operation: operation));

    try {
      // Check connectivity status
      final connectivityStatus = await _connectivityService.statusStream.first.timeout(
        const Duration(seconds: 2),
        onTimeout: () => ConnectivityStatus.connected, // Assume connected if check times out
      );

      if (connectivityStatus == ConnectivityStatus.noInternet) {
        emit(const ProfilePictureError(
          message: 'No internet connection. Please check your network settings.',
          errorType: ProfilePictureErrorType.network,
          isRetryable: true,
        ));
        return;
      }

      if (connectivityStatus == ConnectivityStatus.backendUnreachable) {
        emit(const ProfilePictureError(
          message: 'Cannot connect to server. Please try again later.',
          errorType: ProfilePictureErrorType.network,
          isRetryable: true,
          technicalDetails: 'Backend server is unreachable',
        ));
        return;
      }

      // Proceed with the operation
      await action();
    } catch (e) {
      emit(ProfilePictureError(
        message: 'An unexpected error occurred',
        technicalDetails: e.toString(),
        errorType: ProfilePictureErrorType.unknown,
        isRetryable: true,
      ));
    }
  }

  /// Handle repository result with proper error categorization
  Future<void> _handleRepositoryResult(
    dynamic result,
    Emitter<ProfilePictureState> emit,
  ) async {
    if (result.isCancelled) {
      emit(const ProfilePictureCancelled());
    } else if (result.isSuccess) {
      emit(ProfilePictureSuccess(
        result: result,
        message: 'Profile picture updated successfully!',
      ));
    } else {
      // Categorize error based on error message
      ProfilePictureErrorType errorType = ProfilePictureErrorType.unknown;
      bool isRetryable = true;

      final errorMessage = result.error ?? 'Operation failed';

      if (errorMessage.toLowerCase().contains('network') ||
          errorMessage.toLowerCase().contains('connection')) {
        errorType = ProfilePictureErrorType.network;
      } else if (errorMessage.toLowerCase().contains('storage') ||
                 errorMessage.toLowerCase().contains('upload')) {
        errorType = ProfilePictureErrorType.storage;
      } else if (errorMessage.toLowerCase().contains('size')) {
        errorType = ProfilePictureErrorType.fileSize;
        isRetryable = false;
      } else if (errorMessage.toLowerCase().contains('format') ||
                 errorMessage.toLowerCase().contains('type')) {
        errorType = ProfilePictureErrorType.fileFormat;
        isRetryable = false;
      }

      emit(ProfilePictureError(
        message: errorMessage,
        errorType: errorType,
        isRetryable: isRetryable,
      ));
    }
  }
}
