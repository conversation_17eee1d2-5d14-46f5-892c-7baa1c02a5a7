import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../provider/exceptions/storage_exception.dart';
import '../../../repositories/profile_picture/profile_picture_repository.dart';
import 'profile_picture_event.dart';
import 'profile_picture_state.dart';

/// BLoC for managing profile picture operations
/// This follows the four-layer dependency rule by only depending on the repository interface
class ProfilePictureBloc extends Bloc<ProfilePictureEvent, ProfilePictureState> {
  ProfilePictureBloc({
    required ProfilePictureRepository profilePictureRepository,
  }) : _profilePictureRepository = profilePictureRepository,
       super(const ProfilePictureInitial()) {

    on<PickFromGalleryEvent>(_onPickFromGallery);
    on<TakePhotoEvent>(_onTakePhoto);
    on<RemoveProfilePictureEvent>(_onRemoveProfilePicture);
    on<ValidateImageEvent>(_onValidateImage);
    on<ResetProfilePictureEvent>(_onResetProfilePicture);
    on<RetryUploadEvent>(_onRetryUpload);
  }

  final ProfilePictureRepository _profilePictureRepository;

  /// Handle picking image from gallery
  Future<void> _onPickFromGallery(
    PickFromGalleryEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    emit(const ProfilePictureLoading(operation: 'Selecting image from gallery...'));

    try {
      final result = await _profilePictureRepository.pickFromGallery();

      if (result.isCancelled) {
        emit(const ProfilePictureCancelled());
      } else if (result.isSuccess) {
        emit(ProfilePictureSuccess(
          result: result,
          message: 'Profile picture updated successfully!',
        ));
      } else {
        emit(ProfilePictureError(
          message: result.error ?? 'Failed to pick image from gallery',
          isRetryable: true,
        ));
      }
    } on StorageException catch (e) {
      emit(ProfilePictureError(
        message: e.message,
        isRetryable: e.isRetryable,
        retryAfter: e.retryAfter,
        errorCode: e.code,
      ));
    } on NetworkException catch (e) {
      emit(ProfilePictureError(
        message: e.message,
        isRetryable: e.isRetryable,
        retryAfter: e.retryAfter,
        errorCode: e.code,
      ));
    } catch (e) {
      emit(ProfilePictureError(
        message: 'An unexpected error occurred while picking image',
        technicalDetails: e.toString(),
        isRetryable: false,
      ));
    }
  }

  /// Handle taking photo with camera
  Future<void> _onTakePhoto(
    TakePhotoEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    emit(const ProfilePictureLoading(operation: 'Taking photo...'));

    try {
      final result = await _profilePictureRepository.takePhoto();

      if (result.isCancelled) {
        emit(const ProfilePictureCancelled());
      } else if (result.isSuccess) {
        emit(ProfilePictureSuccess(
          result: result,
          message: 'Profile picture updated successfully!',
        ));
      } else {
        emit(ProfilePictureError(
          message: result.error ?? 'Failed to take photo',
          isRetryable: true,
        ));
      }
    } on StorageException catch (e) {
      emit(ProfilePictureError(
        message: e.message,
        isRetryable: e.isRetryable,
        retryAfter: e.retryAfter,
        errorCode: e.code,
      ));
    } on NetworkException catch (e) {
      emit(ProfilePictureError(
        message: e.message,
        isRetryable: e.isRetryable,
        retryAfter: e.retryAfter,
        errorCode: e.code,
      ));
    } catch (e) {
      emit(ProfilePictureError(
        message: 'An unexpected error occurred while taking photo',
        technicalDetails: e.toString(),
        isRetryable: false,
      ));
    }
  }

  /// Handle removing profile picture
  Future<void> _onRemoveProfilePicture(
    RemoveProfilePictureEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    emit(const ProfilePictureLoading(operation: 'Removing profile picture...'));
    
    try {
      final success = await _profilePictureRepository.removeProfilePicture(event.imageUrl);
      
      if (success) {
        emit(const ProfilePictureRemoved());
      } else {
        emit(const ProfilePictureError(
          message: 'Failed to remove profile picture',
        ));
      }
    } catch (e) {
      emit(ProfilePictureError(
        message: 'An unexpected error occurred while removing profile picture',
        technicalDetails: e.toString(),
      ));
    }
  }

  /// Handle image validation
  Future<void> _onValidateImage(
    ValidateImageEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    emit(const ProfilePictureLoading(operation: 'Validating image...'));
    
    try {
      final validationResult = await _profilePictureRepository.validateImage(event.imagePath);
      
      emit(ProfilePictureValidated(
        isValid: validationResult.isValid,
        error: validationResult.error,
      ));
    } catch (e) {
      emit(ProfilePictureError(
        message: 'An unexpected error occurred while validating image',
        technicalDetails: e.toString(),
      ));
    }
  }

  /// Handle resetting profile picture state
  Future<void> _onResetProfilePicture(
    ResetProfilePictureEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    emit(const ProfilePictureInitial());
  }

  /// Handle retry upload operation
  Future<void> _onRetryUpload(
    RetryUploadEvent event,
    Emitter<ProfilePictureState> emit,
  ) async {
    // Retry the last operation based on the event type
    if (event.lastOperation == ProfilePictureOperation.pickFromGallery) {
      await _onPickFromGallery(const PickFromGalleryEvent(), emit);
    } else if (event.lastOperation == ProfilePictureOperation.takePhoto) {
      await _onTakePhoto(const TakePhotoEvent(), emit);
    } else if (event.lastOperation == ProfilePictureOperation.removeProfilePicture && event.imageUrl != null) {
      await _onRemoveProfilePicture(RemoveProfilePictureEvent(imageUrl: event.imageUrl!), emit);
    }
  }
}
