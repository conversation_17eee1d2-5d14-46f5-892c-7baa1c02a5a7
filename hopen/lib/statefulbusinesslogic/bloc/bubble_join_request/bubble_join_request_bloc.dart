import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../repositories/bubble/bubble_join_request_repository.dart';
import '../../../repositories/user/user_repository.dart';
import 'bubble_join_request_event.dart';
import 'bubble_join_request_state.dart';

class BubbleJoinRequestBloc extends Bloc<BubbleJoinRequestEvent, BubbleJoinRequestState> {

  BubbleJoinRequestBloc({
    required BubbleJoinRequestRepository repository,
    required UserRepository userRepository,
    BuildContext? context,
  })  : _repository = repository,
        _userRepository = userRepository,
        _context = context,
        super(const BubbleJoinRequestState()) {
    on<AcceptBubbleJoinRequestEvent>(_onAcceptJoinRequest);
    on<DeclineBubbleJoinRequestEvent>(_onDeclineJoinRequest);
    on<ResetBubbleJoinRequestEvent>(_onResetJoinRequest);
  }
  final BubbleJoinRequestRepository _repository;
  final UserRepository _userRepository;
  final BuildContext? _context;

  Future<void> _onAcceptJoinRequest(
    AcceptBubbleJoinRequestEvent event,
    Emitter<BubbleJoinRequestState> emit,
  ) async {
    try {
      emit(state.copyWith(status: BubbleJoinRequestStatus.accepting));

      // Get the requester's name
      final requester = await _userRepository.getUserById(event.requesterId);
      if (requester == null) {
        throw Exception('Requester not found');
      }

      // Get the bubble details
      final bubbleResult = await _repository.getBubbleDetails(event.bubbleId);
      
      bubbleResult.fold(
        onSuccess: (bubble) async {
          // Accept the join request
          final acceptResult = await _repository.acceptJoinRequest(event.bubbleId, event.requestId);
          
          acceptResult.fold(
            onSuccess: (_) async {
              // For now, assume it's not the last member to accept (simplified logic)
              const isLastMemberToAccept = false;

              // If this is not the last member to accept, notify other bubble members
              if (!isLastMemberToAccept) {
                // Get the current user's name
                final currentUser = await _userRepository.getCurrentUser();
                if (currentUser == null) {
                  throw Exception('Current user not found');
                }

                // Notify other bubble members about the propose request via unified notification service
                // The notification will be handled by the real-time service and dispatched to appropriate handlers
                // This replaces the direct dialog showing with proper notification flow
              }

              emit(state.copyWith(
                status: BubbleJoinRequestStatus.accepted,
                isLastMemberToAccept: isLastMemberToAccept,
              ),);
            },
            onFailure: (error) {
              emit(state.copyWith(
                status: BubbleJoinRequestStatus.error,
                errorMessage: error.toString(),
              ),);
            },
          );
        },
        onFailure: (error) {
          emit(state.copyWith(
            status: BubbleJoinRequestStatus.error,
            errorMessage: error.toString(),
          ),);
        },
      );
    } catch (e) {
      emit(state.copyWith(
        status: BubbleJoinRequestStatus.error,
        errorMessage: e.toString(),
      ),);
    }
  }

  Future<void> _onDeclineJoinRequest(
    DeclineBubbleJoinRequestEvent event,
    Emitter<BubbleJoinRequestState> emit,
  ) async {
    try {
      emit(state.copyWith(status: BubbleJoinRequestStatus.declining));

      await _repository.declineJoinRequest(event.bubbleId, event.requestId);

      emit(state.copyWith(status: BubbleJoinRequestStatus.declined));
    } catch (e) {
      emit(state.copyWith(
        status: BubbleJoinRequestStatus.error,
        errorMessage: e.toString(),
      ),);
    }
  }

  void _onResetJoinRequest(
    ResetBubbleJoinRequestEvent event,
    Emitter<BubbleJoinRequestState> emit,
  ) {
    emit(const BubbleJoinRequestState());
  }
}