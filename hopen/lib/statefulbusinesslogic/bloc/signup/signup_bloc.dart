import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

// import '../../core/user/user.dart'; // Removed this line
import '../../../statefulbusinesslogic/core/models/user_model.dart'
    as core_models; // Corrected import
import '../../core/usecases/signup_usecase.dart'; // Corrected path
import '../../core/error/result.dart'; // Import for PartialSuccess
import '../../../repositories/profile_picture/profile_picture_repository.dart';
// Removed: import '../../../../repositories/auth_repository.dart'; // UseCase handles repository

part 'signup_event.dart';
part 'signup_state.dart';

class SignUpBloc extends Bloc<SignUpEvent, SignUpState> {
  SignUpBloc({
    required SignUpUseCase signUpUseCase,
    required ProfilePictureRepository profilePictureRepository,
  })  : _signUpUseCase = signUpUseCase,
        _profilePictureRepository = profilePictureRepository,
        super(SignUpInitial()) {
    on<SignUpSubmitted>(_onSignUpSubmitted);
  }
  final SignUpUseCase _signUpUseCase;
  final ProfilePictureRepository _profilePictureRepository;

  Future<void> _onSignUpSubmitted(
    SignUpSubmitted event,
    Emitter<SignUpState> emit,
  ) async {
    emit(SignUpLoading());
    final params = SignUpParams(
      firstName: event.firstName,
      lastName: event.lastName,
      email: event.email,
      password: event.password,
      username: event.username,
      birthday: event.birthday,
      profilePicturePathOrUrl: event.profilePicturePathOrUrl,
      notificationsEnabled: event.notificationsEnabled,
    );
    final result = await _signUpUseCase(params);

    if (result.isSuccess) {
      final user = result.data as core_models.UserModel;

      // If a local profile picture path was provided, upload it and update user profile
      if (params.profilePicturePathOrUrl != null &&
          !params.profilePicturePathOrUrl!.startsWith('http')) {
        try {
          final profilePictureUrl = await _profilePictureRepository.uploadLocalProfilePicture(
            params.profilePicturePathOrUrl!,
          );

          // Update the user model with the uploaded profile picture URL
          if (profilePictureUrl != null) {
            final updatedUser = user.copyWith(profilePictureUrl: profilePictureUrl);
            emit(SignUpSuccess(user: updatedUser));
            return;
          }
        } catch (e) {
          // Log error but don't fail signup - profile picture upload is not critical
          print('Failed to upload profile picture during signup: $e');
        }
      }

      emit(SignUpSuccess(user: user));
    } else if (result.isPartialSuccess) {
      // Account created but authentication failed
      final partialResult = result as PartialSuccess;
      final user = partialResult.data as core_models.UserModel;

      // Still try to upload profile picture for partial success
      if (params.profilePicturePathOrUrl != null &&
          !params.profilePicturePathOrUrl!.startsWith('http')) {
        try {
          await _profilePictureRepository.uploadLocalProfilePicture(
            params.profilePicturePathOrUrl!,
          );
        } catch (e) {
          print('Failed to upload profile picture during partial signup: $e');
        }
      }

      emit(SignUpPartialSuccess(user: user, message: partialResult.message));
    } else {
      emit(SignUpFailure(message: result.error.userMessage));
    }
  }
}
