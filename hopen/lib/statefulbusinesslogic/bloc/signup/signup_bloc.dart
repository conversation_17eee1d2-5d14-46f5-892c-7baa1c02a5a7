import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

// import '../../core/user/user.dart'; // Removed this line
import '../../../statefulbusinesslogic/core/models/user_model.dart'
    as core_models; // Corrected import
import '../../core/usecases/signup_usecase.dart'; // Corrected path
import '../../core/error/result.dart'; // Import for PartialSuccess
import '../../../repositories/profile_picture/profile_picture_repository.dart';
// Removed: import '../../../../repositories/auth_repository.dart'; // UseCase handles repository

part 'signup_event.dart';
part 'signup_state.dart';

class SignUpBloc extends Bloc<SignUpEvent, SignUpState> {
  SignUpBloc({
    required SignUpUseCase signUpUseCase,
    required ProfilePictureRepository profilePictureRepository,
  })  : _signUpUseCase = signUpUseCase,
        _profilePictureRepository = profilePictureRepository,
        super(SignUpInitial()) {
    on<SignUpSubmitted>(_onSignUpSubmitted);
  }
  final SignUpUseCase _signUpUseCase;
  final ProfilePictureRepository _profilePictureRepository;

  Future<void> _onSignUpSubmitted(
    SignUpSubmitted event,
    Emitter<SignUpState> emit,
  ) async {
    emit(SignUpLoading());
    final params = SignUpParams(
      firstName: event.firstName,
      lastName: event.lastName,
      email: event.email,
      password: event.password,
      username: event.username,
      birthday: event.birthday,
      profilePicturePathOrUrl: event.profilePicturePathOrUrl,
      notificationsEnabled: event.notificationsEnabled,
    );
    final result = await _signUpUseCase(params);

    if (result.isSuccess) {
      // If a local profile picture path was provided, upload it now
      if (params.profilePicturePathOrUrl != null &&
          !params.profilePicturePathOrUrl!.startsWith('http')) {
        await _profilePictureRepository.uploadLocalProfilePicture(
          params.profilePicturePathOrUrl!,
        );
      }

      emit(SignUpSuccess(user: result.data as core_models.UserModel));
    } else if (result.isPartialSuccess) {
      // Account created but authentication failed
      final partialResult = result as PartialSuccess;
      emit(SignUpPartialSuccess(user: partialResult.data as core_models.UserModel, message: partialResult.message));
    } else {
      emit(SignUpFailure(message: result.error.userMessage));
    }
  }
}
