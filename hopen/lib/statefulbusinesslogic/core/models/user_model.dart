import 'package:equatable/equatable.dart';
import 'bubble_membership_status.dart';

// Re-export from the repositories layer to maintain compatibility
// This follows the dependency rule: StatefulBusinessLogic can import from Repositories

enum OnlineStatus {
  // You might manage this more dynamically, but for the model:
  online,
  offline,
}

class UserModel extends Equatable {
  // Added to track onboarding completion

  const UserModel({
    required this.id,
    this.email,
    this.birthday,
    this.firstName,
    this.lastName,
    this.username, // Added
    this.profilePictureUrl,
    this.friendIds = const [],
    this.bubbleId,
    this.contactIds = const [],
    this.onlineStatus = OnlineStatus.offline, // Default
    this.bubbleStatus = BubbleMembershipStatus.noBubble, // Default
    this.blockedUserIds = const [], // Added
    this.pendingSentContactRequestIds = const [], // Added
    this.pendingReceivedContactRequestIds = const [], // Added
    this.pendingSentBubbleRequestUserIds = const [], // Added
    this.pendingReceivedBubbleRequestUserIds = const [], // Added
    this.hasCompletedOnboarding = false, // Default to false for new users
    this.mutualFriends = const [],
    this.mutualContacts = const [],
    this.profilePageActionType,
    this.profilePageButtonText,
    this.isProfilePageButtonEnabled = false,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    List<String> parseStringList(dynamic list) {
      if (list is List) {
        return list.map((e) => e.toString()).toList();
      }
      return [];
    }

    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String?,
      birthday:
          json['birthday'] != null
              ? DateTime.tryParse(json['birthday'] as String? ?? '')
              : null,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      username: json['username'] as String?,
      profilePictureUrl: json['profile_pic_url'] as String?,
      friendIds: parseStringList(json['mutual_friends']),
      bubbleId: json['bubbleId'] as String?,
      contactIds: parseStringList(json['mutual_contacts']),
      onlineStatus:
          json['onlineStatus'] != null
              ? OnlineStatus.values.firstWhere(
                (e) => e.toString() == json['onlineStatus'],
                orElse: () => OnlineStatus.offline,
              )
              : OnlineStatus.offline,
      bubbleStatus:
          json['bubbleStatus'] != null
              ? BubbleMembershipStatus.values.firstWhere(
                (e) => e.toString() == json['bubbleStatus'],
                orElse: () => BubbleMembershipStatus.noBubble,
              )
              : BubbleMembershipStatus.noBubble,
      blockedUserIds: parseStringList(json['blockedUserIds']),
      pendingSentContactRequestIds: parseStringList(
        json['pendingSentContactRequestIds'],
      ),
      pendingReceivedContactRequestIds: parseStringList(
        json['pendingReceivedContactRequestIds'],
      ),
      pendingSentBubbleRequestUserIds: parseStringList(
        json['pendingSentBubbleRequestUserIds'],
      ),
      pendingReceivedBubbleRequestUserIds: parseStringList(
        json['pendingReceivedBubbleRequestUserIds'],
      ),
      hasCompletedOnboarding: json['is_profile_page_button_enabled'] as bool? ?? false,
      mutualFriends: parseStringList(json['mutual_friends']),
      mutualContacts: parseStringList(json['mutual_contacts']),
      profilePageActionType: json['profile_page_action_type'] as String?,
      profilePageButtonText: json['profile_page_button_text'] as String?,
      isProfilePageButtonEnabled: json['is_profile_page_button_enabled'] as bool? ?? false,
    );
  }
  final String id; // The UserId
  final String? email; // Made nullable as per previous discussions
  final DateTime? birthday; // Made nullable
  final String? firstName; // Made nullable
  final String? lastName; // Made nullable
  final String? username; // Added
  final String? profilePictureUrl; // Nullable if user hasn't set one
  final List<String> friendIds; // List of UserIds
  final String? bubbleId; // ID of the bubble they are a member of, if any
  final List<String> contactIds; // List of UserIds
  final OnlineStatus onlineStatus;
  final BubbleMembershipStatus bubbleStatus;
  final List<String> blockedUserIds; // Added
  final List<String> pendingSentContactRequestIds; // Added
  final List<String> pendingReceivedContactRequestIds; // Added
  final List<String> pendingSentBubbleRequestUserIds; // Added
  final List<String> pendingReceivedBubbleRequestUserIds; // Added
  final bool hasCompletedOnboarding;
  final List<String> mutualFriends;
  final List<String> mutualContacts;
  final String? profilePageActionType;
  final String? profilePageButtonText;
  final bool isProfilePageButtonEnabled;

  // For easy updates (optional but recommended)
  UserModel copyWith({
    String? id,
    String? email,
    DateTime? birthday,
    String? firstName,
    String? lastName,
    String? username, // Added
    String? profilePictureUrl,
    List<String>? friendIds,
    String? bubbleId, // Allow setting to null
    bool clearBubbleId = false, // Helper to explicitly set bubbleId to null
    List<String>? contactIds,
    OnlineStatus? onlineStatus,
    BubbleMembershipStatus? bubbleStatus,
    List<String>? blockedUserIds, // Added
    List<String>? pendingSentContactRequestIds, // Added
    List<String>? pendingReceivedContactRequestIds, // Added
    List<String>? pendingSentBubbleRequestUserIds, // Added
    List<String>? pendingReceivedBubbleRequestUserIds, // Added
    bool? hasCompletedOnboarding, // Added
    List<String>? mutualFriends,
    List<String>? mutualContacts,
    String? profilePageActionType,
    String? profilePageButtonText,
    bool? isProfilePageButtonEnabled,
  }) => UserModel(
    id: id ?? this.id,
    email: email ?? this.email, // Email can't change as per requirement
    birthday: birthday ?? this.birthday, // Birthday can't change
    firstName: firstName ?? this.firstName,
    lastName: lastName ?? this.lastName,
    username: username ?? this.username, // Added
    profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
    friendIds: friendIds ?? this.friendIds,
    bubbleId: clearBubbleId ? null : (bubbleId ?? this.bubbleId),
    contactIds: contactIds ?? this.contactIds,
    onlineStatus: onlineStatus ?? this.onlineStatus,
    bubbleStatus: bubbleStatus ?? this.bubbleStatus,
    blockedUserIds: blockedUserIds ?? this.blockedUserIds, // Added
    pendingSentContactRequestIds:
        pendingSentContactRequestIds ??
        this.pendingSentContactRequestIds, // Added
    pendingReceivedContactRequestIds:
        pendingReceivedContactRequestIds ??
        this.pendingReceivedContactRequestIds, // Added
    pendingSentBubbleRequestUserIds:
        pendingSentBubbleRequestUserIds ??
        this.pendingSentBubbleRequestUserIds, // Added
    pendingReceivedBubbleRequestUserIds:
        pendingReceivedBubbleRequestUserIds ??
        this.pendingReceivedBubbleRequestUserIds, // Added
    hasCompletedOnboarding:
        hasCompletedOnboarding ?? this.hasCompletedOnboarding, // Added
    mutualFriends: mutualFriends ?? this.mutualFriends,
    mutualContacts: mutualContacts ?? this.mutualContacts,
    profilePageActionType: profilePageActionType ?? this.profilePageActionType,
    profilePageButtonText: profilePageButtonText ?? this.profilePageButtonText,
    isProfilePageButtonEnabled: isProfilePageButtonEnabled ?? this.isProfilePageButtonEnabled,
  );

  // For Equatable
  @override
  List<Object?> get props => [
    id,
    email,
    birthday,
    firstName,
    lastName,
    username, // Added
    profilePictureUrl,
    friendIds,
    bubbleId,
    contactIds,
    onlineStatus,
    bubbleStatus,
    blockedUserIds, // Added
    pendingSentContactRequestIds, // Added
    pendingReceivedContactRequestIds, // Added
    pendingSentBubbleRequestUserIds, // Added
    pendingReceivedBubbleRequestUserIds, // Added
    hasCompletedOnboarding, // Added
    mutualFriends,
    mutualContacts,
    profilePageActionType,
    profilePageButtonText,
    isProfilePageButtonEnabled,
  ];

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, firstName: $firstName, lastName: $lastName, username: $username, profilePictureUrl: $profilePictureUrl, hasCompletedOnboarding: $hasCompletedOnboarding)';
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'email': email,
    'birthday': birthday?.toIso8601String(),
    'first_name': firstName,
    'last_name': lastName,
    'username': username,
    'profile_pic_url': profilePictureUrl,
    'friend_ids': friendIds,
    'contact_ids': contactIds,
    'bubbleId': bubbleId,
    'onlineStatus': onlineStatus.toString(),
    'bubbleStatus': bubbleStatus.toString(),
    'blockedUserIds': blockedUserIds,
    'pendingSentContactRequestIds': pendingSentContactRequestIds,
    'pendingReceivedContactRequestIds': pendingReceivedContactRequestIds,
    'pendingSentBubbleRequestUserIds': pendingSentBubbleRequestUserIds,
    'pendingReceivedBubbleRequestUserIds': pendingReceivedBubbleRequestUserIds,
    'has_completed_onboarding': hasCompletedOnboarding,
    'mutual_friends': mutualFriends,
    'mutual_contacts': mutualContacts,
    'profile_page_action_type': profilePageActionType,
    'profile_page_button_text': profilePageButtonText,
    'is_profile_page_button_enabled': isProfilePageButtonEnabled,
  };

  Map<String, dynamic> toMap() => toJson();

  // Helper getter for full name
  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    } else if (username != null) {
      return username!;
    } else {
      return 'Unknown User';
    }
  }
}
