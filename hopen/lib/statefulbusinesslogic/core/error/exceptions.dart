import 'base_error.dart';

/// Core-level exceptions for infrastructure and external service errors.
/// 
/// These exceptions represent errors that occur at the infrastructure level
/// and should be caught and converted to appropriate failures in the domain layer.
abstract class AppException implements Exception, BaseError {
  const AppException({
    required this.message,
    this.errorContext,
  });
  
  final String message;
  final Map<String, dynamic>? errorContext;

  @override
  String get userMessage => message;

  @override
  String get technicalMessage => message;

  @override
  String get code => runtimeType.toString().toUpperCase();

  @override
  Map<String, dynamic>? get context => errorContext;
  
  @override
  String toString() => '${runtimeType.toString()}: $message';
}

/// Server-related exceptions (5xx errors, backend issues)
class ServerException extends AppException {
  const ServerException({
    super.message = 'Server error occurred',
    super.errorContext,
  });
  
  @override
  String get userMessage => 'Server error occurred. Please try again later.';
  
  @override
  String get code => 'SERVER_EXCEPTION';
}

/// Cache-related exceptions (Redis, local storage issues)
class CacheException extends AppException {
  const CacheException({
    super.message = 'Cache error occurred',
    super.errorContext,
  });
  
  @override
  String get userMessage => 'Temporary data access issue. Please try again.';
  
  @override
  String get code => 'CACHE_EXCEPTION';
}

/// Network-related exceptions (connectivity, timeout issues)
class NetworkException extends AppException {
  const NetworkException({
    super.message = 'Network error occurred',
    super.errorContext,
  });
  
  @override
  String get userMessage => 'Network connection issue. Please check your internet connection.';
  
  @override
  String get code => 'NETWORK_EXCEPTION';
}

/// Authentication-related exceptions (login, token issues)
class AuthenticationException extends AppException {
  const AuthenticationException({
    super.message = 'Authentication failed',
    super.errorContext,
  });
  
  @override
  String get userMessage => 'Authentication failed. Please log in again.';
  
  @override
  String get code => 'AUTHENTICATION_EXCEPTION';
}

/// Partial success exceptions (operations that succeeded with warnings)
class PartialSuccessException extends AppException {
  const PartialSuccessException({
    required super.message,
    required this.data,
    super.errorContext,
  });
  
  final Object data;
  
  @override
  String get userMessage => message;
  
  @override
  String get code => 'PARTIAL_SUCCESS_EXCEPTION';
  
  @override
  String toString() => '${runtimeType.toString()}: $message (Data: $data)';
}

/// Bubble-specific exception types for better error handling and debugging.
/// 
/// These exceptions represent errors that occur during bubble operations
/// and provide detailed context for debugging and user feedback.

/// Base exception for all bubble-related errors.
/// 
/// Provides a foundation for all bubble-specific exceptions with
/// consistent error handling and context management.
abstract class BubbleException extends AppException {
  const BubbleException(
    String message, {
    this.errorCode,
    this.details,
    super.errorContext,
  }) : super(message: message);
  
  final String? errorCode;
  final dynamic details;

  @override
  String get userMessage => message;

  @override
  String get technicalMessage => errorCode != null 
    ? '$message (Code: $errorCode)'
    : message;

  @override
  String get code => errorCode ?? 'BUBBLE_EXCEPTION';

  @override
  Map<String, dynamic>? get context => {
    if (details != null) 'details': details,
    ...?errorContext,
  };

  @override
  String toString() => '${runtimeType.toString()}: $message${errorCode != null ? ' (Code: $errorCode)' : ''}';
}

/// Exception thrown when bubble validation fails.
class BubbleValidationException extends BubbleException {
  const BubbleValidationException(
    super.message, {
    super.errorCode,
    super.details,
    super.errorContext,
  });

  @override
  String get userMessage => 'Invalid bubble information. Please check your input.';
}

/// Exception thrown when member operations fail.
class BubbleMemberException extends BubbleException {
  const BubbleMemberException(
    super.message, {
    this.memberId,
    this.bubbleId,
    super.errorCode,
    super.details,
    super.errorContext,
  });
  
  final String? memberId;
  final String? bubbleId;

  @override
  String get userMessage => 'Member operation failed. Please try again.';
  
  @override
  String get technicalMessage => '$message'
      '${memberId != null ? ' (Member: $memberId)' : ''}'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
  
  @override
  Map<String, dynamic>? get context => {
    if (memberId != null) 'memberId': memberId,
    if (bubbleId != null) 'bubbleId': bubbleId,
    ...super.context ?? {},
  };

  @override
  String toString() => 'BubbleMemberException: $message'
      '${memberId != null ? ' (Member: $memberId)' : ''}'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
}

/// Exception thrown when voting operations fail.
class BubbleVotingException extends BubbleException {
  const BubbleVotingException(
    super.message, {
    this.voterId,
    this.targetMemberId,
    this.bubbleId,
    super.errorCode,
    super.details,
    super.errorContext,
  });
  
  final String? voterId;
  final String? targetMemberId;
  final String? bubbleId;

  @override
  String get userMessage => 'Voting operation failed. Please try again.';
  
  @override
  String get technicalMessage => '$message'
      '${voterId != null ? ' (Voter: $voterId)' : ''}'
      '${targetMemberId != null ? ' (Target: $targetMemberId)' : ''}'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
  
  @override
  Map<String, dynamic>? get context => {
    if (voterId != null) 'voterId': voterId,
    if (targetMemberId != null) 'targetMemberId': targetMemberId,
    if (bubbleId != null) 'bubbleId': bubbleId,
    ...super.context ?? {},
  };

  @override
  String toString() => 'BubbleVotingException: $message'
      '${voterId != null ? ' (Voter: $voterId)' : ''}'
      '${targetMemberId != null ? ' (Target: $targetMemberId)' : ''}'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
}

/// Exception thrown when bubble lifecycle operations fail.
class BubbleLifecycleException extends BubbleException {
  const BubbleLifecycleException(
    super.message, {
    this.bubbleId,
    this.currentStatus,
    this.attemptedOperation,
    super.errorCode,
    super.details,
    super.errorContext,
  });
  
  final String? bubbleId;
  final String? currentStatus;
  final String? attemptedOperation;

  @override
  String get userMessage => 'Bubble operation failed. Please try again.';
  
  @override
  String get technicalMessage => '$message'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${currentStatus != null ? ' (Status: $currentStatus)' : ''}'
      '${attemptedOperation != null ? ' (Operation: $attemptedOperation)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
  
  @override
  Map<String, dynamic>? get context => {
    if (bubbleId != null) 'bubbleId': bubbleId,
    if (currentStatus != null) 'currentStatus': currentStatus,
    if (attemptedOperation != null) 'attemptedOperation': attemptedOperation,
    ...super.context ?? {},
  };

  @override
  String toString() => 'BubbleLifecycleException: $message'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${currentStatus != null ? ' (Status: $currentStatus)' : ''}'
      '${attemptedOperation != null ? ' (Operation: $attemptedOperation)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
}

/// Exception thrown when bubble capacity limits are exceeded.
class BubbleCapacityException extends BubbleException {
  const BubbleCapacityException(
    super.message, {
    required this.currentCount,
    required this.maxCapacity,
    this.bubbleId,
    super.errorCode,
    super.details,
    super.errorContext,
  });
  
  final int currentCount;
  final int maxCapacity;
  final String? bubbleId;

  @override
  String get userMessage => 'Bubble is at maximum capacity.';
  
  @override
  String get technicalMessage => '$message'
      ' (Current: $currentCount, Max: $maxCapacity)'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
  
  @override
  Map<String, dynamic>? get context => {
    'currentCount': currentCount,
    'maxCapacity': maxCapacity,
    if (bubbleId != null) 'bubbleId': bubbleId,
    ...super.context ?? {},
  };

  @override
  String toString() => 'BubbleCapacityException: $message'
      ' (Current: $currentCount, Max: $maxCapacity)'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
}

/// Exception thrown when bubble duration calculations fail.
class BubbleDurationException extends BubbleException {
  const BubbleDurationException(
    super.message, {
    this.creationDate,
    this.bubbleId,
    super.errorCode,
    super.details,
    super.errorContext,
  });
  
  final DateTime? creationDate;
  final String? bubbleId;

  @override
  String get userMessage => 'Bubble duration calculation failed.';
  
  @override
  String get technicalMessage => '$message'
      '${creationDate != null ? ' (Created: $creationDate)' : ''}'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
  
  @override
  Map<String, dynamic>? get context => {
    if (creationDate != null) 'creationDate': creationDate!.toIso8601String(),
    if (bubbleId != null) 'bubbleId': bubbleId,
    ...super.context ?? {},
  };

  @override
  String toString() => 'BubbleDurationException: $message'
      '${creationDate != null ? ' (Created: $creationDate)' : ''}'
      '${bubbleId != null ? ' (Bubble: $bubbleId)' : ''}'
      '${errorCode != null ? ' (Code: $errorCode)' : ''}';
}
