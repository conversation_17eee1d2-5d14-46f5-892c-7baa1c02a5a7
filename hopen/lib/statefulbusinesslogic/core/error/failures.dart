import 'result.dart';

// Core failures for domain layer error handling
abstract class Failure extends AppError {
  const Failure({required this.message});
  final String message;

  @override
  String get userMessage => message;

  @override
  String get technicalMessage => message;

  @override
  String get code => runtimeType.toString().toUpperCase();

  @override
  Map<String, dynamic>? get context => null;
}

class ServerFailure extends Failure {
  const ServerFailure({required super.message});
}

class CacheFailure extends Failure {
  const CacheFailure({required super.message});
}

class NetworkFailure extends Failure {
  const NetworkFailure({required super.message});
}

class AuthenticationFailure extends Failure {
  const AuthenticationFailure({required super.message});
}
