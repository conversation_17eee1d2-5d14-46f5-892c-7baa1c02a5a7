import 'base_error.dart';

/// Standardized Result type for handling success and failure states
/// 
/// This provides a type-safe way to handle operations that can fail
/// without throwing exceptions in normal flow control.
sealed class Result<T> {
  const Result();

  /// Creates a successful result containing data
  factory Result.success(T data) = Success<T>;

  /// Creates a failed result containing an error
  factory Result.failure(BaseError error) = Failure<T>;

  /// Creates a partial success result containing data and a message
  factory Result.partialSuccess(T data, String message) = PartialSuccess<T>;

  /// Check if this result represents success
  bool get isSuccess => this is Success<T>;

  /// Check if this result represents failure
  bool get isFailure => this is Failure<T>;

  /// Check if this result represents partial success
  bool get isPartialSuccess => this is PartialSuccess<T>;
  
  /// Get the success data (throws if called on failure)
  T get data => (this as Success<T>).data;
  
  /// Get the error (throws if called on success)
  BaseError get error => (this as Failure<T>).error;
  
  /// Transform the result using a fold operation
  R fold<R>({
    required R Function(T data) onSuccess,
    required R Function(BaseError error) onFailure,
    R Function(T data, String message)? onPartialSuccess,
  }) => switch (this) {
      Success(data: final data) => onSuccess(data),
      PartialSuccess(data: final data, message: final message) =>
        onPartialSuccess?.call(data, message) ?? onSuccess(data),
      Failure(error: final error) => onFailure(error),
    };
  
  /// Transform the success value if present
  Result<R> map<R>(R Function(T data) transform) => switch (this) {
      Success(data: final data) => Result.success(transform(data)),
      PartialSuccess(data: final data, message: final message) =>
        Result.partialSuccess(transform(data), message),
      Failure(error: final error) => Result.failure(error),
    };

  /// Chain operations that return Result
  Result<R> flatMap<R>(Result<R> Function(T data) transform) => switch (this) {
      Success(data: final data) => transform(data),
      PartialSuccess(data: final data, message: final _) => transform(data),
      Failure(error: final error) => Result.failure(error),
    };

  /// Get data or return default value
  T getOrElse(T defaultValue) => switch (this) {
      Success(data: final data) => data,
      PartialSuccess(data: final data, message: final _) => data,
      Failure() => defaultValue,
    };

  /// Get data or compute default value
  T getOrElseGet(T Function() defaultValueProvider) => switch (this) {
      Success(data: final data) => data,
      PartialSuccess(data: final data, message: final _) => data,
      Failure() => defaultValueProvider(),
    };
}

/// Success case of Result
final class Success<T> extends Result<T> {

  const Success(this.data);
  @override
  final T data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Success<T> && other.data == data);

  @override
  int get hashCode => data.hashCode;

  @override
  String toString() => 'Success($data)';
}

/// Partial success case of Result (success with additional message)
final class PartialSuccess<T> extends Result<T> {

  const PartialSuccess(this.data, this.message);
  @override
  final T data;
  final String message;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is PartialSuccess<T> &&
       other.data == data &&
       other.message == message);

  @override
  int get hashCode => Object.hash(data, message);

  @override
  String toString() => 'PartialSuccess($data, $message)';
}

/// Failure case of Result
final class Failure<T> extends Result<T> {
  
  const Failure(this.error);
  @override
  final BaseError error;
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Failure<T> && other.error == error);
  
  @override
  int get hashCode => error.hashCode;
  
  @override
  String toString() => 'Failure($error)';
}

import 'base_error.dart';

/// Network-related errors
final class NetworkError extends BaseError {
  
  const NetworkError({
    required this.message,
    this.statusCode,
    Map<String, dynamic>? context,
  }) : _context = context;
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? _context;
  
  @override
  String get userMessage => switch (statusCode) {
    null => 'Network connection failed. Please check your internet connection.',
    >= 500 => 'Server is temporarily unavailable. Please try again later.',
    404 => 'The requested resource was not found.',
    403 => "You don't have permission to perform this action.",
    401 => 'Please log in again to continue.',
    _ => 'Network error occurred. Please try again.',
  };
  
  @override
  String get technicalMessage => 'Network error: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
  
  @override
  String? get code => 'NETWORK_ERROR';
  
  @override
  Map<String, dynamic>? get context => _context;
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is NetworkError && 
       other.message == message && 
       other.statusCode == statusCode);
  
  @override
  int get hashCode => Object.hash(message, statusCode);
  
  @override
  String toString() => 'NetworkError($message, $statusCode)';
}

/// Business logic validation errors
final class ValidationError extends BaseError {
  
  const ValidationError({
    required this.message, this.field = '',
    String? code,
    Map<String, dynamic>? context,
  }) : _code = code, _context = context;
  final String field;
  final String message;
  final String? _code;
  final Map<String, dynamic>? _context;
  
  @override
  String get userMessage => message;
  
  @override
  String get technicalMessage => field.isEmpty 
    ? 'Validation error: $message'
    : 'Validation error for field "$field": $message';
  
  @override
  String? get code => _code ?? 'VALIDATION_ERROR';
  
  @override
  Map<String, dynamic>? get context => _context;
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ValidationError && 
       other.field == field && 
       other.message == message);
  
  @override
  int get hashCode => Object.hash(field, message);
  
  @override
  String toString() => field.isEmpty 
    ? 'ValidationError($message)'
    : 'ValidationError($field: $message)';
}

/// Authentication and authorization errors
final class AuthenticationError extends BaseError {
  
  const AuthenticationError({
    required this.message,
    String? code,
    Map<String, dynamic>? context,
  }) : _code = code, _context = context;
  final String message;
  final String? _code;
  final Map<String, dynamic>? _context;
  
  @override
  String get userMessage => 'Authentication failed. Please log in again.';
  
  @override
  String get technicalMessage => 'Authentication error: $message';
  
  @override
  String? get code => _code ?? 'AUTHENTICATION_ERROR';
  
  @override
  Map<String, dynamic>? get context => _context;
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AuthenticationError && other.message == message);
  
  @override
  int get hashCode => message.hashCode;
  
  @override
  String toString() => 'AuthenticationError($message)';
}

/// Unexpected errors that shouldn't occur in normal operation
final class UnexpectedError extends BaseError {
  
  const UnexpectedError({
    required this.message,
    this.exception,
    this.stackTrace,
    Map<String, dynamic>? context,
  }) : _context = context;
  final String message;
  final Exception? exception;
  final StackTrace? stackTrace;
  final Map<String, dynamic>? _context;
  
  @override
  String get userMessage => 'An unexpected error occurred. Please try again.';
  
  @override
  String get technicalMessage => 'Unexpected error: $message${exception != null ? ' (${exception.toString()})' : ''}';
  
  @override
  String? get code => 'UNEXPECTED_ERROR';
  
  @override
  Map<String, dynamic>? get context => _context;
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is UnexpectedError && 
       other.message == message && 
       other.exception == exception);
  
  @override
  int get hashCode => Object.hash(message, exception);
  
  @override
  String toString() => 'UnexpectedError($message)';
}

/// Error when bubble is not found
final class BubbleNotFoundError extends BaseError {
  
  const BubbleNotFoundError(this.bubbleId);
  final String bubbleId;
  
  @override
  String get userMessage => "The bubble you're looking for doesn't exist or has been deleted.";
  
  @override
  String get technicalMessage => 'Bubble not found: $bubbleId';
  
  @override
  String get code => 'BUBBLE_NOT_FOUND';
  
  @override
  Map<String, dynamic> get context => {'bubbleId': bubbleId};
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is BubbleNotFoundError && other.bubbleId == bubbleId);
  
  @override
  int get hashCode => bubbleId.hashCode;
}

/// Error when user is not authorized to perform bubble operations
final class BubbleAccessDeniedError extends BaseError {
  
  const BubbleAccessDeniedError({
    required this.bubbleId, 
    required this.operation,
  });
  final String bubbleId;
  final String operation;
  
  @override
  String get userMessage => "You don't have permission to $operation this bubble.";
  
  @override
  String get technicalMessage => 'Access denied for operation "$operation" on bubble $bubbleId';
  
  @override
  String get code => 'BUBBLE_ACCESS_DENIED';
  
  @override
  Map<String, dynamic> get context => {
    'bubbleId': bubbleId,
    'operation': operation,
  };
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is BubbleAccessDeniedError && 
       other.bubbleId == bubbleId && 
       other.operation == operation);
  
  @override
  int get hashCode => Object.hash(bubbleId, operation);
}

/// Error when bubble capacity is exceeded
final class BubbleCapacityExceededError extends BaseError {
  
  const BubbleCapacityExceededError({
    required this.bubbleId,
    required this.currentCount,
    required this.maxCapacity,
  });
  final String bubbleId;
  final int currentCount;
  final int maxCapacity;
  
  @override
  String get userMessage => 'This bubble is full. Maximum $maxCapacity members allowed.';
  
  @override
  String get technicalMessage => 'Bubble capacity exceeded: $currentCount/$maxCapacity for bubble $bubbleId';
  
  @override
  String get code => 'BUBBLE_CAPACITY_EXCEEDED';
  
  @override
  Map<String, dynamic> get context => {
    'bubbleId': bubbleId,
    'currentCount': currentCount,
    'maxCapacity': maxCapacity,
  };
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is BubbleCapacityExceededError && 
       other.bubbleId == bubbleId &&
       other.currentCount == currentCount &&
       other.maxCapacity == maxCapacity);
  
  @override
  int get hashCode => Object.hash(bubbleId, currentCount, maxCapacity);
}

/// Error when user is not in any bubble
final class NotInBubbleError extends BaseError {
  const NotInBubbleError();
  
  @override
  String get userMessage => "You're not currently in any bubble.";
  
  @override
  String get technicalMessage => 'User not in bubble';
  
  @override
  String get code => 'NOT_IN_BUBBLE';
  
  @override
  Map<String, dynamic>? get context => null;
  
  @override
  bool operator ==(Object other) => other is NotInBubbleError;
  
  @override
  int get hashCode => 'NOT_IN_BUBBLE'.hashCode;
}

/// Error when bubble duration has expired
final class BubbleDurationExpiredError extends BaseError {
  
  const BubbleDurationExpiredError({
    required this.bubbleId,
    required this.expiredAt,
  });
  final String bubbleId;
  final DateTime expiredAt;
  
  @override
  String get userMessage => 'This bubble has expired and is no longer available.';
  
  @override
  String get technicalMessage => 'Bubble $bubbleId expired at $expiredAt';
  
  @override
  String get code => 'BUBBLE_DURATION_EXPIRED';
  
  @override
  Map<String, dynamic> get context => {
    'bubbleId': bubbleId,
    'expiredAt': expiredAt.toIso8601String(),
  };
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is BubbleDurationExpiredError && 
       other.bubbleId == bubbleId && 
       other.expiredAt == expiredAt);
  
  @override
  int get hashCode => Object.hash(bubbleId, expiredAt);
}

/// Helper extensions for working with Results
extension ResultHelpers<T> on Result<T> {
  /// Convert to Future<Result<T>>
  Future<Result<T>> toFuture() => Future.value(this);
  
  /// Handle both success and failure cases
  void when({
    required void Function(T data) onSuccess,
    required void Function(BaseError error) onFailure,
  }) {
    fold(
      onSuccess: onSuccess,
      onFailure: onFailure,
    );
  }
}

/// Helper for creating Results from try-catch blocks
class ResultUtils {
  const ResultUtils._();
  
  /// Safely execute a function and wrap result
  static Result<T> tryCall<T>(T Function() function) {
    try {
      return Result.success(function());
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedError(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      ),);
    }
  }
  
  /// Safely execute an async function and wrap result
  static Future<Result<T>> tryCallAsync<T>(Future<T> Function() function) async {
    try {
      final result = await function();
      return Result.success(result);
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedError(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      ),);
    }
  }
} 