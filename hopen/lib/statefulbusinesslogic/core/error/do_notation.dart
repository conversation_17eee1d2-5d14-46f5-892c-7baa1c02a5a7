import 'result.dart';
import 'base_error.dart';

/// Do notation for Result type - enables cleaner chaining of operations
///
/// Example usage:
/// ```dart
/// final result = ResultDo.Do(($) {
///   final user = $(getUserById(userId));
///   final bubble = $(getBubbleById(bubbleId));
///   final permissions = $(checkPermissions(user.id, bubble.id));
///
///   return BubbleWithPermissions(bubble, permissions);
/// });
/// ```
class ResultDo {
  const ResultDo._();

  /// Do notation constructor for Result
  static Result<T> Do<T>(T Function(R Function<R>(Result<R>)) computation) {
    BaseError? capturedError;

    R extract<R>(Result<R> result) {
      return result.fold(
        onSuccess: (data) => data,
        onFailure: (error) {
          capturedError = error;
          throw _DoNotationException();
        },
      );
    }

    try {
      final result = computation(extract);
      return capturedError != null
        ? Result.failure(capturedError!)
        : Result.success(result);
    } on _DoNotationException {
      return Result.failure(capturedError!);
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedError(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      ));
    }
  }
}

/// Do notation for async Result operations
class TaskResultDo {
  const TaskResultDo._();

  /// Async Do notation constructor for TaskResult
  static TaskResult<T> Do<T>(Future<T> Function(Future<R> Function<R>(TaskResult<R>)) computation) async {
    BaseError? capturedError;

    Future<R> extract<R>(TaskResult<R> taskResult) async {
      final result = await taskResult;
      return result.fold(
        onSuccess: (data) => data,
        onFailure: (error) {
          capturedError = error;
          throw _DoNotationException();
        },
      );
    }

    try {
      final result = await computation(extract);
      return capturedError != null
        ? Result.failure(capturedError!)
        : Result.success(result);
    } on _DoNotationException {
      return Result.failure(capturedError!);
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedError(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
        stackTrace: stackTrace,
      ));
    }
  }
}

/// Internal exception for Do notation control flow
class _DoNotationException implements Exception {}

/// Validation helpers for use with Do notation
class Validate {
  const Validate._();
  
  /// Validate that a value is not null
  static Result<T> notNull<T>(T? value, BaseError Function() onNull) {
    return value != null ? Result.success(value) : Result.failure(onNull());
  }
  
  /// Validate that a string is not empty
  static Result<String> notEmpty(String? value, BaseError Function() onEmpty) {
    return value != null && value.isNotEmpty 
      ? Result.success(value) 
      : Result.failure(onEmpty());
  }
  
  /// Validate that a list is not empty
  static Result<List<T>> notEmptyList<T>(List<T>? value, BaseError Function() onEmpty) {
    return value != null && value.isNotEmpty 
      ? Result.success(value) 
      : Result.failure(onEmpty());
  }
  
  /// Validate with a custom predicate
  static Result<T> that<T>(T value, bool Function(T) predicate, BaseError Function() onFalse) {
    return predicate(value) ? Result.success(value) : Result.failure(onFalse());
  }
  
  /// Validate multiple conditions (all must pass)
  static Result<T> all<T>(T value, List<bool Function(T)> predicates, BaseError Function() onFalse) {
    for (final predicate in predicates) {
      if (!predicate(value)) {
        return Result.failure(onFalse());
      }
    }
    return Result.success(value);
  }
  
  /// Validate at least one condition passes
  static Result<T> any<T>(T value, List<bool Function(T)> predicates, BaseError Function() onFalse) {
    for (final predicate in predicates) {
      if (predicate(value)) {
        return Result.success(value);
      }
    }
    return Result.failure(onFalse());
  }
}

/// Conditional execution helpers
class ResultIf {
  const ResultIf._();
  
  /// Execute operation only if condition is true
  static Result<T> when<T>(bool condition, Result<T> Function() operation, T Function() otherwise) {
    return condition ? operation() : Result.success(otherwise());
  }
  
  /// Execute operation only if condition is false
  static Result<T> unless<T>(bool condition, Result<T> Function() operation, T Function() otherwise) {
    return !condition ? operation() : Result.success(otherwise());
  }
}
