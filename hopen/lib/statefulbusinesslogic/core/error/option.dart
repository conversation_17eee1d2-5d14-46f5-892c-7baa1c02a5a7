import 'result.dart';
import 'base_error.dart';

/// Option type for handling nullable values functionally
/// 
/// Represents a value that may or may not be present, similar to nullable types
/// but with functional programming patterns for composition.
sealed class Option<T> {
  const Option();

  /// Creates an Option containing a value
  factory Option.some(T value) = Some<T>;

  /// Creates an empty Option
  factory Option.none() = None<T>;

  /// Creates an Option from a nullable value
  factory Option.fromNullable(T? value) => 
    value != null ? Option.some(value) : Option.none();

  /// Check if this option contains a value
  bool get isSome => this is Some<T>;

  /// Check if this option is empty
  bool get isNone => this is None<T>;

  /// Get the value (throws if None)
  T get value => (this as Some<T>).value;

  /// Transform the option using a fold operation
  R fold<R>({
    required R Function(T value) onSome,
    required R Function() onNone,
  }) => switch (this) {
    Some(value: final value) => onSome(value),
    None() => onNone(),
  };

  /// Transform the value if present
  Option<R> map<R>(R Function(T value) transform) => switch (this) {
    Some(value: final value) => Option.some(transform(value)),
    None() => Option.none(),
  };

  /// Chain operations that return Option
  Option<R> flatMap<R>(Option<R> Function(T value) transform) => switch (this) {
    Some(value: final value) => transform(value),
    None() => Option.none(),
  };

  /// Get value or return default
  T getOrElse(T defaultValue) => switch (this) {
    Some(value: final value) => value,
    None() => defaultValue,
  };

  /// Get value or compute default
  T getOrElseGet(T Function() defaultValueProvider) => switch (this) {
    Some(value: final value) => value,
    None() => defaultValueProvider(),
  };

  /// Filter values based on a predicate
  Option<T> filter(bool Function(T value) predicate) => switch (this) {
    Some(value: final value) => predicate(value) ? this : Option.none(),
    None() => this,
  };

  /// Convert to Result
  Result<T> toResult(BaseError Function() onNone) => switch (this) {
    Some(value: final value) => Result.success(value),
    None() => Result.failure(onNone()),
  };

  /// Convert to nullable value
  T? toNullable() => switch (this) {
    Some(value: final value) => value,
    None() => null,
  };

  /// Tap into the value without changing the option
  Option<T> tap(void Function(T value) action) => switch (this) {
    Some(value: final value) => this..let((_) => action(value)),
    None() => this,
  };

  /// Execute action if None
  Option<T> tapNone(void Function() action) => switch (this) {
    Some() => this,
    None() => this..let((_) => action()),
  };
}

/// Some case of Option (contains a value)
final class Some<T> extends Option<T> {
  const Some(this.value);
  final T value;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Some<T> && other.value == value);

  @override
  int get hashCode => value.hashCode;

  @override
  String toString() => 'Some($value)';
}

/// None case of Option (empty)
final class None<T> extends Option<T> {
  const None();

  @override
  bool operator ==(Object other) => other is None<T>;

  @override
  int get hashCode => 'None'.hashCode;

  @override
  String toString() => 'None()';
}

/// Extension for utility method
extension _LetExtension<T> on T {
  R let<R>(R Function(T) transform) => transform(this);
}

/// Extensions for working with Options
extension OptionHelpers<T> on Option<T> {
  /// Convert to Future<Option<T>>
  Future<Option<T>> toFuture() => Future.value(this);

  /// Handle both Some and None cases
  void when({
    required void Function(T value) onSome,
    required void Function() onNone,
  }) {
    fold(
      onSome: onSome,
      onNone: onNone,
    );
  }

  /// Zip with another Option
  Option<(T, R)> zip<R>(Option<R> other) => switch ((this, other)) {
    (Some(value: final a), Some(value: final b)) => Option.some((a, b)),
    _ => Option.none(),
  };

  /// Zip with another Option using a combiner function
  Option<R> zipWith<R, S>(Option<S> other, R Function(T, S) combiner) => switch ((this, other)) {
    (Some(value: final a), Some(value: final b)) => Option.some(combiner(a, b)),
    _ => Option.none(),
  };
}

/// Utility functions for Option
class OptionUtils {
  const OptionUtils._();

  /// Safely execute a function that might throw
  static Option<T> tryCatch<T>(T Function() function) {
    try {
      return Option.some(function());
    } catch (_) {
      return Option.none();
    }
  }

  /// Combine multiple Options into one
  static Option<List<T>> sequence<T>(List<Option<T>> options) {
    final List<T> values = [];
    for (final option in options) {
      if (option.isNone) {
        return Option.none();
      }
      values.add(option.value);
    }
    return Option.some(values);
  }

  /// Get the first Some value from a list of Options
  static Option<T> firstSome<T>(List<Option<T>> options) {
    for (final option in options) {
      if (option.isSome) {
        return option;
      }
    }
    return Option.none();
  }
}

/// Do notation for Option
class OptionDo {
  const OptionDo._();

  /// Do notation constructor for Option
  static Option<T> Do<T>(T Function(R Function<R>(Option<R>)) computation) {
    bool hasNone = false;

    R extract<R>(Option<R> option) {
      return option.fold(
        onSome: (value) => value,
        onNone: () {
          hasNone = true;
          throw _OptionDoNotationException();
        },
      );
    }

    try {
      final result = computation(extract);
      return hasNone ? Option.none() : Option.some(result);
    } on _OptionDoNotationException {
      return Option.none();
    } catch (_) {
      return Option.none();
    }
  }
}

/// Internal exception for Option Do notation control flow
class _OptionDoNotationException implements Exception {}

/// Extensions for converting between Result and Option
extension ResultOptionConversion<T> on Result<T> {
  /// Convert Result to Option (loses error information)
  Option<T> toOption() => switch (this) {
    Success(data: final data) => Option.some(data),
    PartialSuccess(data: final data, message: final _) => Option.some(data),
    Failure() => Option.none(),
  };
}

extension OptionResultConversion<T> on Option<T> {
  /// Convert Option to Result with custom error
  Result<T> toResultWith(BaseError error) => switch (this) {
    Some(value: final value) => Result.success(value),
    None() => Result.failure(error),
  };
}
