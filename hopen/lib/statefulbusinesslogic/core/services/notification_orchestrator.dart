import '../models/notification_model.dart';
import '../notification/notification_factory.dart';
import '../../../repositories/notification/notification_repository.dart';
import '../services/logging_service.dart';

/// Central orchestrator for all notification logic
/// Coordinates notification storage and processing following clean architecture
class NotificationOrchestrator {
  final NotificationRepository _notificationRepository;

  NotificationOrchestrator({
    required NotificationRepository notificationRepository,
  }) : _notificationRepository = notificationRepository;

  /// Process incoming notification data and route appropriately
  Future<void> processNotification(Map<String, dynamic> data) async {
    final type = data['type'] as String?;
    if (type == null) {
      LoggingService.warning('NotificationOrchestrator: Received notification without type: $data');
      return;
    }

    LoggingService.info('NotificationOrchestrator: Processing notification type: $type');

    try {
    // Create notification using factory
    final notification = _createNotificationFromData(type, data);
      if (notification == null) {
        LoggingService.warning('NotificationOrchestrator: Could not create notification for type: $type');
        return;
      }

    // Store notification
    await _notificationRepository.saveNotification(notification);

      LoggingService.info('NotificationOrchestrator: Notification processed and stored successfully');
    } catch (e, stackTrace) {
      LoggingService.error('NotificationOrchestrator: Error processing notification: $e', stackTrace: stackTrace);
    }
  }

  /// Create notification from incoming data using factory
  Notification? _createNotificationFromData(String type, Map<String, dynamic> data) {
    try {
    switch (type) {
      case 'contact_request_received':
        return NotificationFactory.createContactRequestReceived(
            fromUserId: (data['fromUserId'] ?? data['requester_id'] ?? '').toString(),
            fromUserName: (data['fromUserName'] ?? data['requester_name'] ?? '').toString(),
        );
      
      case 'contact_request_accepted':
        return NotificationFactory.createContactRequestAccepted(
          acceptedByUserId: (data['acceptedByUserId'] ?? '').toString(),
          acceptedByUserName: (data['acceptedByUserName'] ?? '').toString(),
        );
      
      case 'contact_request_declined':
        return NotificationFactory.createContactRequestDeclined(
          declinedByUserId: (data['declinedByUserId'] ?? '').toString(),
          declinedByUserName: (data['declinedByUserName'] ?? '').toString(),
        );
      
      case 'bubble_invitation_received':
        return NotificationFactory.createBubbleInvitationReceived(
          fromUserId: (data['fromUserId'] ?? '').toString(),
          fromUserName: (data['fromUserName'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );
      
      case 'bubble_join_request_received':
        return NotificationFactory.createBubbleJoinRequestReceived(
          requestingUserId: (data['requestingUserId'] ?? '').toString(),
          requestingUserName: (data['requestingUserName'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );
      
      case 'bubble_join_request_accepted':
        return NotificationFactory.createBubbleJoinRequestAccepted(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
          acceptedUserId: (data['acceptedUserId'] ?? '').toString(),
        );
      
        case 'bubble_join_request_declined':
          return NotificationFactory.createBubbleJoinRequestDeclined(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
            declinedUserId: (data['declinedUserId'] ?? '').toString(),
        );
      
        case 'bubble_invitation_accepted':
          return NotificationFactory.createBubbleInvitationAccepted(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
            acceptedUserId: (data['acceptedUserId'] ?? '').toString(),
        );
      
        case 'bubble_invitation_declined':
          return NotificationFactory.createBubbleInvitationDeclined(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
            declinedUserId: (data['declinedUserId'] ?? '').toString(),
        );
      
        case 'bubble_expiry_warning':
          return NotificationFactory.createBubbleExpiryWarning(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
            daysLeft: (data['daysLeft'] ?? 0) as int,
        );
      
        case 'bubble_expired':
          return NotificationFactory.createBubbleExpired(
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );
      
      case 'bubble_audio_call_incoming':
        return NotificationFactory.createBubbleAudioCallIncoming(
          callerUserId: (data['callerUserId'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? '').toString(),
            callId: (data['callId'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );
      
      case 'bubble_video_call_incoming':
        return NotificationFactory.createBubbleVideoCallIncoming(
          callerUserId: (data['callerUserId'] ?? '').toString(),
          callerUserName: (data['callerUserName'] ?? '').toString(),
            callId: (data['callId'] ?? '').toString(),
          bubbleId: (data['bubbleId'] ?? '').toString(),
          bubbleName: (data['bubbleName'] ?? '').toString(),
        );
      
      case 'friend_audio_call_incoming':
        return NotificationFactory.createFriendAudioCallIncoming(
          friendUserId: (data['friendUserId'] ?? '').toString(),
          friendUserName: (data['friendUserName'] ?? '').toString(),
          callId: (data['callId'] ?? '').toString(),
        );
      
      case 'friend_video_call_incoming':
        return NotificationFactory.createFriendVideoCallIncoming(
          friendUserId: (data['friendUserId'] ?? '').toString(),
          friendUserName: (data['friendUserName'] ?? '').toString(),
          callId: (data['callId'] ?? '').toString(),
        );
      
      // Add more cases as needed
      default:
          LoggingService.warning('NotificationOrchestrator: Unknown notification type: $type');
          return null;
      }
    } catch (e) {
      LoggingService.error('NotificationOrchestrator: Error creating notification for type $type: $e');
        return null;
    }
  }

  /// Get all notifications for user
  Future<List<Notification>> getNotifications() async {
    return await _notificationRepository.getNotifications();
  }

  /// Get notifications by category
  Future<List<Notification>> getNotificationsByCategory(NotificationCategory category) async {
    return await _notificationRepository.getNotificationsByCategory(category);
  }

  /// Get notifications by type
  Future<List<Notification>> getNotificationsByType(String type) async {
    return await _notificationRepository.getNotificationsByType(type);
  }

  /// Get unread notifications count
  Future<int> getUnreadCount() async {
    return await _notificationRepository.getUnreadCount();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    await _notificationRepository.markAsRead(notificationId);
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await _notificationRepository.markAllAsRead();
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    await _notificationRepository.deleteNotification(notificationId);
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    await _notificationRepository.clearAll();
  }

  /// Get notifications stream for real-time updates
  Stream<List<Notification>> notificationsStream() {
    return _notificationRepository.notificationsStream();
  }

  /// Get unread count stream for real-time updates
  Stream<int> unreadCountStream() {
    return _notificationRepository.unreadCountStream();
  }
} 