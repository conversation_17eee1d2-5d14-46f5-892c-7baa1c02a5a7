/// Utility service for profile picture related operations
/// This service contains only presentation-layer utilities and does not handle storage
class ProfilePictureService {
  /// Supported image formats for profile picture upload
  static const List<String> supportedFormats = ['jpg', 'jpeg', 'png', 'webp', 'bmp'];

  /// Check if a file format is supported for profile picture upload
  static bool isSupportedFormat(String? filePath) {
    if (filePath == null || filePath.isEmpty) return false;

    final extension = filePath.split('.').last.toLowerCase();
    return supportedFormats.contains(extension);
  }

  /// Get user-friendly format list for error messages
  static String getSupportedFormatsString() {
    return supportedFormats.map((f) => f.toUpperCase()).join(', ');
  }
  /// Generate initials from first and last name
  static String generateInitials(String? firstName, String? lastName) {
    final first = firstName?.trim() ?? '';
    final last = lastName?.trim() ?? '';

    if (first.isEmpty && last.isEmpty) {
      return 'U'; // Default for User
    }

    final firstInitial = first.isNotEmpty ? first[0].toUpperCase() : '';
    final lastInitial = last.isNotEmpty ? last[0].toUpperCase() : '';

    if (firstInitial.isNotEmpty && lastInitial.isNotEmpty) {
      return '$firstInitial$lastInitial';
    } else if (firstInitial.isNotEmpty) {
      return firstInitial;
    } else {
      return lastInitial;
    }
  }

  /// Get a consistent color for avatar based on initials
  static int getAvatarColorForInitials(String initials) {
    if (initials.isEmpty) {
      return 0xFF6B7280; // Default gray
    }

    // Create a simple hash from the initials
    var hash = 0;
    for (var i = 0; i < initials.length; i++) {
      hash = initials.codeUnitAt(i) + ((hash << 5) - hash);
    }

    // Define a set of pleasant colors for avatars
    final colors = [
      0xFF3B82F6, // Blue
      0xFF10B981, // Emerald
      0xFFF59E0B, // Amber
      0xFFEF4444, // Red
      0xFF8B5CF6, // Violet
      0xFFEC4899, // Pink
      0xFF06B6D4, // Cyan
      0xFF84CC16, // Lime
      0xFFF97316, // Orange
      0xFF6366F1, // Indigo
    ];

    // Use hash to select a color
    final colorIndex = hash.abs() % colors.length;
    return colors[colorIndex];
  }
}
