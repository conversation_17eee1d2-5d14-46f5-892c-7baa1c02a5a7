import 'dart:async';
import '../../statefulbusinesslogic/core/models/notification_model.dart';

/// Repository interface for notification storage and retrieval
abstract class NotificationRepository {
  /// Save a notification
  Future<void> saveNotification(Notification notification);
  
  /// Get all notifications for the current user
  Future<List<Notification>> getNotifications();
  
  /// Get unread notifications count
  Future<int> getUnreadCount();
  
  /// Mark a notification as read
  Future<void> markAsRead(String notificationId);
  
  /// Mark all notifications as read
  Future<void> markAllAsRead();
  
  /// Delete a notification
  Future<void> deleteNotification(String notificationId);
  
  /// Clear all notifications
  Future<void> clearAll();
  
  /// Get notifications by category
  Future<List<Notification>> getNotificationsByCategory(NotificationCategory category);
  
  /// Get notifications by type
  Future<List<Notification>> getNotificationsByType(String type);
  
  /// Get notifications stream for real-time updates
  Stream<List<Notification>> notificationsStream();
  
  /// Get unread count stream for real-time updates
  Stream<int> unreadCountStream();
  
  /// Mark notification as unread
  Future<void> markAsUnread(String notificationId);
  
  /// Delete all notifications
  Future<void> deleteAllNotifications();
}
