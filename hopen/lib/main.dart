import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import 'config/app_config.dart'; // Add AppConfig import
import 'di/injection_container_refactored.dart' as di;
import 'provider/services/auth/ory_auth_service.dart';
import 'provider/services/call/platform_call_handler.dart';

import 'provider/services/notification_service_fcm.dart';
import 'provider/services/real_time_service_manager.dart';
import 'provider/services/performance/startup_performance_service.dart';
import 'provider/services/app_context_manager.dart';
import 'provider/theme/theme_provider.dart';
import 'statefulbusinesslogic/bloc/active_bubble/active_bubble_bloc.dart';
import 'statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import 'statefulbusinesslogic/bloc/auth/auth_event.dart';

import 'statefulbusinesslogic/bloc/bubble/bubble_bloc.dart';
import 'statefulbusinesslogic/bloc/bubble_history/bubble_history_bloc.dart';
import 'statefulbusinesslogic/bloc/bubble_invite_request/bubble_invite_request_bloc.dart';
import 'statefulbusinesslogic/bloc/bubble_join_request/bubble_join_request_bloc.dart';
import 'statefulbusinesslogic/bloc/call/call_bloc.dart';
import 'statefulbusinesslogic/bloc/chat/chat_bloc.dart';
import 'statefulbusinesslogic/bloc/contacts/contacts_bloc.dart';
import 'statefulbusinesslogic/bloc/friend_selection/friend_selection_bloc.dart';
import 'statefulbusinesslogic/bloc/friends/friends_bloc.dart';
import 'statefulbusinesslogic/bloc/notification/notification_bloc.dart';
import 'statefulbusinesslogic/bloc/signup/signup_bloc.dart';
import 'statefulbusinesslogic/bloc/theme/theme_bloc.dart';
import 'statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import 'statefulbusinesslogic/core/services/logging_service.dart';
import 'statefulbusinesslogic/bloc/bubble/bubble_event.dart';
import 'statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import 'statefulbusinesslogic/bloc/user_profile/user_profile_bloc.dart';
import 'presentation/widgets/connectivity_toast_listener.dart';
import 'presentation/widgets/bubble_expiration_alert_dialog.dart';
import 'statefulbusinesslogic/bloc/notification/notification_state.dart';
import 'statefulbusinesslogic/bloc/bubble/bubble_state.dart';

// Global navigator key - THIS MUST BE USED BY YOUR GOROUTER INSTANCE
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Global error display key for catching unhandled errors
final GlobalKey<ScaffoldMessengerState> rootScaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

// Assume 'router' is your GoRouter instance, configured with navigatorKey
// This might be initialized in a separate file and imported, or here directly.
// For this example, we will assume 'router' is passed to MyApp after being configured.

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Force portrait orientation globally by default.
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  // Print configuration for debugging
  AppConfig.printConfig();

  // Set up global error handler before any async operations
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    LoggingService.error(
      'Flutter error: ${details.exception}',
      error: details.exception,
      stackTrace: details.stack,
    );

    // Show a visible error indicator on the app after the current frame
    SchedulerBinding.instance.addPostFrameCallback((_) {
      try {
        if (rootScaffoldMessengerKey.currentState?.mounted == true) {
          rootScaffoldMessengerKey.currentState?.showSnackBar(
            SnackBar(
              content: Text('Error: ${details.exception}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 10),
            ),
          );
        }
              } catch (e) {
        LoggingService.warning('Failed to show error snackbar: $e');
      }
    });
  };

  // ✅ ENABLED: Ory Stack authentication initialization
  try {
    await OryAuthService().initialize();
    LoggingService.success('Ory Stack authentication initialized successfully');
    LoggingService.info('   Kratos URL: ${AppConfig.oryKratosPublicUrl}');
    LoggingService.info('   Hydra URL: ${AppConfig.oryHydraPublicUrl}');
  } catch (e, s) {
    LoggingService.failure('FAILED to initialize Ory Stack', error: e, stackTrace: s);
    // Continue without Ory Stack if it fails
  }

  try {
    // Initialize Firebase with platform-specific options
    if (kIsWeb) {
      // Web configuration - using Firebase project ID from AppConfig
      await Firebase.initializeApp(
        options: const FirebaseOptions(
          apiKey: 'AIzaSyBqJVJKQQQQQQQQQQQQQQQQQQQQQQQQQQQ', // Replace with your actual web API key
          authDomain: '${AppConfig.firebaseProjectId}.firebaseapp.com',
          projectId: AppConfig.firebaseProjectId,
          storageBucket: '${AppConfig.firebaseProjectId}.appspot.com',
          messagingSenderId: '257996495540', // Your messaging sender ID
          appId: '1:257996495540:web:your-web-app-id', // Replace with your actual web app ID
        ),
      );
    } else {
      // Mobile platforms - first check if configuration exists
      try {
        await Firebase.initializeApp();
        LoggingService.success('Firebase initialized successfully from default config');
      } catch (configError) {
        LoggingService.warning(
          'Failed to initialize Firebase from default config',
          error: configError,
        );
        LoggingService.info(
          'Skipping Firebase initialization - app will continue without Firebase features',
          tag: 'Firebase',
        );
      }
    }
  } catch (e, s) {
    LoggingService.failure('FAILED to initialize Firebase', error: e, stackTrace: s);
    LoggingService.info('App will continue without Firebase features');
  }

  try {
    // Initialize dependency injection (performance monitoring starts here)
    await di.init();
    LoggingService.success('Dependency injection initialized successfully');
    
    // Profile startup phases
    final performanceService = di.sl.get<StartupPerformanceService>();
    performanceService.completeOperation('dependency_injection');

    // Wait for all dependencies to be ready
    await di.sl.allReady();
    LoggingService.success('All dependencies are ready');

  } catch (e, s) {
    LoggingService.failure('FAILED to initialize dependency injection', error: e, stackTrace: s);
    rethrow; // Rethrow to prevent app from starting with incomplete initialization
  }

  // IncomingCallListener functionality is now integrated into RealTimeNotificationService
  if (kDebugMode) {
    LoggingService.success('Call notifications are handled by RealTimeNotificationService');
  }

  // Initialize platform call handler for native incoming calls
  if (!kIsWeb) {
    try {
      LoggingService.info('Initializing PlatformCallHandler for native call handling...');
      final platformCallHandler = PlatformCallHandler();
      await platformCallHandler.initialize();
      // Register in DI for potential future access
      di.sl.registerSingleton<PlatformCallHandler>(platformCallHandler);
      LoggingService.success('PlatformCallHandler initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.failure(
        'FAILED TO INITIALIZE PlatformCallHandler in main()',
        error: e,
        stackTrace: stackTrace,
        tag: 'PlatformCalls',
      );
      LoggingService.warning('Background call handling will not work properly');
    }
  }

  // Initialize notification service (ensure it handles non-web gracefully if needed)
  if (!kIsWeb) {
    try {
      LoggingService.info('Attempting to initialize NotificationService...');
      // Only initialize notifications for mobile (or add web support)
      await NotificationServiceFCM().initialize();
      LoggingService.success('NotificationService initialized successfully (non-web)');
    } catch (e, stackTrace) {
      LoggingService.failure(
        'FAILED TO INITIALIZE NotificationService in main()',
        error: e,
        stackTrace: stackTrace,
        tag: 'Notifications',
      );
    }
  }

  // Initialize unified real-time service manager
  try {
    LoggingService.info('Initializing Unified RealTimeServiceManager...');
    final realTimeServiceManager = di.sl<RealTimeServiceManager>();
    await realTimeServiceManager.initialize();
    LoggingService.success('Unified RealTimeServiceManager initialized successfully');
  } catch (e, stackTrace) {
    LoggingService.failure(
      'FAILED TO INITIALIZE Unified RealTimeServiceManager in main()',
      error: e,
      stackTrace: stackTrace,
      tag: 'RealTimeServices',
    );
    LoggingService.warning('Real-time notifications will not work properly');
  }

  // Contact request notifications are now handled by the unified RealTimeNotificationService
  // which is initialized automatically when users log in

  // Configure system UI for edge-to-edge display
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  // Enable edge-to-edge display (extend UI under system navigation bars)
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  // Mark startup as complete before launching the app
  try {
    final performanceService = di.sl<StartupPerformanceService>();
    performanceService.markStartupComplete();
  } catch (e) {
    LoggingService.warning('Failed to mark startup complete', error: e);
  }

  // final router = AppRouter(navigatorKey: navigatorKey).router; // If AppRouter structure is used
  try {
    final router = di.sl<GoRouter>(); // Assuming router is in DI and configured with the key
    print('Router fetched successfully from DI');
    runApp(MyApp(router: router));
  } catch (e, s) {
    print('FAILED to get router from DI: $e');
    print(s);

    // Run a simplified error app instead
    runApp(ErrorApp(error: e.toString(), stackTrace: s.toString()));
  }
}

// Simple error display app as a fallback when the main app fails to initialize
class ErrorApp extends StatelessWidget {

  const ErrorApp({required this.error, required this.stackTrace, super.key});
  final String error;
  final String stackTrace;

  @override
  Widget build(BuildContext context) => MaterialApp(
      title: 'Hopen Error',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        scaffoldBackgroundColor: const Color(0xFF0A2955),
      ),
      home: Scaffold(
        appBar: AppBar(title: const Text('Hopen Initialization Error')),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'The app encountered an error during initialization:',
                  style: TextStyle(fontSize: 18, color: Colors.white),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    error,
                    style: const TextStyle(fontSize: 16, color: Colors.white),
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'Stack Trace:',
                  style: TextStyle(fontSize: 16, color: Colors.white70),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: SingleChildScrollView(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        stackTrace,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
}
class MyApp extends StatelessWidget {

  const MyApp({required this.router, super.key});
  final GoRouter router;

  @override
  Widget build(BuildContext context) {
    // The BLoC provider for authentication will wrap the entire application
    // to make authentication state globally accessible.
    return MultiProvider(
      providers: [
        BlocProvider<AuthBloc>(
          create: (_) => di.sl<AuthBloc>()..add(const CheckAuthStatusEvent()),
        ),
        BlocProvider<SignUpBloc>(create: (_) => di.sl<SignUpBloc>()),
        BlocProvider<NotificationBloc>(create: (_) => di.sl<NotificationBloc>()),
        BlocProvider<BubbleHistoryBloc>(
            create: (_) => di.sl<BubbleHistoryBloc>(),),
        BlocProvider<ContactsBloc>(
            create: (_) => di.sl<ContactsBloc>(),),
        BlocProvider<BubbleBloc>(
          create: (_) => di.sl<BubbleBloc>()..add(const LoadBubble()),
        ),
        BlocProvider<FriendsBloc>(create: (_) => di.sl<FriendsBloc>()),
        BlocProvider<UnifiedProfileBloc>(
          create: (_) => di.sl<UnifiedProfileBloc>(),
        ),
        BlocProvider<UserProfileBloc>(
          create: (_) => di.sl<UserProfileBloc>(),
        ),
        BlocProvider<CallBloc>(create: (_) => di.sl<CallBloc>()),
        BlocProvider<ActiveBubbleBloc>(
            create: (_) => di.sl<ActiveBubbleBloc>(),),
        BlocProvider<ChatBloc>(create: (_) => di.sl<ChatBloc>()),
        BlocProvider<FriendSelectionBloc>(
            create: (_) => di.sl<FriendSelectionBloc>(),),
        BlocProvider<BubbleJoinRequestBloc>(
            create: (_) => di.sl<BubbleJoinRequestBloc>(),),
        BlocProvider<BubbleInviteRequestBloc>(
            create: (_) => di.sl<BubbleInviteRequestBloc>(),),
        BlocProvider<ThemeBloc>(create: (_) => di.sl<ThemeBloc>()),
        // Note: BubbleProposeRequestBloc, BubbleCountdownBloc, and VoiceRecordingBloc
        // are not registered in DI container yet
        ChangeNotifierProvider<NavBarVisibilityNotifier>(
          create: (_) => NavBarVisibilityNotifier(),
        ),
        ChangeNotifierProvider<ThemeProvider>(
          create: (_) => di.sl<ThemeProvider>(),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) => BlocListener<NotificationBloc, NotificationState>(
          listener: (context, state) {
            if (state is BubbleExpiryNotificationReceived) {
              // Get the current bubble from BubbleBloc to show the warning dialog
              final bubbleState = context.read<BubbleBloc>().state;
              if (bubbleState is BubbleLoaded) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  BubbleExpirationWarningDialog.show(
                    context,
                    bubble: bubbleState.bubble,
                    daysRemaining: state.daysLeft,
                  );
                });
              }
            }
          },
          child: MaterialApp.router(
            scaffoldMessengerKey: rootScaffoldMessengerKey,
            title: 'Hopen',
            theme: themeProvider.getTheme(context),
            routerConfig: router,
            debugShowCheckedModeBanner: false,
            builder: (context, child) => AppContextProvider(
              child: ConnectivityToastListener(
                child: child ?? const SizedBox.shrink(),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Remove the default MyHomePage and related code below this line



