import 'package:flutter/material.dart';

/// A widget that displays a badge indicating that a contact request has been sent to a user.
///
/// This badge appears in contact lists when the current user has sent a contact request
/// to another user and it's still pending. It uses the same shape and styling patterns
/// as other badges in the app for visual consistency.
class ContactRequestSentBadge extends StatelessWidget {
  /// Creates a ContactRequestSentBadge.
  ///
  /// [fontSize] allows customizing the text size. If null, a responsive size will be calculated.
  /// [horizontalPadding] allows customizing the horizontal padding. If null, a responsive padding will be calculated.
  /// [verticalPadding] allows customizing the vertical padding. If null, a responsive padding will be calculated.
  const ContactRequestSentBadge({
    super.key,
    this.fontSize,
    this.horizontalPadding,
    this.verticalPadding,
  });

  final double? fontSize;
  final double? horizontalPadding;
  final double? verticalPadding;

  @override
  Widget build(BuildContext context) {
    // Calculate responsive sizes if not explicitly provided
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Match the sizing from contacts page filter chips and other badges
    final actualFontSize = fontSize ?? (screenHeight * 0.010).clamp(7.0, 9.0);
    final actualVerticalPadding =
        verticalPadding ?? (screenHeight * 0.004).clamp(3.0, 4.0);
    final actualHorizontalPadding =
        horizontalPadding ?? (screenWidth * 0.015).clamp(6.0, 8.0);

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: actualHorizontalPadding,
        vertical: actualVerticalPadding * 1.5, // Match Contact badge padding
      ),
      decoration: ShapeDecoration(
        color: Colors.orange.withValues(alpha: 0.8), // Orange color to indicate pending status
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(5), // Match bubble status badge shape
        ),
        shadows: [
          BoxShadow(
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0.5,
            color: Colors.orange.withValues(alpha: 0.3),
          ),
        ],
      ),
      child: Text(
        'contact request sent',
        style: TextStyle(
          fontSize: actualFontSize,
          color: Colors.white,
          height: 1,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
