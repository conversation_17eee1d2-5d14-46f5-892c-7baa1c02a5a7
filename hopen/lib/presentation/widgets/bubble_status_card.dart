import 'dart:async';
import 'dart:math';
import 'dart:ui';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../statefulbusinesslogic/bloc/bubble/bubble_bloc.dart';
import '../../statefulbusinesslogic/bloc/bubble/bubble_event.dart';
import '../../statefulbusinesslogic/bloc/bubble/bubble_state.dart';
import '../../statefulbusinesslogic/bloc/bubble_countdown/bubble_countdown_bloc.dart';
import '../../statefulbusinesslogic/bloc/bubble_countdown/bubble_countdown_event.dart';
import '../../statefulbusinesslogic/bloc/bubble_countdown/bubble_countdown_state.dart';
import '../../statefulbusinesslogic/core/models/bubble_member.dart';
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../pages/chat/bubble_chat_page.dart' as chat;
import '../router/app_router.dart';
import 'bubble_call_dialog.dart';
import 'bubble_countdown_dialog.dart';
import 'gradient_background.dart';
import 'hopen_button_demo_dialog.dart';
import '../utils/nav_bar_visibility_notifier.dart';

/// A responsive card widget displaying the status and details of the user's current bubble.
///
/// Includes the bubble name (editable via a tap action, styled with an animated gradient),
/// start/end dates, duration, countdown, and a radial progress indicator indicating the bubble's advancement.
/// The card's height and internal elements scale based on [baseTileHeight] for responsiveness.
/// The bubble name is visually limited to 30 characters; longer names are truncated with ellipsis.
/// All text elements dynamically scale based on the provided [baseTileHeight] for
/// optimal layout on various screen sizes.
class BubbleStatusCard extends StatefulWidget {
  final BubbleEntity bubble;
  final VoidCallback? onTap;

  const BubbleStatusCard({
    Key? key,
    required this.bubble,
    this.onTap,
  }) : super(key: key);

  @override
  State<BubbleStatusCard> createState() => _BubbleStatusCardState();
}

class _BubbleStatusCardState extends State<BubbleStatusCard> {
  late BubbleCountdownBloc _countdownBloc;

  @override
  void initState() {
    super.initState();
    _countdownBloc = context.read<BubbleCountdownBloc>();
    _startCountdown();
  }

  void _startCountdown() {
    if (widget.bubble.endDate != null) {
      final duration = widget.bubble.endDate!.difference(DateTime.now());
      if (duration.isNegative) return;
      _countdownBloc.add(StartCountdown(duration));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BubbleCountdownBloc, BubbleCountdownState>(
      builder: (context, state) {
        return _buildCard(state);
      },
    );
  }

  Widget _buildCard(BubbleCountdownState state) {
    return Card(
      elevation: 4,
      shape: RoundedSuperellipseBorder(
        borderRadius: BorderRadius.circular(36),
      ),
      child: InkWell(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Bubble Status',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  IconButton(
                    icon: const Icon(Icons.info_outline),
                    onPressed: () => _showBubbleInfo(context),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              if (state.isRunning)
                Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildTimeUnit('Days', state.remainingTime.inDays),
                        _buildTimeUnit('Hours', state.remainingTime.inHours.remainder(24)),
                        _buildTimeUnit('Minutes', state.remainingTime.inMinutes.remainder(60)),
                        _buildTimeUnit('Seconds', state.remainingTime.inSeconds.remainder(60)),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: state.progress,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeUnit(String label, int value) {
    return Column(
      children: [
        Text(
          value.toString().padLeft(2, '0'),
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  void _showBubbleInfo(BuildContext context) {
    final navNotifier = Provider.of<NavBarVisibilityNotifier>(context, listen: false);
    navNotifier.hide();
    HopenButtonDemoDialog.show(context);
  }

  void _onDismissed() {
    final navNotifier = Provider.of<NavBarVisibilityNotifier>(context, listen: false);
    navNotifier.show();
  }
}

// --- Radial Painter ---
class _RadialPainter extends CustomPainter {
  // Progress from 0.0 to 1.0

  _RadialPainter({required this.progressPercentage});
  final double progressPercentage;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    const strokeWidth = 8.0;

    // Background circle paint
    final backgroundPaint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.3) // Consistent background
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth;

    // Progress arc paint
    final progressPaint =
        Paint()
  // // Use a simple white for progress arc for clarity against gradient
          ..color = Colors.white
          ..strokeCap = StrokeCap.round
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth;

    // Draw background circle
    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw progress arc
    final progressRadians = _degreesToRadians(progressPercentage * 360);
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      _degreesToRadians(-90), // Start at 12 o'clock
      progressRadians,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant _RadialPainter oldDelegate) {
    // Repaint only if progress changes
    return oldDelegate.progressPercentage != progressPercentage;
  }
}

double _degreesToRadians(double degrees) {
  return degrees * (math.pi / 180.0);
}

// --- Custom Gradient Transform for Sweep Effect ---
class _SweepGradientTransform extends GradientTransform {
  // Width of the bounds being painted onto

  const _SweepGradientTransform(this.percent, this.width);
  final double percent; // Animation value 0.0 to 1.0
  final double width;

  @override
  Matrix4? transform(Rect bounds, {TextDirection? textDirection}) {
    // Calculate translation to move gradient across the bounds.
    // We move it from -width to +width to ensure the gradient sweeps fully.
    // final double dx = lerpDouble(-width, width, percent)!; // No longer needed
    // return Matrix4.identity()..translate(dx); // No longer needed
    return Matrix4.identity(); // Return identity since gradient is removed
  }
}
