import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../utils/color_transition_controller.dart';
import '../../../statefulbusinesslogic/bloc/contact_request/contact_request_bloc.dart';
import '../../../statefulbusinesslogic/bloc/contact_request/contact_request_event.dart' as contact_request;
import '../../../statefulbusinesslogic/bloc/contact_request/contact_request_state.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_event.dart';

/// A dialog that appears when another user sends a request to become a contact with the current user.
///
/// Features:
/// * Displays the requester's profile picture and name
/// * Shows the timestamp of when the request was made
/// * Provides accept/decline buttons with appropriate confirmation messages
/// * Uses the same design pattern and UI style as the existing dialogs
/// * Includes animations and transitions consistent with other dialogs
class ContactRequestDialog extends StatefulWidget {
  const ContactRequestDialog({
    required this.requestId,
    required this.requesterId,
    required this.requesterName,
    required this.requestTimestamp,
    super.key,
    this.requesterUsername,
    this.requesterProfilePicUrl,
    this.message,
  });
  final String requestId;
  final String requesterId;
  final String requesterName;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final DateTime requestTimestamp;
  final String? message;

  /// Shows the contact request dialog.
  static Future<bool?> show(
    BuildContext context, {
    required String requestId,
    required String requesterId,
    required String requesterName,
    required DateTime requestTimestamp,
    String? requesterUsername,
    String? requesterProfilePicUrl,
    String? message,
  }) => showDialog<bool>(
    context: context,
    barrierDismissible: false, // Make the dialog non-dismissible
    barrierColor: Colors.black.withValues(alpha: 0.9),
    builder:
        (context) => WillPopScope(
          // Prevent back button from dismissing the dialog
          onWillPop: () async => false,
          child: ContactRequestDialog(
            requestId: requestId,
            requesterId: requesterId,
            requesterName: requesterName,
            requesterUsername: requesterUsername,
            requesterProfilePicUrl: requesterProfilePicUrl,
            requestTimestamp: requestTimestamp,
            message: message,
          ),
        ),
  );

  @override
  State<ContactRequestDialog> createState() => _ContactRequestDialogState();
}

class _ContactRequestDialogState extends State<ContactRequestDialog> {
  String? _errorMessage;

  /// Controller for managing color transitions
  late ColorTransitionController _colorController;

  @override
  void initState() {
    super.initState();
    _colorController = ColorTransitionController();
    _colorController.startColorLoop();

    // Listen to color changes and update the UI
    _colorController.addListener(() {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _colorController.dispose();
    super.dispose();
  }

  double _getImageSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 370) {
      return 120;
    } else if (width < 600) {
      return 140;
    } else {
      return 150;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d').format(timestamp);
    }
  }

  @override
  Widget build(BuildContext context) => _buildDialogContent(context);

  Widget _buildDialogContent(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    // Get current color and glow effect from the controller
    final currentActiveColor = _colorController.currentColor;
    final titleGlowEffect = _colorController.getGlowEffect();

    final imageSize = _getImageSize(context);
    final verticalSpacing = ColorTransitionController.getDialogVerticalSpacing(
      context,
    );

    return BlocListener<ContactRequestBloc, ContactRequestState>(
      listener: (context, state) {
        if (state.status == ContactRequestStatus.accepted) {
          _showSuccessMessage(context);
        } else if (state.status == ContactRequestStatus.declined) {
          Navigator.of(context).pop(false);
        } else if (state.status == ContactRequestStatus.error) {
          setState(() {
            _errorMessage = state.errorMessage ?? 'An error occurred';
          });
        }
      },
      child: AlertDialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
        insetPadding: EdgeInsets.zero,
        titlePadding: const EdgeInsets.only(top: 16),
        contentPadding: const EdgeInsets.symmetric(),
      title: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedDefaultTextStyle(
              duration: _colorController.transitionDuration,
              style: TextStyle(
                fontFamily: 'Omnes',
                fontSize: ColorTransitionController.getTitleSize(context),
                fontWeight: FontWeight.bold,
                color: currentActiveColor,
                shadows: titleGlowEffect,
              ),
              child: const Text('Contact request'),
            ),
            SizedBox(height: verticalSpacing * 8),
            _buildRequesterProfile(context, imageSize * 1.4),
            SizedBox(height: verticalSpacing * 4),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                '${widget.requesterName} wants to connect with you',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: ColorTransitionController.getSubtitleSize(context),
                  height: 1.4, // Added line height for readability
                ),
              ),
            ),
            SizedBox(height: verticalSpacing * 0.8),
            Text(
              'Request sent ${_formatTimestamp(widget.requestTimestamp)}',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white54,
                fontSize:
                    ColorTransitionController.getSubtitleSize(context) * 0.8,
              ),
            ),
          ],
        ),
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: screenHeight * 0.1,
      ),
      actionsPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.red, fontSize: 14),
                ),
              ),
            SizedBox(
              width: double.infinity,
              height: 50,
              child: Row(
                children: [
                  Expanded(child: _buildDeclineButton(context)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildAcceptButton(context)),
                ],
              ),
            ),
          ],
        ),
      ],
    ),
    );
  }

  Widget _buildRequesterProfile(BuildContext context, double imageSize) =>
      Container(
        width: imageSize,
        height: imageSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white.withValues(alpha: 0.1),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 2,
          ),
        ),
        child:
            widget.requesterProfilePicUrl != null &&
                    widget.requesterProfilePicUrl!.isNotEmpty
                ? ClipOval(
                  child: Image.network(
                    widget.requesterProfilePicUrl!,
                    fit: BoxFit.cover,
                    errorBuilder:
                        (context, error, stackTrace) =>
                            _buildFallbackAvatar(imageSize),
                  ),
                )
                : _buildFallbackAvatar(imageSize),
      );

  Widget _buildFallbackAvatar(double imageSize) => Center(
    child: Text(
      widget.requesterName.isNotEmpty
          ? widget.requesterName[0].toUpperCase()
          : '?',
      style: TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: imageSize * 0.4,
      ),
    ),
  );

  Widget _buildDeclineButton(BuildContext context) => ElevatedButton(
    onPressed: () => _handleDecline(context),
    style: ElevatedButton.styleFrom(
      backgroundColor: const Color(0xFF4A4A4A), // Darker grey for decline
      foregroundColor: Colors.white,
      disabledBackgroundColor: Colors.grey.withValues(alpha: 0.3),
      disabledForegroundColor: Colors.white.withValues(alpha: 0.5),
      shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(18)),
      padding: EdgeInsets.zero,
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    ).copyWith(
      elevation: WidgetStateProperty.all(0), // Explicitly set elevation to 0
    ),
    child: const Center(
      child: Text(
        'Decline',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    ),
  );

  Widget _buildAcceptButton(BuildContext context) => ElevatedButton(
    onPressed: () => _handleAccept(context),
    style: ElevatedButton.styleFrom(
      backgroundColor: Colors.blue, // Original blue color
      foregroundColor: Colors.white,
      disabledBackgroundColor: Colors.grey.withValues(alpha: 0.3),
      disabledForegroundColor: Colors.white.withValues(alpha: 0.5),
      shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(18)),
      padding: EdgeInsets.zero,
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    ).copyWith(
      elevation: WidgetStateProperty.all(0), // Explicitly set elevation to 0
    ),
    child: const Center(
      child: Text(
        'Accept',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    ),
  );

  void _handleAccept(BuildContext context) {
    setState(() {
      _errorMessage = null;
    });

    context.read<ContactRequestBloc>().add(
      contact_request.AcceptContactRequestEvent(requestId: widget.requestId),
    );
    // Notify UnifiedProfileBloc
    context.read<UnifiedProfileBloc>().add(
      ContactRequestAcceptedEvent(requesterId: widget.requesterId),
    );
  }

  void _showSuccessMessage(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.9),
      builder: (dialogContext) {
        // Auto-close after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          if (Navigator.of(dialogContext).canPop()) {
            Navigator.of(dialogContext).pop(); // Close success message dialog
            // Now pop the original ContactRequestDialog
            if (Navigator.of(context).canPop()) {
              Navigator.of(
                context,
              ).pop(true); // Return true to indicate acceptance
            }
          }
        });

        const titleText = 'New contact added!';
        final subtitleText =
            'You and ${widget.requesterName} are now contacts. You can now create bubbles together or invite each other to existing bubbles.';

        return WillPopScope(
          // Prevent back button from dismissing the dialog
          onWillPop: () async => false,
          child: AlertDialog(
            backgroundColor: const Color(0xFF1A2B4D),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
            contentPadding: const EdgeInsets.all(24),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/3d/200px/normal/tick.png',
                  width: 70,
                  height: 70,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    print('Error loading tick.png: $error');
                    // Fallback to the original icon if image fails to load
                    return const Icon(
                      Icons.check_circle,
                      color: Color(0xFF00FFFF),
                      size: 70,
                    );
                  },
                ),
                const SizedBox(height: 24),
                const Text(
                  titleText,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontFamily: 'Omnes',
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  subtitleText,
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleDecline(BuildContext context) {
    // Show a confirmation dialog before declining
    _showDeclineConfirmationDialog(context);
  }

  void _showDeclineConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withValues(alpha: 0.9),
      builder:
          (dialogContext) => AlertDialog(
            backgroundColor: const Color(0xFF1A2B4D),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
            title: const Text(
              'Are you sure?',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            content: Text(
              "You will decline ${widget.requesterName}'s request to connect with you.",
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            actionsAlignment: MainAxisAlignment.spaceEvenly,
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                },
                child: const Text(
                  'Let me rethink',
                  style: TextStyle(color: Colors.white),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  _submitDecline(context);
                },
                child: const Text(
                  'Confirm',
                  style: TextStyle(
                    color: Color(0xFF00FFFF),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  void _submitDecline(BuildContext context) {
    setState(() {
      _errorMessage = null;
    });

    context.read<ContactRequestBloc>().add(
      contact_request.DeclineContactRequestEvent(requestId: widget.requestId),
    );
    // Notify UnifiedProfileBloc
    context.read<UnifiedProfileBloc>().add(
      ContactRequestDeclinedEvent(requesterId: widget.requesterId),
    );
  }
}
