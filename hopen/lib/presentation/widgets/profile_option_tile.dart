import 'package:flutter/material.dart';
import 'profile_icon.dart';

class ProfileOptionTile extends StatelessWidget {
  const ProfileOptionTile({
    required this.title,
    required this.subtitle,
    super.key,
    this.iconPath,
    this.iconWidget,
    this.onTap,
    this.trailing,
  }) : assert(
         iconPath != null || iconWidget != null,
         'Must provide either iconPath or iconWidget',
       );
  final String title;
  final String subtitle;
  final String? iconPath;
  final Widget? iconWidget;
  final VoidCallback? onTap;
  final Widget? trailing;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final tileHeight = screenHeight / 12;
    final iconContainerSize = tileHeight * 0.7; // Match avatar size

    final verticalPadding = screenHeight / 96; // Padding around the tile
    // Font sizes relative to tile height
    final titleFontSize = (tileHeight * 0.25).clamp(16.0, 18.0);
    final subtitleFontSize = (tileHeight * 0.18).clamp(12.0, 14.0);

    // Build icon widget dynamically
    Widget buildIcon() => Container(
      width: iconContainerSize,
      height: iconContainerSize,
      decoration: ShapeDecoration(
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(iconContainerSize * 0.4),
          side: BorderSide(
            color: Colors.white.withValues(alpha: 0.2),
            width: 2,
          ),
        ),
        color: const Color(0x1AFFFFFF), // Standard background
      ),
      child: Center(
        child:
            iconWidget ?? // Prioritize direct widget if provided
            ProfileIcon(svgPath: iconPath, fallbackText: title),
      ),
    );

    return Padding(
      padding: EdgeInsets.symmetric(vertical: verticalPadding),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(25),
        child: Container(
          height: tileHeight, // Set fixed height like FriendsTile
          decoration: ShapeDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            shape: RoundedSuperellipseBorder(
              borderRadius: BorderRadius.circular(26),
              side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
            ),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
          ), // Keep horizontal padding, adjust vertical if needed
          child: Row(
            children: [
              buildIcon(),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment:
                      MainAxisAlignment.center, // Center content vertically
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: titleFontSize, // Use dynamic size
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: subtitleFontSize, // Use dynamic size
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              if (trailing != null) ...[const SizedBox(width: 12), trailing!],
            ],
          ),
        ),
      ),
    );
  }
}
