import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../statefulbusinesslogic/bloc/friend_selection/friend_selection_bloc.dart';
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';
import 'requests/friends_choice_dialog.dart';

/// Dialog shown when a bubble expires to inform users and allow friend selection
class BubbleExpirationAlertDialog extends StatelessWidget {
  const BubbleExpirationAlertDialog({
    required this.expiredBubble,
    required this.formerMembers,
    super.key,
  });

  final BubbleEntity expiredBubble;
  final List<UserModel> formerMembers;

  /// Shows the bubble expiration alert dialog
  static Future<void> show(
    BuildContext context, {
    required BubbleEntity expiredBubble,
    required List<UserModel> formerMembers,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.9),
      builder: (context) => BubbleExpirationAlertDialog(
        expiredBubble: expiredBubble,
        formerMembers: formerMembers,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: const Color(0xFF1A2B4D),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(25),
      ),
      title: Column(
        children: [
          Icon(
            Icons.access_time_filled,
            color: Colors.orange,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'Bubble Expired',
            style: TextStyle(
              color: Colors.orange,
              fontWeight: FontWeight.bold,
              fontSize: 24,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Your bubble "${expiredBubble.name.value}" has reached its 90-day limit and has expired.',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          if (formerMembers.length > 1) ...[
            Text(
              'You can now choose to become friends with your former bubble members!',
              style: const TextStyle(
                color: Color(0xFF00FFFF),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Former Members:',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...formerMembers.take(3).map((member) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Text(
                '• ${member.firstName} ${member.lastName}',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            )),
            if (formerMembers.length > 3)
              Text(
                '• and ${formerMembers.length - 3} more...',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
          ] else ...[
            Text(
              'Since you were the only member, no friend requests will be created.',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
      actions: [
        if (formerMembers.length > 1) ...[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Show friends choice dialog
              FriendsChoiceDialog.show(
                context,
                bubbleId: expiredBubble.id.value,
                bubbleName: expiredBubble.name.value,
                formerMembers: formerMembers,
              );
            },
            child: const Text(
              'Choose Friends',
              style: TextStyle(
                color: Color(0xFF00FFFF),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Maybe Later',
              style: TextStyle(
                color: Colors.white70,
              ),
            ),
          ),
        ] else ...[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'OK',
              style: TextStyle(
                color: Color(0xFF00FFFF),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }
}

/// Dialog shown to warn users when bubble is about to expire
class BubbleExpirationWarningDialog extends StatelessWidget {
  const BubbleExpirationWarningDialog({
    required this.bubble,
    required this.daysRemaining,
    super.key,
  });

  final BubbleEntity bubble;
  final int daysRemaining;

  /// Shows the bubble expiration warning dialog
  static Future<void> show(
    BuildContext context, {
    required BubbleEntity bubble,
    required int daysRemaining,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (context) => BubbleExpirationWarningDialog(
        bubble: bubble,
        daysRemaining: daysRemaining,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isUrgent = daysRemaining <= 3;
    
    return AlertDialog(
      backgroundColor: const Color(0xFF1A2B4D),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(25),
      ),
      title: Column(
        children: [
          Icon(
            isUrgent ? Icons.warning : Icons.schedule,
            color: isUrgent ? Colors.red : Colors.orange,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            isUrgent ? 'Bubble Expiring Soon!' : 'Bubble Expiration Notice',
            style: TextStyle(
              color: isUrgent ? Colors.red : Colors.orange,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Your bubble "${bubble.name.value}" will expire in $daysRemaining ${daysRemaining == 1 ? 'day' : 'days'}.',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'When it expires, you\'ll be able to send friend requests to your bubble members.',
            style: const TextStyle(
              color: Color(0xFF00FFFF),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          if (isUrgent) ...[
            const SizedBox(height: 16),
            Text(
              'Make the most of your remaining time together!',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            'Got it',
            style: TextStyle(
              color: Color(0xFF00FFFF),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
