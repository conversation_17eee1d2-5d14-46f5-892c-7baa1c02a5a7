import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'online_status_indicator.dart';
import 'profile_picture_widget.dart';

/// A flexible card widget used for displaying both friends and bubble members.
/// Renamed from FriendsTileCard
class FriendsTile extends StatelessWidget {
  /// Creates a FriendsTile.
  /// Renamed from FriendsTileCard
  const FriendsTile({
    required this.name,
    required this.subtitle,
    super.key,
    this.time,
    this.avatarUrl,
    this.onTap,
    this.trailing,
    this.usageContext = TileCardContext.friend, // Default to friend context
    this.nameColor, // Add nameColor to constructor
    this.isOnline = false, // Default to offline
  });

  /// The name displayed as the primary text.
  final String name;

  /// The subtitle or description displayed as secondary text.
  final String subtitle;

  /// Optional time information, used primarily for friend contexts.
  final String? time;

  /// URL or path to the avatar image (falls back to initials if null).
  final String? avatarUrl;

  /// Callback when the card is tapped.
  final VoidCallback? onTap;

  /// Optional trailing widget (e.g., notification count for friends).
  final Widget? trailing;

  /// The context in which this card is being used, affects certain visual properties.
  final TileCardContext usageContext;

  /// Optional color for the name text.
  final Color? nameColor;

  /// Indicates whether the user is online.
  final bool isOnline;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final tileHeight = screenHeight / 12;
    final avatarSize = tileHeight * 0.7;

    final verticalPadding = screenHeight / 96;
    // Remove the extra vertical padding for Bubbler context that was causing overflow
  // const contentVerticalPadding = 0.0; // Same for all contexts now

    // Define decoration based on context
    final ShapeDecoration tileDecoration;
    if (usageContext == TileCardContext.Bubbler) {
      tileDecoration = ShapeDecoration(
        color: Colors.white.withValues(
          alpha: 0.1,
        ), // Translucent white background
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(26),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
      );
    } else {
      // Default decoration for friends context
      tileDecoration = ShapeDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF00C4FF), Color(0xFF00A3E0)],
        ),
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(26),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
      );
    }

    // Determine if the avatar is an SVG
    final isSvg = avatarUrl?.toLowerCase().endsWith('.svg') ?? false;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: verticalPadding),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              child: Container(
                height: tileHeight,
                decoration: tileDecoration,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Row(
                  children: [
                    isSvg
                        ? Container(
                            width: avatarSize,
                            height: avatarSize,
                            padding: EdgeInsets.all(avatarSize * 0.15),
                            decoration: ShapeDecoration(
                              shape: RoundedSuperellipseBorder(
                                borderRadius: BorderRadius.circular(avatarSize * 0.4),
                                side: BorderSide(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  width: 2,
                                ),
                              ),
                              color: Colors.white.withValues(alpha: 0.1),
                            ),
                            child: SvgPicture.asset(
                              avatarUrl!,
                              colorFilter: const ColorFilter.mode(
                                Colors.white,
                                BlendMode.srcIn,
                              ),
                              width: avatarSize * 0.7,
                              height: avatarSize * 0.7,
                            ),
                          )
                        : ProfilePictureWidget(
                            imageUrl: avatarUrl,
                            firstName: name.split(' ').first,
                            lastName: name.split(' ').length > 1 ? name.split(' ').last : '',
                            radius: avatarSize / 2,
                            backgroundColor: usageContext == TileCardContext.Bubbler
                                ? Colors.white.withValues(alpha: 0.1)
                                : null,
                          ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        mainAxisAlignment:
                            MainAxisAlignment
                                .center, // Add to ensure vertical centering
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Flexible(
                                child: Text(
                                  name,
                                  style: TextStyle(
                                    fontSize: tileHeight * 0.25,
                                    fontWeight: FontWeight.w500,
                                    color: nameColor ?? Colors.white,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 6),
                                child: OnlineStatusIndicator(
                                  isOnline: isOnline,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            subtitle,
                            style: TextStyle(
                              fontSize: tileHeight * 0.18,
                              color: Colors.white.withValues(alpha: 0.7),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    if (trailing != null) ...[
                      const SizedBox(width: 12),
                      trailing!,
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  ImageProvider _resolveImage(String url) {
    debugPrint('Resolving image URL: $url');
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return NetworkImage(url);
    } else {
      return AssetImage(url);
    }
  }
}

/// Enum defining the different contexts where the TileCard can be used
enum TileCardContext { friend, bubble, Bubbler }
