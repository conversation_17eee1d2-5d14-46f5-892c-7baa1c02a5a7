import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../statefulbusinesslogic/bloc/profile_picture/profile_picture_bloc.dart';
import '../../statefulbusinesslogic/bloc/profile_picture/profile_picture_event.dart';
import '../../statefulbusinesslogic/bloc/profile_picture/profile_picture_state.dart';

/// Enhanced dialog for profile picture upload with progress and error handling
class ProfilePictureUploadDialog extends StatelessWidget {
  const ProfilePictureUploadDialog({
    super.key,
    required this.onSuccess,
    this.onCancel,
  });

  final VoidCallback onSuccess;
  final VoidCallback? onCancel;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProfilePictureBloc, ProfilePictureState>(
      listener: (context, state) {
        if (state is ProfilePictureSuccess) {
          Navigator.of(context).pop();
          onSuccess();
        } else if (state is ProfilePictureCancelled) {
          Navigator.of(context).pop();
          onCancel?.call();
        }
      },
      builder: (context, state) {
        return AlertDialog(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          shape: const RoundedSuperellipseBorder(
            borderRadius: BorderRadius.all(Radius.circular(18)),
          ),
          contentPadding: const EdgeInsets.all(24),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildContent(context, state),
              const SizedBox(height: 24),
              _buildActions(context, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, ProfilePictureState state) {
    if (state is ProfilePictureLoading) {
      return _buildLoadingContent(state);
    } else if (state is ProfilePictureError) {
      return _buildErrorContent(context, state);
    } else {
      return _buildInitialContent(context);
    }
  }

  Widget _buildLoadingContent(ProfilePictureLoading state) {
    return Column(
      children: [
        const CircularProgressIndicator(),
        const SizedBox(height: 16),
        Text(
          state.operation ?? 'Processing...',
          style: const TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        if (state.progress != null) ...[
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: state.progress,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
          const SizedBox(height: 8),
          Text(
            '${(state.progress! * 100).toInt()}%',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ],
    );
  }

  Widget _buildErrorContent(BuildContext context, ProfilePictureError state) {
    IconData errorIcon;
    Color errorColor;
    
    switch (state.errorType) {
      case ProfilePictureErrorType.network:
        errorIcon = Icons.wifi_off;
        errorColor = Colors.orange;
        break;
      case ProfilePictureErrorType.storage:
        errorIcon = Icons.cloud_off;
        errorColor = Colors.red;
        break;
      case ProfilePictureErrorType.fileSize:
        errorIcon = Icons.file_present;
        errorColor = Colors.amber;
        break;
      case ProfilePictureErrorType.fileFormat:
        errorIcon = Icons.image_not_supported;
        errorColor = Colors.amber;
        break;
      default:
        errorIcon = Icons.error;
        errorColor = Colors.red;
    }

    return Column(
      children: [
        Icon(
          errorIcon,
          size: 48,
          color: errorColor,
        ),
        const SizedBox(height: 16),
        Text(
          'Upload Failed',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: errorColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          state.message,
          style: const TextStyle(fontSize: 14),
          textAlign: TextAlign.center,
        ),
        if (state.technicalDetails != null && state.technicalDetails!.isNotEmpty) ...[
          const SizedBox(height: 8),
          ExpansionTile(
            title: const Text(
              'Technical Details',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  state.technicalDetails!,
                  style: const TextStyle(fontSize: 10, color: Colors.grey),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildInitialContent(BuildContext context) {
    return const Column(
      children: [
        Icon(
          Icons.camera_alt,
          size: 48,
          color: Colors.blue,
        ),
        SizedBox(height: 16),
        Text(
          'Update Profile Picture',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Choose how you want to update your profile picture',
          style: TextStyle(fontSize: 14, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context, ProfilePictureState state) {
    if (state is ProfilePictureLoading) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onCancel?.call();
            },
            child: const Text('Cancel'),
          ),
        ],
      );
    } else if (state is ProfilePictureError) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onCancel?.call();
            },
            child: const Text('Cancel'),
          ),
          if (state.isRetryable)
            ElevatedButton(
              onPressed: () {
                // Retry the last operation
                context.read<ProfilePictureBloc>().add(
                  RetryUploadEvent(
                    lastOperation: ProfilePictureOperation.pickFromGallery, // Default to gallery
                  ),
                );
              },
              child: const Text('Retry'),
            ),
        ],
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                context.read<ProfilePictureBloc>().add(const PickFromGalleryEvent());
              },
              icon: const Icon(Icons.photo_library),
              label: const Text('Gallery'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                context.read<ProfilePictureBloc>().add(const TakePhotoEvent());
              },
              icon: const Icon(Icons.camera_alt),
              label: const Text('Camera'),
            ),
          ),
        ],
      );
    }
  }

  /// Show the profile picture upload dialog
  static Future<void> show(
    BuildContext context, {
    required VoidCallback onSuccess,
    VoidCallback? onCancel,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ProfilePictureUploadDialog(
        onSuccess: onSuccess,
        onCancel: onCancel,
      ),
    );
  }
}
