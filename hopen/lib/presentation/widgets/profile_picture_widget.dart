import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import '../../statefulbusinesslogic/core/services/profile_picture_service.dart';

/// Custom cache manager that bypasses certificate issues for development
class HopenCacheManager extends CacheManager with ImageCacheManager {
  static const key = 'hopenCachedImageData';

  static HopenCacheManager? _instance;

  factory HopenCacheManager() {
    return _instance ??= HopenCacheManager._();
  }

  HopenCacheManager._() : super(
    Config(
      key,
      stalePeriod: const Duration(days: 7), // Cache for 7 days
      maxNrOfCacheObjects: 200, // Limit cache size
      repo: JsonCacheInfoRepository(databaseName: key),
      fileService: HopenHttpFileService(), // Use our custom HTTP service
    ),
  );
}

/// Custom HTTP file service that bypasses certificate issues
class HopenHttpFileService extends HttpFileService {
  @override
  Future<FileServiceResponse> get(String url, {Map<String, String>? headers}) async {
    print('🌐 HopenHttpFileService fetching: $url');

    try {
      // Create HTTP client that bypasses certificate verification
      final client = HttpClient();
      client.badCertificateCallback = (cert, host, port) => true;

      final request = await client.getUrl(Uri.parse(url));
      if (headers != null) {
        headers.forEach((key, value) {
          request.headers.add(key, value);
        });
      }

      final response = await request.close();
      final bytes = await consolidateHttpClientResponseBytes(response);

      print('✅ HopenHttpFileService success: ${bytes.length} bytes');

      return HttpGetResponse(response);
    } catch (e) {
      print('❌ HopenHttpFileService error: $e');
      rethrow;
    }
  }
}

// HopenNetworkImageProvider removed - replaced with CachedNetworkImage + HopenCacheManager
// This provides better caching, persistence, and performance while maintaining certificate bypass

/// A reusable profile picture widget that displays a Superellipse avatar
/// with proper loading states, error handling, and fallback to initials
class ProfilePictureWidget extends StatelessWidget {
  const ProfilePictureWidget({
    super.key,
    this.imageUrl,
    this.firstName,
    this.lastName,
    this.radius = 40,
    this.onTap,
    this.showEditIcon = false,
    this.backgroundColor,
  });
  final String? imageUrl;
  final String? firstName;
  final String? lastName;
  final double radius;
  final VoidCallback? onTap;
  final bool showEditIcon;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    print('🖼️ ProfilePictureWidget.build() - imageUrl: $imageUrl, firstName: $firstName, lastName: $lastName');
    
    final initials = ProfilePictureService.generateInitials(
      firstName,
      lastName,
    );
    final avatarColor =
        backgroundColor ??
        Color(ProfilePictureService.getAvatarColorForInitials(initials));

    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            width: radius * 2,
            height: radius * 2,
            decoration: ShapeDecoration(
              shape: RoundedSuperellipseBorder(
                borderRadius: BorderRadius.circular((radius * 2) * 0.4),
              ),
              color: avatarColor,
            ),
            child: _buildAvatarContent(initials, avatarColor),
          ),
          if (showEditIcon)
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: ShapeDecoration(
                  shape: RoundedSuperellipseBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      width: 2,
                    ),
                  ),
                  color: Theme.of(context).primaryColor,
                ),
                child: Icon(
                  Icons.camera_alt,
                  size: radius * 0.4,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAvatarContent(String initials, Color avatarColor) {
    print('🖼️ _buildAvatarContent() - imageUrl: $imageUrl, initials: $initials');
    
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      print('🖼️ Image URL is provided: $imageUrl');
      
      if (imageUrl!.startsWith('http')) {
        print('🖼️ Using CachedNetworkImage with HopenCacheManager (bypasses certificate issues + better caching)');
        return ClipRRect(
          borderRadius: BorderRadius.circular((radius * 2) * 0.4),
          child: CachedNetworkImage(
            imageUrl: imageUrl!,
            cacheManager: HopenCacheManager(),
            width: radius * 2,
            height: radius * 2,
            fit: BoxFit.cover,
            placeholder: (context, url) {
              print('🔄 CachedNetworkImage loading: $url');
              return _buildLoadingWidget();
            },
            errorWidget: (context, url, error) {
              print('❌ CachedNetworkImage error for $url: $error');
              return _buildInitialsWidget(initials, avatarColor);
            },
            fadeInDuration: const Duration(milliseconds: 200),
            fadeOutDuration: const Duration(milliseconds: 100),
          ),
        );
      } else {
        print('🖼️ Using Image.file for local file path');
        // Treat as local file path
        return ClipRRect(
          borderRadius: BorderRadius.circular((radius * 2) * 0.4),
          child: Image.file(
            File(imageUrl!),
            width: radius * 2,
            height: radius * 2,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              print('❌ Local file load error: $error');
              return _buildInitialsWidget(initials, avatarColor);
            },
          ),
        );
      }
    }

    print('🖼️ No image URL, showing initials: $initials');
    return _buildInitialsWidget(initials, avatarColor);
  }

  Widget _buildLoadingWidget() => Container(
    width: radius * 2,
    height: radius * 2,
    decoration: ShapeDecoration(
      shape: RoundedSuperellipseBorder(
        borderRadius: BorderRadius.circular((radius * 2) * 0.4),
      ),
      color: Colors.grey[200],
    ),
    child: Center(
      child: SizedBox(
        width: radius * 0.6,
        height: radius * 0.6,
        child: const CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    ),
  );

  Widget _buildInitialsWidget(String initials, Color backgroundColor) =>
      Container(
        width: radius * 2,
        height: radius * 2,
        decoration: ShapeDecoration(
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular((radius * 2) * 0.4),
          ),
          color: backgroundColor,
        ),
        child: Center(
          child: Text(
            initials,
            style: TextStyle(
              color: Colors.white,
              fontSize: radius * 0.6,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
}

/// Profile picture picker dialog
class ProfilePicturePickerDialog extends StatelessWidget {
  const ProfilePicturePickerDialog({
    super.key,
    this.onCameraSelected,
    this.onGallerySelected,
    this.onRemove,
    this.hasCurrentPicture = false,
    this.imageUrl,
    this.firstName,
    this.lastName,
  });
  final VoidCallback? onCameraSelected;
  final VoidCallback? onGallerySelected;
  final VoidCallback? onRemove;
  final bool hasCurrentPicture;
  final String? imageUrl;
  final String? firstName;
  final String? lastName;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final spacingHeight = screenHeight / 48;
    final fieldHeight = screenHeight / 16;
    final avatarSize = screenHeight * 0.25; // Larger size for dialog

    final initials = ProfilePictureService.generateInitials(
      firstName,
      lastName,
    );
    final avatarColor = Color(ProfilePictureService.getAvatarColorForInitials(initials));

    return AlertDialog(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedSuperellipseBorder(
        borderRadius: BorderRadius.all(Radius.circular(18)),
      ),
      contentPadding: const EdgeInsets.all(24),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Large profile picture with superellipse borders
          Container(
            width: avatarSize,
            height: avatarSize,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(18)),
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(18)),
              child: _buildLargeAvatarContent(initials, avatarColor, avatarSize),
            ),
          ),
          SizedBox(height: spacingHeight * 2),
          // Buttons row
          SizedBox(
            height: fieldHeight,
            child: Row(
              children: [
                Expanded(
                  child: _buildButton(
                    context: context,
                    label: 'Gallery',
                    icon: Icons.photo_library,
                    onPressed: () {
                      Navigator.pop(context);
                      onGallerySelected?.call();
                    },
                    fieldHeight: fieldHeight,
                    spacingHeight: spacingHeight,
                  ),
                ),
                SizedBox(width: spacingHeight),
                Expanded(
                  child: _buildButton(
                    context: context,
                    label: 'Camera',
                    icon: Icons.camera_alt,
                    onPressed: () {
                      Navigator.pop(context);
                      onCameraSelected?.call();
                    },
                    fieldHeight: fieldHeight,
                    spacingHeight: spacingHeight,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLargeAvatarContent(String initials, Color avatarColor, double avatarSize) {
    print('🖼️ _buildLargeAvatarContent() - imageUrl: $imageUrl, initials: $initials');
    
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      print('🖼️ Large avatar - Image URL is provided: $imageUrl');
      
      if (imageUrl!.startsWith('http')) {
        print('🖼️ Large avatar - Using CachedNetworkImage with HopenCacheManager (bypasses certificate issues + better caching)');
        return CachedNetworkImage(
          imageUrl: imageUrl!,
          cacheManager: HopenCacheManager(),
          width: avatarSize,
          height: avatarSize,
          fit: BoxFit.cover,
          placeholder: (context, url) {
            print('🔄 Large avatar - CachedNetworkImage loading: $url');
            return _buildLargeLoadingWidget(avatarSize);
          },
          errorWidget: (context, url, error) {
            print('❌ Large avatar - CachedNetworkImage error for $url: $error');
            return _buildLargeInitialsWidget(initials, avatarColor, avatarSize);
          },
          fadeInDuration: const Duration(milliseconds: 200),
          fadeOutDuration: const Duration(milliseconds: 100),
        );
      } else {
        print('🖼️ Large avatar - Using Image.file for local file path');
        // Treat as local file path
        return Image.file(
          File(imageUrl!),
          width: avatarSize,
          height: avatarSize,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            print('❌ Large avatar - Local file load error: $error');
            return _buildLargeInitialsWidget(initials, avatarColor, avatarSize);
          },
        );
      }
    }

    print('🖼️ Large avatar - No image URL, showing initials: $initials');
    return _buildLargeInitialsWidget(initials, avatarColor, avatarSize);
  }

  Widget _buildLargeLoadingWidget(double avatarSize) => Container(
    width: avatarSize,
    height: avatarSize,
    color: Colors.grey[200],
    child: Center(
      child: SizedBox(
        width: avatarSize * 0.3,
        height: avatarSize * 0.3,
        child: const CircularProgressIndicator(
          strokeWidth: 3,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    ),
  );

  Widget _buildLargeInitialsWidget(String initials, Color backgroundColor, double avatarSize) =>
      Container(
        width: avatarSize,
        height: avatarSize,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: const BorderRadius.all(Radius.circular(18)),
        ),
        child: Center(
          child: Text(
            initials,
            style: TextStyle(
              color: Colors.white,
              fontSize: avatarSize * 0.3,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );

  Widget _buildButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    required VoidCallback? onPressed,
    required double fieldHeight,
    required double spacingHeight,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        padding: EdgeInsets.zero,
        minimumSize: Size.zero,
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        fixedSize: Size.fromHeight(fieldHeight),
      ),
      icon: Icon(icon),
      label: Text(label),
    );
  }

  /// Show the profile picture picker dialog
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onCameraSelected,
    VoidCallback? onGallerySelected,
    VoidCallback? onRemove,
    bool hasCurrentPicture = false,
    String? imageUrl,
    String? firstName,
    String? lastName,
  }) => showDialog(
    context: context,
    builder: (context) => ProfilePicturePickerDialog(
      onCameraSelected: onCameraSelected,
      onGallerySelected: onGallerySelected,
      onRemove: onRemove,
      hasCurrentPicture: hasCurrentPicture,
      imageUrl: imageUrl,
      firstName: firstName,
      lastName: lastName,
    ),
  );
}
