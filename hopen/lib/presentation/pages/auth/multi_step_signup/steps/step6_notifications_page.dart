import 'package:flutter/material.dart';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../widgets/custom_toast.dart';
import '../signup_step_base.dart';
import '../../../../../statefulbusinesslogic/bloc/notification_permission/notification_permission_bloc.dart';
import '../../../../../statefulbusinesslogic/bloc/notification_permission/notification_permission_event.dart';
import '../../../../../statefulbusinesslogic/bloc/notification_permission/notification_permission_state.dart';
import '../../../../../di/injection_container_refactored.dart' as di;

class Step6NotificationsPage extends SignupStepBase {
  Step6NotificationsPage({
    required super.currentStep,
    required super.totalSteps,
    required this.onNextCustom,
    required VoidCallback super.onBack,
    required this.initialValue,
    super.key,
  }) : super(title: 'Don\'t miss any notifications', onNext: () {});
  final void Function(bool) onNextCustom;
  final bool initialValue;

  @override
  State<Step6NotificationsPage> createState() => _Step6NotificationsPageState();
}

class _Step6NotificationsPageState
    extends SignupStepBaseState<Step6NotificationsPage> {
  late NotificationPermissionBloc _permissionBloc;
  bool _requesting = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _notificationsEnabled = false;

  @override
  void initState() {
    super.initState();
    _permissionBloc = di.sl<NotificationPermissionBloc>();

    // Listen for permission request results
    _permissionBloc.stream.listen((state) {
      if (!mounted) return;
      if (state is PermissionGranted) {
        setState(() {
          _requesting = false;
          _hasError = false;
          _errorMessage = '';
          _notificationsEnabled = true;
        });
        CustomToast.showSuccess(context, 'Notifications are already enabled');
        // DO NOT automatically proceed - let user explicitly choose to create account
      } else if (state is PermissionDenied) {
        setState(() {
          _requesting = false;
          _hasError = false;
          _errorMessage = '';
          _notificationsEnabled = false;
        });
        CustomToast.showInfo(context, 'You can enable notifications later in settings');
        // Don't automatically proceed - let user choose to continue or retry
      } else if (state is PermissionError) {
        setState(() {
          _requesting = false;
          _hasError = true;
          _errorMessage = state.message;
          _notificationsEnabled = false;
        });
        CustomToast.showError(context, state.message);
        // Don't automatically proceed - show retry option
      }
    });

    // Automatically request permission when page is shown (native dialog appears)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_requesting && !_hasError) {
        _requestPermission();
      }
    });
  }

  Future<bool> _isPermissionAlreadyGranted() async {
    try {
      if (Platform.isAndroid) {
        final status = await Permission.notification.status;
        return status.isGranted;
      }

      final settings = await FirebaseMessaging.instance.getNotificationSettings();
      return settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;
    } catch (_) {
      // In case of any unexpected error, assume not granted so that the request flow can handle it.
      return false;
    }
  }

  Future<void> _requestPermission() async {
    if (_requesting) return; // Prevent duplicate dialogs while keeping button enabled

    final alreadyGranted = await _isPermissionAlreadyGranted();
    if (alreadyGranted) {
      if (mounted) {
        CustomToast.showInfo(context, 'Notifications are already enabled');
      }
      return;
    }

    setState(() {
      _requesting = true;
      _hasError = false;
      _errorMessage = '';
      _notificationsEnabled = false;
    });

    _permissionBloc.add(const RequestPermissionEvent());
  }

  @override
  void handleNext() {
    // Pass whether notifications were enabled (based on permission result)
    widget.onNextCustom(_notificationsEnabled);
  }

  @override
  List<Widget> buildStepContent(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final spacingHeight = screenHeight / 48;
    final fieldHeight = screenHeight / 16;

    return [
      // Notification explanation – unified style with other notices
      Text(
        'Get important updates about your friends, contacts, bubble, and more.',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.7),
          fontSize: 16,
          height: 1.5,
        ),
      ),
      SizedBox(height: spacingHeight * 2),

      // Notification illustration icon
      Center(
        child: Container(
          width: screenWidth * 0.35,
          height: screenWidth * 0.35,
          padding: const EdgeInsets.all(15),
          child: Image.asset(
            'assets/images/3d/500px/normal/bell.png',
            fit: BoxFit.contain,
          ),
        ),
      ),


      // Error message display
      if (_hasError) ...[
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          margin: EdgeInsets.only(bottom: spacingHeight),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 24,
              ),
              
              Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],

      // Primary enable button
      SizedBox(
        width: double.infinity,
        height: fieldHeight,
        child: ElevatedButton(
          onPressed: () => _requestPermission(),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            shape: RoundedSuperellipseBorder(
              borderRadius: BorderRadius.circular(18),
            ),
            padding: EdgeInsets.zero,
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: const Text(
            'Enable notifications',
            style: TextStyle(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Omnes',
            ),
          ),
        ),
      ),

      SizedBox(height: spacingHeight),

      // Removed Skip button and informational text to enforce notification decision on this step
    ];
  }

  @override
  bool isFormValid() {
    // Always valid - it's just a preference
    return true;
  }

  @override
  void dispose() {
    _permissionBloc.close();
    super.dispose();
  }
}
