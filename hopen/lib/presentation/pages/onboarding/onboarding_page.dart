import 'package:flutter/material.dart';

/// Data model representing the content for a single onboarding screen.
///
/// This model is used by [OnboardingPage] to define the content and appearance
/// of each individual page within the onboarding flow.
class OnboardingPageModel {
  /// Creates an instance of [OnboardingPageModel].
  ///
  /// Requires `title`, `description`, and `image` path.
  /// `bgColor` defaults to `Colors.blue` and `textColor` to `Colors.white` if not specified.
  OnboardingPageModel({
    required this.title,
    required this.description,
    required this.image,
    this.bgColor = Colors.blue,
    this.textColor = Colors.white,
  });

  /// The primary heading displayed on the page.
  final String title;

  /// A more detailed explanation or description.
  final String description;

  /// The asset path for the image illustration.
  final String image;

  /// The background color for this specific page.
  final Color bgColor;

  /// The color for the title and description text.
  final Color textColor;
}

/// A widget that displays a series of swipeable onboarding screens.
///
/// This widget takes a list of [OnboardingPageModel] instances to dynamically
/// build each page of the onboarding flow. It handles page transitions, navigation
/// controls (Next/Hop in, Skip, Back), and triggers an `onComplete` callback
/// when the flow is finished or skipped.
///
/// Key Design Aspects:
/// - **Dynamic Content**: Populated from `OnboardingData.getPages()`.
/// - **Responsive Layout**: UI elements, particularly images and text, adapt to screen size.
/// - **Animated Transitions**: Smooth background color changes and page swipes.
/// - **Clear Navigation**: Consistent AppBar with Skip/Back and a prominent Next/Hop in button.
///
/// To customize behavior or add new features:
/// - Modify `_onSkip`, `_onNext`, `_onBack` for navigation logic changes.
/// - Adjust `itemBuilder` within `PageView.builder` to alter page layout or add elements.
/// - Update AppBar or bottom navigation buttons within the main `Scaffold` structure.
class OnboardingPage extends StatefulWidget {
  /// Creates an instance of [OnboardingPage].
  const OnboardingPage({
    required this.pages,
    required this.onComplete,
    super.key,
  });

  /// The list of pages to display, defined by [OnboardingPageModel].
  final List<OnboardingPageModel> pages;

  /// A callback function triggered when the onboarding flow is completed or skipped.
  final VoidCallback onComplete;

  @override
  _OnboardingPageState createState() => _OnboardingPageState();
}

/// State management for the [OnboardingPage] widget.
///
/// Handles the current page index, page controller for [PageView], and the logic
/// for navigation actions. It also calculates responsive dimensions for UI elements.
class _OnboardingPageState extends State<OnboardingPage> {
  /// Tracks the index of the currently visible page (0-based).
  int _currentPage = 0;

  /// Controls the [PageView] enabling programmatic page transitions.
  final PageController _pageController = PageController();

  /// Handles the action when the 'Skip' or 'Close' button is pressed.
  ///
  /// Triggers the [onComplete] callback provided to the widget.
  void _onSkip() {
    widget.onComplete();
  }

  /// Handles the action when the 'Next' or 'Hop in' button is pressed.
  ///
  /// Advances to the next page if available, otherwise triggers the
  /// [onComplete] callback.
  void _onNext() {
    if (_currentPage == widget.pages.length - 1) {
      widget.onComplete(); // Last page, complete the flow
    } else {
      // Animate to the next page
      _pageController.animateToPage(
        _currentPage + 1,
        curve: Curves.easeInOutCubic,
        duration: const Duration(milliseconds: 250),
      );
    }
  }

  /// Handles the action for the 'Back' button press.
  ///
  /// Animates the [PageView] to the previous page.
  void _onBack() {
    _pageController.animateToPage(
      _currentPage - 1,
      curve: Curves.easeInOutCubic,
      duration: const Duration(milliseconds: 250),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Calculate responsive sizes based on screen dimensions.
    // These are used to ensure UI elements scale appropriately.
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final fieldHeight = screenHeight / 16;
    final logoHeight = 64 * (screenWidth / 375);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        automaticallyImplyLeading: false,
        leadingWidth: 72,
        // Show Back button only after the first page
        leading:
            _currentPage > 0
                ? Padding(
                  padding: const EdgeInsets.only(left: 24),
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: _onBack,
                  ),
                )
                : null, // No leading button on the first page
        title: Image.asset(
          'assets/images/hopen-logotype.png',
          height: logoHeight,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            print('Error loading logo: $error');
            return const SizedBox(height: 40);
          },
        ),
        centerTitle: true,
        actions: [
          // Skip button remains consistently available
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: TextButton(
              onPressed: _onSkip,
              child: const Text(
                'Skip',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          ),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        // Dynamically update background color based on current page
        color: widget.pages[_currentPage].bgColor,
        child: SafeArea(
          top: false, // AppBar handles top inset
          child: Column(
            children: [
              // Main content area using PageView
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: widget.pages.length,
                  onPageChanged: (idx) {
                    // Update state when page view changes
                    setState(() {
                      _currentPage = idx;
                    });
                  },
                  itemBuilder: (context, idx) {
                    final item = widget.pages[idx];
                    // Build individual page content.
                    // The layout consists of an image section (flex: 3) and a text content section (flex: 1).
                    return Column(
                      children: [
                        // Image section
                        Expanded(
                          flex: 3,
                          child: Padding(
                            // Padding around the image: LTRB(32, 32, 32, 0) to minimize gap to title.
                            padding: const EdgeInsets.fromLTRB(
                              32,
                              32,
                              32,
                              0, // Minimized bottom padding for closer title.
                            ),
                            child: Image.asset(
                              item.image,
                              // Image dimensions are responsive: 60% of screen width, 30% of screen height.
                              width: screenWidth * 0.6,
                              height: screenHeight * 0.3,
                              fit:
                                  BoxFit
                                      .contain, // Ensures entire image is visible, scaled down if necessary.
                              errorBuilder: (context, error, stackTrace) {
                                print(
                                  'Error loading onboarding image ${item.image}: $error',
                                );
                                return const Center(
                                  child: Icon(
                                    Icons.image_not_supported,
                                    color: Colors.white54,
                                    size: 50,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        // Text content section
                        Expanded(
                          child: Column(
                            children: [
                              Padding(
                                // Padding for the title: LTRB(16, 0, 16, 16) to minimize gap from image.
                                padding: const EdgeInsets.fromLTRB(
                                  16,
                                  0, // Minimized top padding.
                                  16,
                                  16,
                                ),
                                child: Text(
                                  item.title,
                                  textAlign: TextAlign.center,
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: item.textColor,
                                  ),
                                ),
                              ),
                              Container(
                                constraints: const BoxConstraints(
                                  maxWidth: 280,
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 8,
                                ),
                                child: Text(
                                  item.description,
                                  textAlign: TextAlign.center,
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(color: item.textColor),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),

              // Bottom action button (Next/Hop in)
              Padding(
                padding: EdgeInsets.only(
                  left: 24,
                  right: 24,
                  bottom: MediaQuery.of(context).padding.bottom + 10,
                  top: 10,
                ),
                child: SizedBox(
                  width: double.infinity,
                  height: fieldHeight,
                  child: ElevatedButton(
                    onPressed: _onNext,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedSuperellipseBorder(
                        borderRadius: BorderRadius.circular(18),
                      ),
                      padding: EdgeInsets.zero,
                      minimumSize: const Size(0, 0),
                      maximumSize: const Size(double.infinity, double.infinity),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      _currentPage == widget.pages.length - 1
                          ? 'Hop in'
                          : 'Next',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
