name: hopen
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.7.2 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  shared_preferences: ^2.2.2
  uuid: ^4.3.3
  dio: ^5.4.0
  dio_http2_adapter: ^2.3.2
  crypto: ^3.0.2
  flutter_secure_storage: ^9.2.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_bloc: ^8.1.6
  go_router: ^14.2.7
  google_fonts: ^6.2.1
  provider: ^6.1.2
  equatable: ^2.0.5
  http: ^1.2.2
  bloc: ^8.1.4
  animations: ^2.0.8

  # Platform-specific HTTP/3 clients
  cupertino_http: ^2.2.0  # iOS/macOS HTTP/3 support
  cronet_http: ^1.3.4     # Android HTTP/3 support
  flutter_svg: ^2.0.10
  vector_math: ^2.1.4
  intl: ^0.20.2
  cached_network_image: ^3.3.1
  permission_handler: ^11.3.1
  auto_size_text: ^3.0.0
  connectivity_plus: ^5.0.2
  shimmer: ^3.0.0

  # Audio recording
  record: ^5.2.1
  record_platform_interface: ^1.3.0
  record_android: ^1.3.3
  record_ios: ^1.0.0
  record_web: ^1.1.8

  # Generated API client for Encore.go backend
  retrofit: ^4.1.0
  json_annotation: ^4.9.0

  # WebRTC for calls (MQTT replaces WebSocket for signaling)
  flutter_webrtc: ^0.13.0

  # MQTT5 client for real-time notifications
  mqtt5_client: ^4.12.1

  # Firebase (Updated versions)
  firebase_core: ^3.14.0
  firebase_messaging: ^15.2.7
  flutter_local_notifications: ^17.2.2
  
  # Ory Stack Authentication
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.1
  jwt_decoder: ^2.0.1
  webview_flutter: ^4.9.0
  
  image_picker: ^1.1.2
  image: ^4.2.0
  file_picker: ^8.0.6
  path_provider: ^2.1.3
  audioplayers: ^6.0.0
  logger: ^2.4.0

  # Drift (for local database)
  drift: ^2.26.0
  path: ^1.9.0
  camera: ^0.11.0
  video_player: ^2.8.7

  typed_data: ^1.3.2

  # WebSocket for real-time communication (legacy, being phased out)
  web_socket_channel: ^3.0.0

  # PostHog for analytics
  posthog_flutter: ^4.6.0

  # Dependency injection
  get_it: ^7.7.0

  # Background tasks
  workmanager: ^0.7.0
  background_fetch: ^1.2.3

  # MIME handling
  mime: ^1.0.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  device_preview: ^1.2.0
  bloc_test: ^9.1.7
  mockito: ^5.4.4
  network_image_mock: ^2.1.1

  # Code generation for API client
  retrofit_generator: ^8.1.2
  json_serializable: ^6.8.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  build_runner: ^2.4.12
  drift_dev: ^2.26.0

# Temporary override to pull latest record_linux with startStream implemented
dependency_overrides:
  record_linux: ^1.1.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  assets:
    - assets/fonts/
    - assets/flags/1x1/
    - assets/flags/4x3/
    - assets/icons/
    - assets/images/
    - assets/images/3d/
    - assets/images/3d/200px/
    - assets/images/3d/200px/normal/
    - assets/images/3d/200px/premium/
    - assets/images/3d/200px/white/
    - assets/images/3d/500px/
    - assets/images/3d/500px/normal/
    - assets/images/3d/500px/premium/
    - assets/navbar/
    - assets/animations/

  fonts:
    - family: Omnes
      fonts:
        - asset: assets/fonts/Omnes-Hairline.ttf
          weight: 100
        - asset: assets/fonts/Omnes-Hairline-Italic.ttf
          weight: 100
          style: italic
        - asset: assets/fonts/Omnes-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Omnes-ExtraLight-Italic.ttf
          weight: 200
          style: italic
        - asset: assets/fonts/Omnes-Light.ttf
          weight: 300
        - asset: assets/fonts/Omnes-Light-Italic.ttf
          weight: 300
          style: italic
        - asset: assets/fonts/Omnes-Regular.ttf
          weight: 400
        - asset: assets/fonts/Omnes-Italic.ttf
          weight: 400
          style: italic
        - asset: assets/fonts/Omnes-Medium.ttf
          weight: 500
        - asset: assets/fonts/Omnes-Medium-Italic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/Omnes-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Omnes-SemiBold-Italic.ttf
          weight: 600
          style: italic
        - asset: assets/fonts/Omnes-Bold.ttf
          weight: 700
        - asset: assets/fonts/Omnes-Bold-Italic.ttf
          weight: 700
          style: italic
        - asset: assets/fonts/Omnes-Black.ttf
          weight: 900
        - asset: assets/fonts/Omnes-Black-Italic.ttf
          weight: 900
          style: italic
