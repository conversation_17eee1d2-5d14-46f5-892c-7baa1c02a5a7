// Example demonstrating the enhanced error system
// This shows how to use the new features in practice

import '../lib/statefulbusinesslogic/core/error/result.dart';
import '../lib/statefulbusinesslogic/core/error/do_notation.dart';
import '../lib/statefulbusinesslogic/core/error/option.dart';

// Mock models for demonstration
class User {
  final String id;
  final String name;
  final String email;
  
  const User({required this.id, required this.name, required this.email});
}

class Bubble {
  final String id;
  final String name;
  final int memberCount;
  final int maxMembers;
  final bool isActive;
  
  const Bubble({
    required this.id,
    required this.name,
    required this.memberCount,
    required this.maxMembers,
    required this.isActive,
  });
}

class BubbleWithPermissions {
  final Bubble bubble;
  final List<String> permissions;
  
  const BubbleWithPermissions(this.bubble, this.permissions);
}

// Mock repository methods
Future<Result<User>> getUserById(String userId) async {
  await Future.delayed(Duration(milliseconds: 100));
  if (userId == 'invalid') {
    return Result.failure(ValidationError(message: 'Invalid user ID'));
  }
  return Result.success(User(id: userId, name: '<PERSON> Doe', email: '<EMAIL>'));
}

Future<Result<Bubble>> getBubbleById(String bubbleId) async {
  await Future.delayed(Duration(milliseconds: 100));
  if (bubbleId == 'not-found') {
    return Result.failure(BubbleNotFoundError(bubbleId));
  }
  return Result.success(Bubble(
    id: bubbleId,
    name: 'Test Bubble',
    memberCount: 3,
    maxMembers: 5,
    isActive: true,
  ));
}

Future<Result<List<String>>> checkPermissions(String userId, String bubbleId) async {
  await Future.delayed(Duration(milliseconds: 50));
  return Result.success(['read', 'write', 'invite']);
}

Option<User> findUserInCache(String userId) {
  // Simulate cache lookup
  if (userId == 'cached-user') {
    return Option.some(User(id: userId, name: 'Cached User', email: '<EMAIL>'));
  }
  return Option.none();
}

// Example 1: Using Do Notation for clean composition
Future<Result<BubbleWithPermissions>> loadBubbleWithPermissions(
  String userId,
  String bubbleId,
) async {
  return TaskResultDo.Do(($) async {
    // All these operations are chained cleanly
    final user = await $(getUserById(userId));
    final bubble = await $(getBubbleById(bubbleId));
    final permissions = await $(checkPermissions(user.id, bubble.id));
    
    // Validate bubble is active
    final activeBubble = $(Validate.that(
      bubble,
      (b) => b.isActive,
      () => ValidationError(message: 'Bubble is not active'),
    ).toFuture());
    
    return BubbleWithPermissions(activeBubble, permissions);
  });
}

// Example 2: Using Option for nullable values with fallback
Future<Result<User>> getUserWithFallback(String userId) async {
  // Try cache first, then API
  final cachedUser = findUserInCache(userId);
  
  return cachedUser
    .toResultWith(ValidationError(message: 'User not in cache'))
    .toFuture()
    .recoverWithAsync((error) => getUserById(userId))
    .tapAsync((user) async => print('Loaded user: ${user.name}'))
    .tapErrorAsync((error) async => print('Failed to load user: ${error.userMessage}'));
}

// Example 3: Parallel operations with error handling
Future<Result<Map<String, dynamic>>> loadDashboardData(String userId) async {
  final tasks = [
    getUserById(userId).mapAsync((user) => {'user': user}),
    getBubbleById('bubble-1').mapAsync((bubble) => {'bubble': bubble}),
    checkPermissions(userId, 'bubble-1').mapAsync((perms) => {'permissions': perms}),
  ];
  
  return ResultUtils.sequenceAsync(tasks)
    .mapAsync((results) async {
      final combined = <String, dynamic>{};
      for (final result in results) {
        combined.addAll(result);
      }
      return combined;
    })
    .timeout(Duration(seconds: 5));
}

// Example 4: Validation with multiple conditions
Result<String> validateEmail(String email) {
  return ResultDo.Do(($) {
    final nonEmpty = $(Validate.notEmpty(email, () => 
      ValidationError(message: 'Email cannot be empty')));
    
    final validFormat = $(Validate.that(
      nonEmpty,
      (e) => e.contains('@') && e.contains('.'),
      () => ValidationError(message: 'Invalid email format'),
    ));
    
    final validLength = $(Validate.that(
      validFormat,
      (e) => e.length >= 5 && e.length <= 100,
      () => ValidationError(message: 'Email length must be between 5 and 100 characters'),
    ));
    
    return validLength;
  });
}

// Example 5: Error recovery with multiple fallbacks
Future<Result<User>> getUserWithMultipleFallbacks(String userId) async {
  return getUserById(userId)
    .recoverWithAsync((error) async {
      print('Primary failed: ${error.userMessage}, trying cache...');
      return findUserInCache(userId)
        .toResultWith(ValidationError(message: 'Not in cache'));
    })
    .recoverWithAsync((error) async {
      print('Cache failed: ${error.userMessage}, using default...');
      return Result.success(User(
        id: 'default',
        name: 'Default User',
        email: '<EMAIL>',
      ));
    });
}

// Example 6: Conditional operations
Future<Result<String>> joinBubbleIfAllowed(String userId, String bubbleId) async {
  return TaskResultDo.Do(($) async {
    final user = await $(getUserById(userId));
    final bubble = await $(getBubbleById(bubbleId));
    
    // Check if bubble has space
    final hasSpace = $(Validate.that(
      bubble,
      (b) => b.memberCount < b.maxMembers,
      () => BubbleCapacityExceededError(
        bubbleId: bubbleId,
        currentCount: bubble.memberCount,
        maxCapacity: bubble.maxMembers,
      ),
    ).toFuture());
    
    // Check if bubble is active
    final isActive = $(Validate.that(
      hasSpace,
      (b) => b.isActive,
      () => ValidationError(message: 'Bubble is not active'),
    ).toFuture());
    
    // Simulate joining
    await Future.delayed(Duration(milliseconds: 100));
    return 'Successfully joined ${isActive.name}';
  });
}

// Example usage
void main() async {
  print('=== Enhanced Error System Examples ===\n');
  
  // Example 1: Do Notation
  print('1. Loading bubble with permissions using Do notation:');
  final bubbleResult = await loadBubbleWithPermissions('user-1', 'bubble-1');
  bubbleResult.fold(
    onSuccess: (data) => print('✅ Success: ${data.bubble.name} with ${data.permissions.length} permissions'),
    onFailure: (error) => print('❌ Error: ${error.userMessage}'),
  );
  
  // Example 2: Option with fallback
  print('\n2. Getting user with cache fallback:');
  final userResult = await getUserWithFallback('cached-user');
  userResult.fold(
    onSuccess: (user) => print('✅ Success: ${user.name}'),
    onFailure: (error) => print('❌ Error: ${error.userMessage}'),
  );
  
  // Example 3: Parallel operations
  print('\n3. Loading dashboard data in parallel:');
  final dashboardResult = await loadDashboardData('user-1');
  dashboardResult.fold(
    onSuccess: (data) => print('✅ Success: Loaded ${data.keys.join(', ')}'),
    onFailure: (error) => print('❌ Error: ${error.userMessage}'),
  );
  
  // Example 4: Validation
  print('\n4. Email validation:');
  final emailResult = validateEmail('<EMAIL>');
  emailResult.fold(
    onSuccess: (email) => print('✅ Valid email: $email'),
    onFailure: (error) => print('❌ Invalid email: ${error.userMessage}'),
  );
  
  // Example 5: Multiple fallbacks
  print('\n5. User with multiple fallbacks:');
  final fallbackResult = await getUserWithMultipleFallbacks('invalid');
  fallbackResult.fold(
    onSuccess: (user) => print('✅ Got user: ${user.name}'),
    onFailure: (error) => print('❌ Error: ${error.userMessage}'),
  );
  
  // Example 6: Conditional operations
  print('\n6. Joining bubble with validation:');
  final joinResult = await joinBubbleIfAllowed('user-1', 'bubble-1');
  joinResult.fold(
    onSuccess: (message) => print('✅ $message'),
    onFailure: (error) => print('❌ Error: ${error.userMessage}'),
  );
  
  print('\n=== Examples completed ===');
}
