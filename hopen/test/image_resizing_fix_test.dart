import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hopen/provider/services/image_processing_service.dart';
import 'package:hopen/provider/services/avif_processing_service.dart';
import 'package:path/path.dart' as path;

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('Image Resizing Fix Tests', () {
    late Directory tempDir;

    setUpAll(() async {
      tempDir = await Directory.systemTemp.createTemp('image_resizing_test_');
    });

    tearDownAll(() async {
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    test('should allow large images and resize them automatically', () async {
      // Test with non-existent file to check validation logic
      final testImagePath = path.join(tempDir.path, 'non_existent_large.png');

      // Test the image processing pipeline with large image path
      final result = await ImageProcessingService.processImageInIsolate(
        imagePath: testImagePath,
        minResolution: 640,
        maxResolution: 1440, // Should resize to this
        maxFileSizeBytes: 2 * 1024 * 1024, // 2MB
        compressionQuality: 85.0,
      );

      // Should fail with file not found, not with "too large" error
      expect(result.isSuccess, isFalse);
      expect(result.errorMessage, contains('Image file not found'));
      expect(result.errorMessage, isNot(contains('too large')));
      expect(result.errorMessage, isNot(contains('Maximum:')));
    });

    test('should validate image processing service methods exist', () async {
      // Test that the service methods exist and are callable
      expect(ImageProcessingService.processImageInIsolate, isA<Function>());

      // Test with non-existent file to verify error handling
      final testImagePath = path.join(tempDir.path, 'non_existent_small.png');

      final result = await ImageProcessingService.processImageInIsolate(
        imagePath: testImagePath,
        minResolution: 640,
        maxResolution: 1440,
        maxFileSizeBytes: 2 * 1024 * 1024, // 2MB
        compressionQuality: 85.0,
      );

      // Should fail with file not found error
      expect(result.isSuccess, isFalse);
      expect(result.errorMessage, contains('Image file not found'));
    });

    test('should validate image processing configuration', () async {
      // Test that the processing configuration is correct
      expect(ImageProcessingService.processImageInIsolate, isA<Function>());

      // Test with non-existent file to verify the validation logic doesn't reject large images
      final testImagePath = path.join(tempDir.path, 'config_test.png');

      final result = await ImageProcessingService.processImageInIsolate(
        imagePath: testImagePath,
        minResolution: 640,
        maxResolution: 1440,
        maxFileSizeBytes: 2 * 1024 * 1024, // 2MB
        compressionQuality: 85.0,
      );

      // Should fail with file not found, not with size restrictions
      expect(result, isNotNull);
      expect(result.isSuccess, isFalse);
      expect(result.errorMessage, contains('Image file not found'));
      expect(result.errorMessage, isNot(contains('too large')));
      expect(result.errorMessage, isNot(contains('Maximum:')));
    });

    test('should validate AVIF processing with proper resizing parameters', () async {
      // Test AVIF service with resizing parameters (non-existent file)
      final testImagePath = path.join(tempDir.path, 'avif_test_image.png');

      // Test AVIF conversion with resizing
      final result = await AvifProcessingService.convertToAvif(
        inputPath: testImagePath,
        maxFileSizeBytes: 2 * 1024 * 1024,
        initialQuality: 85,
        maxWidth: 1440,
        maxHeight: 1440,
      );

      // In test environment, will return null due to missing FFmpeg
      // but the method should handle parameters correctly
      expect(result, isNull); // Expected in test environment
    });

    test('should validate enhanced image processor integration', () async {
      // Test the enhanced image processor with non-existent file
      final testImagePath = path.join(tempDir.path, 'processor_test_image.png');

      // Create an empty file to avoid file length error
      await File(testImagePath).writeAsBytes([]);

      // Test enhanced image processor
      final result = await EnhancedImageProcessor.processImage(
        inputPath: testImagePath,
        maxFileSizeBytes: 2 * 1024 * 1024,
        initialQuality: 85,
        maxWidth: 1440,
        maxHeight: 1440,
      );

      // Should handle the request properly
      expect(result, isNotNull);
      expect(result.isSuccess, isFalse); // Expected in test environment
      expect(result.errorMessage, contains('AVIF processing failed'));
    });
  });
}
