import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hopen/provider/services/avif_processing_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('AVIF Processing Service Tests', () {
    test('should check AVIF support', () async {
      final isSupported = await AvifProcessingService.isAvifSupported();
      expect(isSupported, isTrue);
    });

    test('should validate AVIF processing service exists and has required methods', () {
      // Test that the service class exists and has the expected methods
      expect(AvifProcessingService.isAvifSupported, isA<Function>());
      expect(AvifProcessingService.encodeToAvif, isA<Function>());
      expect(AvifProcessingService.convertToAvif, isA<Function>());
    });

    test('should handle invalid input paths gracefully', () async {
      final result = await AvifProcessingService.encodeToAvif(
        inputPath: '/non/existent/path.jpg',
        quality: 80,
      );
      expect(result, isNull);
    });

    test('should validate convertToAvif parameters', () async {
      final result = await AvifProcessingService.convertToAvif(
        inputPath: '/non/existent/path.jpg',
        maxFileSizeBytes: 2 * 1024 * 1024, // 2MB
        initialQuality: 80,
        maxWidth: 1440,
        maxHeight: 1440,
      );
      expect(result, isNull);
    });

    test('should handle quality parameter bounds', () async {
      // Test with extreme quality values
      final result1 = await AvifProcessingService.convertToAvif(
        inputPath: '/non/existent/path.jpg',
        maxFileSizeBytes: 2 * 1024 * 1024, // 2MB
        initialQuality: 0, // Minimum quality
        maxWidth: 1440,
        maxHeight: 1440,
      );
      expect(result1, isNull); // Should fail gracefully with invalid input

      final result2 = await AvifProcessingService.convertToAvif(
        inputPath: '/non/existent/path.jpg',
        maxFileSizeBytes: 2 * 1024 * 1024, // 2MB
        initialQuality: 100, // Maximum quality
        maxWidth: 1440,
        maxHeight: 1440,
      );
      expect(result2, isNull); // Should fail gracefully with invalid input
    });
  });
}
