import 'dart:io';
import 'dart:typed_data';

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:hopen/statefulbusinesslogic/bloc/signup/signup_bloc.dart';
import 'package:hopen/statefulbusinesslogic/core/usecases/signup_usecase.dart';
import 'package:hopen/repositories/profile_picture/profile_picture_repository.dart';
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart';
import 'package:hopen/statefulbusinesslogic/core/error/result.dart';

// Generate mocks
@GenerateMocks([SignUpUseCase, ProfilePictureRepository])
import 'profile_picture_integration_test.mocks.dart';

void main() {
  group('Profile Picture Integration Tests', () {
    late SignUpBloc signUpBloc;
    late MockSignUpUseCase mockSignUpUseCase;
    late MockProfilePictureRepository mockProfilePictureRepository;

    setUp(() {
      mockSignUpUseCase = MockSignUpUseCase();
      mockProfilePictureRepository = MockProfilePictureRepository();
      signUpBloc = SignUpBloc(mockSignUpUseCase, mockProfilePictureRepository);
    });

    tearDown(() {
      signUpBloc.close();
    });

    test('should complete signup with profile picture upload', () async {
      // Arrange
      const localImagePath = '/path/to/local/image.jpg';
      const uploadedImageUrl = 'https://cdn.hopenapp.com/user123/profile.jpg';
      
      final mockUser = UserModel(
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        username: 'johndoe',
        profilePictureUrl: null, // Initially null
      );

      final updatedUser = mockUser.copyWith(profilePictureUrl: uploadedImageUrl);

      // Mock successful signup
      when(mockSignUpUseCase.call(any))
          .thenAnswer((_) async => Result.success(mockUser));

      // Mock successful profile picture upload
      when(mockProfilePictureRepository.uploadLocalProfilePicture(localImagePath))
          .thenAnswer((_) async => uploadedImageUrl);

      // Act
      signUpBloc.add(SignUpSubmitted(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        username: 'johndoe',
        birthday: DateTime(1990, 1, 1),
        profilePicturePathOrUrl: localImagePath,
        notificationsEnabled: true,
      ));

      // Assert
      await expectLater(
        signUpBloc.stream,
        emitsInOrder([
          isA<SignUpLoading>(),
          predicate<SignUpSuccess>((state) {
            return state.user.profilePictureUrl == uploadedImageUrl;
          }),
        ]),
      );

      // Verify interactions
      verify(mockSignUpUseCase.call(any)).called(1);
      verify(mockProfilePictureRepository.uploadLocalProfilePicture(localImagePath)).called(1);
    });

    test('should handle profile picture upload failure gracefully', () async {
      // Arrange
      const localImagePath = '/path/to/local/image.jpg';
      
      final mockUser = UserModel(
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        username: 'johndoe',
        profilePictureUrl: null,
      );

      // Mock successful signup
      when(mockSignUpUseCase.call(any))
          .thenAnswer((_) async => Result.success(mockUser));

      // Mock failed profile picture upload
      when(mockProfilePictureRepository.uploadLocalProfilePicture(localImagePath))
          .thenAnswer((_) async => null);

      // Act
      signUpBloc.add(SignUpSubmitted(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        username: 'johndoe',
        birthday: DateTime(1990, 1, 1),
        profilePicturePathOrUrl: localImagePath,
        notificationsEnabled: true,
      ));

      // Assert - should still succeed even if profile picture upload fails
      await expectLater(
        signUpBloc.stream,
        emitsInOrder([
          isA<SignUpLoading>(),
          predicate<SignUpSuccess>((state) {
            return state.user.profilePictureUrl == null; // Should remain null
          }),
        ]),
      );

      // Verify interactions
      verify(mockSignUpUseCase.call(any)).called(1);
      verify(mockProfilePictureRepository.uploadLocalProfilePicture(localImagePath)).called(1);
    });

    test('should not attempt upload for remote URLs', () async {
      // Arrange
      const remoteImageUrl = 'https://example.com/profile.jpg';
      
      final mockUser = UserModel(
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        username: 'johndoe',
        profilePictureUrl: remoteImageUrl,
      );

      // Mock successful signup
      when(mockSignUpUseCase.call(any))
          .thenAnswer((_) async => Result.success(mockUser));

      // Act
      signUpBloc.add(SignUpSubmitted(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        username: 'johndoe',
        birthday: DateTime(1990, 1, 1),
        profilePicturePathOrUrl: remoteImageUrl, // Remote URL
        notificationsEnabled: true,
      ));

      // Assert
      await expectLater(
        signUpBloc.stream,
        emitsInOrder([
          isA<SignUpLoading>(),
          predicate<SignUpSuccess>((state) {
            return state.user.profilePictureUrl == remoteImageUrl;
          }),
        ]),
      );

      // Verify that upload was not attempted for remote URL
      verify(mockSignUpUseCase.call(any)).called(1);
      verifyNever(mockProfilePictureRepository.uploadLocalProfilePicture(any));
    });

    test('should handle partial success with profile picture upload', () async {
      // Arrange
      const localImagePath = '/path/to/local/image.jpg';
      const uploadedImageUrl = 'https://cdn.hopenapp.com/user123/profile.jpg';
      
      final mockUser = UserModel(
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        username: 'johndoe',
        profilePictureUrl: null,
      );

      // Mock partial success (account created but auth failed)
      when(mockSignUpUseCase.call(any))
          .thenAnswer((_) async => Result.partialSuccess(mockUser, 'Account created but login failed'));

      // Mock successful profile picture upload
      when(mockProfilePictureRepository.uploadLocalProfilePicture(localImagePath))
          .thenAnswer((_) async => uploadedImageUrl);

      // Act
      signUpBloc.add(SignUpSubmitted(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        username: 'johndoe',
        birthday: DateTime(1990, 1, 1),
        profilePicturePathOrUrl: localImagePath,
        notificationsEnabled: true,
      ));

      // Assert
      await expectLater(
        signUpBloc.stream,
        emitsInOrder([
          isA<SignUpLoading>(),
          predicate<SignUpPartialSuccess>((state) {
            return state.user.id == 'user123' && 
                   state.message == 'Account created but login failed';
          }),
        ]),
      );

      // Verify that profile picture upload was still attempted
      verify(mockSignUpUseCase.call(any)).called(1);
      verify(mockProfilePictureRepository.uploadLocalProfilePicture(localImagePath)).called(1);
    });

    test('should validate signup parameters correctly', () async {
      // Arrange
      final mockUser = UserModel(
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        username: 'johndoe',
        profilePictureUrl: null,
      );

      when(mockSignUpUseCase.call(any))
          .thenAnswer((_) async => Result.success(mockUser));

      // Act
      signUpBloc.add(SignUpSubmitted(
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        username: 'johndoe',
        birthday: DateTime(1990, 1, 1),
        profilePicturePathOrUrl: null, // No profile picture
        notificationsEnabled: true,
      ));

      // Assert
      await expectLater(
        signUpBloc.stream,
        emitsInOrder([
          isA<SignUpLoading>(),
          isA<SignUpSuccess>(),
        ]),
      );

      // Verify the correct parameters were passed
      final captured = verify(mockSignUpUseCase.call(captureAny)).captured.single as SignUpParams;
      expect(captured.firstName, equals('John'));
      expect(captured.lastName, equals('Doe'));
      expect(captured.email, equals('<EMAIL>'));
      expect(captured.username, equals('johndoe'));
      expect(captured.profilePicturePathOrUrl, isNull);
      expect(captured.notificationsEnabled, isTrue);
    });
  });
}
