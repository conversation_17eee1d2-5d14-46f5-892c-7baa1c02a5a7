import 'dart:io';
import 'dart:typed_data';

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import 'package:hopen/provider/services/avif_processing_service.dart';
import 'package:hopen/provider/services/image_processing_service.dart';

void main() {
  group('AVIF Processing Integration Tests', () {
    late Directory tempDir;

    setUpAll(() async {
      // Create temporary directory for test files
      tempDir = await Directory.systemTemp.createTemp('avif_test_');
    });

    tearDownAll(() async {
      // Clean up temporary directory
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    test('should check AVIF support availability', () async {
      // Act
      final isSupported = await AvifProcessingService.isAvifSupported();

      // Assert
      expect(isSupported, isTrue, reason: 'FFmpeg should support AVIF encoding');
    });

    test('should get supported formats with AVIF-only', () async {
      // Act
      final formats = await EnhancedImageProcessor.getSupportedFormats();

      // Assert
      expect(formats, equals(['avif']), reason: 'Mobile apps should only support AVIF');
    });

    test('should create test image file', () async {
      // Arrange - Create a simple test image (PNG format)
      final testImagePath = path.join(tempDir.path, 'test_image.png');
      
      // Create a simple 100x100 red square PNG
      final testImageBytes = _createTestPngImage();
      await File(testImagePath).writeAsBytes(testImageBytes);

      // Assert
      expect(await File(testImagePath).exists(), isTrue);
      expect(await File(testImagePath).length(), greaterThan(0));
    });

    test('should process image with AVIF compression', () async {
      // Arrange
      final testImagePath = path.join(tempDir.path, 'test_image_for_avif.png');
      final testImageBytes = _createTestPngImage();
      await File(testImagePath).writeAsBytes(testImageBytes);

      const maxFileSize = 50 * 1024; // 50KB
      const initialQuality = 80;

      // Act
      final result = await EnhancedImageProcessor.processImage(
        inputPath: testImagePath,
        maxFileSizeBytes: maxFileSize,
        initialQuality: initialQuality,
        maxWidth: 640,
        maxHeight: 640,
      );

      // Assert
      expect(result.isSuccess, isTrue, reason: 'AVIF processing should succeed');
      expect(result.bestData, isNotNull);
      expect(result.bestData!.length, lessThanOrEqualTo(maxFileSize));
      expect(result.format, equals('avif'),
        reason: 'Should only use AVIF format for mobile apps');
      expect(result.compressionRatio, greaterThan(0.0), 
        reason: 'Should achieve some compression');
    });

    test('should handle image processing with size constraints', () async {
      // Arrange
      final testImagePath = path.join(tempDir.path, 'test_large_image.png');
      final largeImageBytes = _createTestPngImage(width: 800, height: 800);
      await File(testImagePath).writeAsBytes(largeImageBytes);

      const maxFileSize = 20 * 1024; // 20KB - very small
      const initialQuality = 90;

      // Act
      final result = await EnhancedImageProcessor.processImage(
        inputPath: testImagePath,
        maxFileSizeBytes: maxFileSize,
        initialQuality: initialQuality,
        maxWidth: 640,
        maxHeight: 640,
      );

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.bestData, isNotNull);
      expect(result.bestData!.length, lessThanOrEqualTo(maxFileSize));
      expect(result.sizeReductionPercentage, greaterThan(50.0),
        reason: 'Should achieve significant compression for small size limit');
    });

    test('should integrate with ImageProcessingService', () async {
      // Arrange
      final testImagePath = path.join(tempDir.path, 'test_integration.png');
      final testImageBytes = _createTestPngImage();
      await File(testImagePath).writeAsBytes(testImageBytes);

      const minResolution = 100;
      const maxResolution = 1440;
      const maxFileSize = 100 * 1024; // 100KB
      const compressionQuality = 85.0;

      // Act
      final result = await ImageProcessingService.processImageInIsolate(
        imagePath: testImagePath,
        minResolution: minResolution,
        maxResolution: maxResolution,
        maxFileSizeBytes: maxFileSize,
        compressionQuality: compressionQuality,
      );

      // Assert
      expect(result.isSuccess, isTrue, reason: 'Image processing should succeed');
      expect(result.processedBytes, isNotNull);
      expect(result.processedBytes!.length, lessThanOrEqualTo(maxFileSize));
    });

    test('should handle invalid image files gracefully', () async {
      // Arrange
      final invalidImagePath = path.join(tempDir.path, 'invalid.png');
      await File(invalidImagePath).writeAsString('This is not an image');

      // Act
      final result = await EnhancedImageProcessor.processImage(
        inputPath: invalidImagePath,
        maxFileSizeBytes: 100 * 1024,
        initialQuality: 80,
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.errorMessage, isNotNull);
      expect(result.errorMessage, contains('failed'));
    });

    test('should handle non-existent files gracefully', () async {
      // Arrange
      final nonExistentPath = path.join(tempDir.path, 'does_not_exist.png');

      // Act
      final result = await ImageProcessingService.processImageInIsolate(
        imagePath: nonExistentPath,
        minResolution: 640,
        maxResolution: 1440,
        maxFileSizeBytes: 100 * 1024,
        compressionQuality: 80.0,
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.errorMessage, isNotNull);
      expect(result.errorMessage, contains('not found'));
    });

    test('should validate image dimensions correctly', () async {
      // Arrange - Create a small image (below minimum resolution)
      final smallImagePath = path.join(tempDir.path, 'small_image.png');
      final smallImageBytes = _createTestPngImage(width: 50, height: 50);
      await File(smallImagePath).writeAsBytes(smallImageBytes);

      // Act
      final result = await ImageProcessingService.processImageInIsolate(
        imagePath: smallImagePath,
        minResolution: 640,
        maxResolution: 1440,
        maxFileSizeBytes: 100 * 1024,
        compressionQuality: 80.0,
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.errorMessage, contains('too small'));
      expect(result.errorMessage, contains('640'));
    });

    test('should detect image formats correctly', () async {
      // Arrange
      final testImagePath = path.join(tempDir.path, 'format_test.png');
      final testImageBytes = _createTestPngImage();
      await File(testImagePath).writeAsBytes(testImageBytes);

      // Act
      final result = await EnhancedImageProcessor.processImage(
        inputPath: testImagePath,
        maxFileSizeBytes: 100 * 1024,
        initialQuality: 80,
      );

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.format, isNotNull);
      expect(result.format, equals('avif'));
    });
  });
}

/// Create a simple test PNG image
Uint8List _createTestPngImage({int width = 100, int height = 100}) {
  // Simple PNG header and data for a solid color image
  // This is a minimal PNG implementation for testing
  final List<int> pngData = [
    // PNG signature
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
    
    // IHDR chunk
    0x00, 0x00, 0x00, 0x0D, // Length
    0x49, 0x48, 0x44, 0x52, // Type: IHDR
    
    // Width (4 bytes, big-endian)
    (width >> 24) & 0xFF, (width >> 16) & 0xFF, (width >> 8) & 0xFF, width & 0xFF,
    
    // Height (4 bytes, big-endian)
    (height >> 24) & 0xFF, (height >> 16) & 0xFF, (height >> 8) & 0xFF, height & 0xFF,
    
    0x08, // Bit depth: 8
    0x02, // Color type: RGB
    0x00, // Compression method
    0x00, // Filter method
    0x00, // Interlace method
    
    // CRC (placeholder - would need proper calculation for real PNG)
    0x9A, 0x76, 0x82, 0x70,
    
    // IDAT chunk (minimal data for red square)
    0x00, 0x00, 0x00, 0x0C, // Length
    0x49, 0x44, 0x41, 0x54, // Type: IDAT
    
    // Compressed data (zlib format with minimal red pixel data)
    0x78, 0x9C, 0x63, 0xF8, 0x0F, 0x00, 0x01, 0x01, 0x01, 0x00, 0x18, 0xDD,
    
    // CRC (placeholder)
    0x8D, 0xB4, 0x2C, 0x78,
    
    // IEND chunk
    0x00, 0x00, 0x00, 0x00, // Length
    0x49, 0x45, 0x4E, 0x44, // Type: IEND
    0xAE, 0x42, 0x60, 0x82, // CRC
  ];
  
  return Uint8List.fromList(pngData);
}
