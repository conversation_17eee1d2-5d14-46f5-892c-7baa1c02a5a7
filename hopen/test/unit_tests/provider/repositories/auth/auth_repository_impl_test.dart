// TODO: This test file needs to be updated to work with OryAuthService exclusively
// AuthRemoteDataSource has been removed in favor of using Ory Stack authentication
// Tests should mock OryAuthService methods instead of AuthRemoteDataSource

import 'package:flutter_test/flutter_test.dart';
import 'package:hopen/provider/repositories/auth/auth_repository_impl.dart';
import 'package:hopen/provider/services/auth/ory_auth_service.dart';
import 'package:hopen/statefulbusinesslogic/core/error/exceptions.dart';
import 'package:hopen/statefulbusinesslogic/core/error/result.dart';
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'auth_repository_impl_test.mocks.dart';

@GenerateMocks([OryAuthService])
void main() {
  late AuthRepositoryImpl repository;
  late MockOryAuthService mockOryAuthService;

  setUp(() {
    mockOryAuthService = MockOryAuthService();
    repository = AuthRepositoryImpl(
      oryAuthService: mockOryAuthService,
    );
  });

  final testUser = UserModel(
    id: 'test-user-id',
    email: '<EMAIL>',
    username: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    profilePictureUrl: 'https://example.com/profile.jpg',
    hasCompletedOnboarding: true,
  );

  group('login', () {
    test('should return a UserModel when login succeeds', () async {
      // TODO: Update test to use OryAuthService instead of AuthRemoteDataSource
      // This test needs to be rewritten to mock OryAuthService.signInWithEmail()
      // and return an OryAuthResponse with OryUser
      expect(true, isTrue); // Placeholder test
    });

    test('should return NetworkError when login throws ServerException', () async {
      // Arrange
      when(mockRemoteDataSource.login(
        email: anyNamed('email'),
        password: anyNamed('password'),
      )).thenThrow(const ServerException('Server error'));

      // Act
      final result = await repository.login(
        email: '<EMAIL>',
        password: 'password',
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.error, isA<NetworkError>());
      expect((result.error as NetworkError).message, equals('Server error'));
      verify(mockRemoteDataSource.login(
        email: '<EMAIL>',
        password: 'password',
      ));
    });

    test('should return NetworkError when login throws NetworkException', () async {
      // Arrange
      when(mockRemoteDataSource.login(
        email: anyNamed('email'),
        password: anyNamed('password'),
      )).thenThrow(const NetworkException('Network error'));

      // Act
      final result = await repository.login(
        email: '<EMAIL>',
        password: 'password',
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.error, isA<NetworkError>());
      expect((result.error as NetworkError).message, equals('Network error'));
      verify(mockRemoteDataSource.login(
        email: '<EMAIL>',
        password: 'password',
      ));
    });
  });

  group('loginWithGoogle', () {
    test('should return a UserModel when login with Google succeeds', () async {
      // Arrange
      when(mockRemoteDataSource.loginWithGoogle(any))
          .thenAnswer((_) async => testUser);

      // Act
      final result = await repository.loginWithGoogle();

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(testUser));
      verify(mockRemoteDataSource.loginWithGoogle('GOOGLE_ID_TOKEN'));
    });

    test('should return NetworkError when login with Google throws ServerException', () async {
      // Arrange
      when(mockRemoteDataSource.loginWithGoogle(any))
          .thenThrow(const ServerException('Server error'));

      // Act
      final result = await repository.loginWithGoogle();

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.error, isA<NetworkError>());
      expect((result.error as NetworkError).message, equals('Server error'));
      verify(mockRemoteDataSource.loginWithGoogle('GOOGLE_ID_TOKEN'));
    });
  });

  group('loginWithApple', () {
    test('should return a UserModel when login with Apple succeeds', () async {
      // Arrange
      when(mockRemoteDataSource.loginWithApple(any))
          .thenAnswer((_) async => testUser);

      // Act
      final result = await repository.loginWithApple();

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(testUser));
      verify(mockRemoteDataSource.loginWithApple('APPLE_ID_TOKEN'));
    });

    test('should return NetworkError when login with Apple throws ServerException', () async {
      // Arrange
      when(mockRemoteDataSource.loginWithApple(any))
          .thenThrow(const ServerException('Server error'));

      // Act
      final result = await repository.loginWithApple();

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.error, isA<NetworkError>());
      expect((result.error as NetworkError).message, equals('Server error'));
      verify(mockRemoteDataSource.loginWithApple('APPLE_ID_TOKEN'));
    });
  });

  group('signUp', () {
    test('should return a UserModel when sign up succeeds', () async {
      // Arrange
      when(mockRemoteDataSource.signUp(
        email: anyNamed('email'),
        password: anyNamed('password'),
        username: anyNamed('username'),
      )).thenAnswer((_) async => testUser);

      // Act
      final result = await repository.signUp(
        email: '<EMAIL>',
        password: 'password',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
      );

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(testUser));
      verify(mockRemoteDataSource.signUp(
        email: '<EMAIL>',
        password: 'password',
        username: 'testuser',
      ));
    });

    test('should return NetworkError when sign up throws ServerException', () async {
      // Arrange
      when(mockRemoteDataSource.signUp(
        email: anyNamed('email'),
        password: anyNamed('password'),
        username: anyNamed('username'),
      )).thenThrow(const ServerException('Server error'));

      // Act
      final result = await repository.signUp(
        email: '<EMAIL>',
        password: 'password',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
      );

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.error, isA<NetworkError>());
      expect((result.error as NetworkError).message, equals('Server error'));
      verify(mockRemoteDataSource.signUp(
        email: '<EMAIL>',
        password: 'password',
        username: 'testuser',
      ));
    });
  });

  group('getCurrentUser', () {
    test('should return cached user when available', () async {
      // Arrange
      // First login to cache user
      when(mockRemoteDataSource.login(
        email: anyNamed('email'),
        password: anyNamed('password'),
      )).thenAnswer((_) async => testUser);
      
      await repository.login(email: '<EMAIL>', password: 'password');

      // Act
      final result = await repository.getCurrentUser();

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(testUser));
      // Verify remote data source wasn't called again
      verifyNever(mockRemoteDataSource.getCurrentUser());
    });

    test('should fetch user from remote when cache is empty', () async {
      // Arrange
      when(mockRemoteDataSource.getCurrentUser())
          .thenAnswer((_) async => testUser);

      // Act
      final result = await repository.getCurrentUser();

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(testUser));
      verify(mockRemoteDataSource.getCurrentUser());
    });

    test('should return error when no user is available', () async {
      // Arrange
      when(mockRemoteDataSource.getCurrentUser())
          .thenAnswer((_) async => null);

      // Act
      final result = await repository.getCurrentUser();

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.error, isA<AuthenticationError>());
      expect((result.error as AuthenticationError).message, equals('No user logged in'));
      verify(mockRemoteDataSource.getCurrentUser());
    });
  });

  group('logout', () {
    test('should clear cached user and return true when logout succeeds', () async {
      // Arrange
      // First login to cache user
      when(mockRemoteDataSource.login(
        email: anyNamed('email'),
        password: anyNamed('password'),
      )).thenAnswer((_) async => testUser);
      
      await repository.login(email: '<EMAIL>', password: 'password');
      
      when(mockRemoteDataSource.logout())
          .thenAnswer((_) async => {});

      // Act
      final result = await repository.logout();

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, isTrue);
      verify(mockRemoteDataSource.logout());
      
      // Verify user is cleared from cache
      expect(repository.authenticatedUser, isNull);
      expect(repository.isAuthenticated, isFalse);
    });

    test('should return NetworkError when logout throws ServerException', () async {
      // Arrange
      when(mockRemoteDataSource.logout())
          .thenThrow(const ServerException('Server error'));

      // Act
      final result = await repository.logout();

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.error, isA<NetworkError>());
      expect((result.error as NetworkError).message, equals('Server error'));
      verify(mockRemoteDataSource.logout());
    });
  });

  group('updateOnboardingStatus', () {
    test('should return true when update succeeds', () async {
      // Arrange
      when(mockRemoteDataSource.updateOnboardingStatus(any))
          .thenAnswer((_) async => {});

      // Act
      final result = await repository.updateOnboardingStatus(true);

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.data, isTrue);
      verify(mockRemoteDataSource.updateOnboardingStatus(true));
    });

    test('should return NetworkError when update throws ServerException', () async {
      // Arrange
      when(mockRemoteDataSource.updateOnboardingStatus(any))
          .thenThrow(const ServerException('Server error'));

      // Act
      final result = await repository.updateOnboardingStatus(true);

      // Assert
      expect(result.isSuccess, isFalse);
      expect(result.error, isA<NetworkError>());
      expect((result.error as NetworkError).message, equals('Server error'));
      verify(mockRemoteDataSource.updateOnboardingStatus(true));
    });
  });

  group('User properties', () {
    test('authenticatedUser should return null initially', () {
      expect(repository.authenticatedUser, isNull);
    });

    test('isAuthenticated should return false initially', () {
      expect(repository.isAuthenticated, isFalse);
    });

    test('authenticatedUser should return user after login', () async {
      // Arrange
      when(mockRemoteDataSource.login(
        email: anyNamed('email'),
        password: anyNamed('password'),
      )).thenAnswer((_) async => testUser);
      
      // Act
      await repository.login(email: '<EMAIL>', password: 'password');

      // Assert
      expect(repository.authenticatedUser, equals(testUser));
      expect(repository.isAuthenticated, isTrue);
    });
  });
} 