import 'package:test/test.dart';
import 'package:hopen/provider/datasources/http_remote_datasource.dart';
import 'package:hopen/provider/models/api_models.dart';
import 'package:hopen/provider/repositories/user/user_repository_impl.dart';
import 'package:hopen/statefulbusinesslogic/core/error/result.dart';
import 'package:hopen/statefulbusinesslogic/core/models/bubble_membership_status.dart';

/// Lightweight fake implementation of [HttpRemoteDataSource] for unit tests.
class _TestRemoteDataSource implements HttpRemoteDataSource {
  _TestRemoteDataSource({
    required this.primarySucceeds,
    required this.fallbackSucceeds,
  });

  final bool primarySucceeds;
  final bool fallbackSucceeds;

  final _sampleUser = ApiUserProfile(
    id: 'user-123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    username: 'tester',
    profilePictureUrl: null,
    onlineStatus: 'online',
    bubbleStatus: BubbleMembershipStatus.noBubble.name,
  );

  // ──────────────────────────────────────────────────────────────────────────
  // Minimal methods needed for tests
  // ──────────────────────────────────────────────────────────────────────────
  @override
  Future<ApiUserProfile> getCurrentUserProfile() async {
    if (primarySucceeds) {
      return _sampleUser;
    }
    throw Exception('Primary endpoint failed');
  }

  @override
  Future<ApiUserProfile> getUserProfile(String userId) async {
    if (fallbackSucceeds) {
      return _sampleUser;
    }
    throw Exception('Fallback endpoint failed');
  }

  // ──────────────────────────────────────────────────────────────────────────
  // Unimplemented methods — not used in these tests
  // Each throws to signal accidental use during tests
  // ──────────────────────────────────────────────────────────────────────────
  @override
  noSuchMethod(Invocation invocation) =>
      throw UnimplementedError('Method ${invocation.memberName} not mocked.');
}

void main() {
  group('UserRepositoryImpl.getCurrentUserSafe()', () {
    test('returns Success when primary endpoint succeeds', () async {
      final remote = _TestRemoteDataSource(
        primarySucceeds: true,
        fallbackSucceeds: false,
      );
      final repo = UserRepositoryImpl(httpDataSource: remote);

      final result = await repo.getCurrentUserSafe();

      expect(result, isA<Success>());
      expect(result.isSuccess, isTrue);
    });

    test('returns Failure when both primary and fallback fail', () async {
      final remote = _TestRemoteDataSource(
        primarySucceeds: false,
        fallbackSucceeds: false,
      );
      final repo = UserRepositoryImpl(httpDataSource: remote);

      final result = await repo.getCurrentUserSafe();

      expect(result, isA<Failure>());
      expect(result.isFailure, isTrue);
    });

    test('performs fallback and returns Success when primary fails but fallback succeeds', () async {
      // First, allow primary to succeed once to cache _currentUserId.
      final remoteInitial = _TestRemoteDataSource(
        primarySucceeds: true,
        fallbackSucceeds: true,
      );
      final repo = UserRepositoryImpl(httpDataSource: remoteInitial);
      await repo.getCurrentUserSafe();

      // Now replace data source with failing primary but successful fallback
      final remoteFallback = _TestRemoteDataSource(
        primarySucceeds: false,
        fallbackSucceeds: true,
      );
      // We cannot change internal httpDataSource field; construct a new repo and set currentUserId.
      final repo2 = UserRepositoryImpl(httpDataSource: remoteFallback);
      repo2.setCurrentUserId('user-123');

      final result = await repo2.getCurrentUserSafe();
      expect(result, isA<Success>());
    });
  });
} 