import 'package:flutter_test/flutter_test.dart';
import 'package:hopen/statefulbusinesslogic/core/error/option.dart';
import 'package:hopen/statefulbusinesslogic/core/error/result.dart';

void main() {
  group('Option', () {
    test('Some should hold value correctly', () {
      const testValue = 'test value';
      final some = Option.some(testValue);

      expect(some.isSome, isTrue);
      expect(some.isNone, isFalse);
      expect(some.value, equals(testValue));
    });

    test('None should be empty', () {
      final none = Option<String>.none();

      expect(none.isSome, isFalse);
      expect(none.isNone, isTrue);
      expect(() => none.value, throwsA(isA<TypeError>()));
    });

    test('fromNullable should create Some for non-null values', () {
      final option = Option.fromNullable('test');
      expect(option.isSome, isTrue);
      expect(option.value, equals('test'));
    });

    test('fromNullable should create None for null values', () {
      final option = Option.fromNullable(null);
      expect(option.isNone, isTrue);
    });

    test('fold should call onSome for Some', () {
      final some = Option.some('test');

      final result = some.fold(
        onSome: (value) => 'Some: $value',
        onNone: () => 'None',
      );

      expect(result, equals('Some: test'));
    });

    test('fold should call onNone for None', () {
      final none = Option<String>.none();

      final result = none.fold(
        onSome: (value) => 'Some: $value',
        onNone: () => 'None',
      );

      expect(result, equals('None'));
    });

    test('map should transform Some value', () {
      final some = Option.some('test');
      final mapped = some.map((value) => value.length);

      expect(mapped.isSome, isTrue);
      expect(mapped.value, equals(4));
    });

    test('map should preserve None', () {
      final none = Option<String>.none();
      final mapped = none.map((value) => value.length);

      expect(mapped.isNone, isTrue);
    });

    test('flatMap should chain operations on Some', () {
      final some = Option.some('test');
      final chained = some.flatMap((value) => 
        value.isNotEmpty ? Option.some(value.length) : Option.none());

      expect(chained.isSome, isTrue);
      expect(chained.value, equals(4));
    });

    test('flatMap should preserve None', () {
      final none = Option<String>.none();
      final chained = none.flatMap((value) => Option.some(value.length));

      expect(chained.isNone, isTrue);
    });

    test('getOrElse should return value for Some', () {
      final some = Option.some('test');
      final result = some.getOrElse('default');

      expect(result, equals('test'));
    });

    test('getOrElse should return default for None', () {
      final none = Option<String>.none();
      final result = none.getOrElse('default');

      expect(result, equals('default'));
    });

    test('getOrElseGet should return value for Some', () {
      final some = Option.some('test');
      final result = some.getOrElseGet(() => 'computed');

      expect(result, equals('test'));
    });

    test('getOrElseGet should compute default for None', () {
      final none = Option<String>.none();
      final result = none.getOrElseGet(() => 'computed');

      expect(result, equals('computed'));
    });

    test('filter should preserve Some if predicate is true', () {
      final some = Option.some('test');
      final filtered = some.filter((value) => value.length > 2);

      expect(filtered.isSome, isTrue);
      expect(filtered.value, equals('test'));
    });

    test('filter should return None if predicate is false', () {
      final some = Option.some('test');
      final filtered = some.filter((value) => value.length > 10);

      expect(filtered.isNone, isTrue);
    });

    test('filter should preserve None', () {
      final none = Option<String>.none();
      final filtered = none.filter((value) => value.isNotEmpty);

      expect(filtered.isNone, isTrue);
    });

    test('toResult should convert Some to Success', () {
      final some = Option.some('test');
      final result = some.toResult(() => ValidationError(message: 'missing'));

      expect(result.isSuccess, isTrue);
      expect(result.data, equals('test'));
    });

    test('toResult should convert None to Failure', () {
      final none = Option<String>.none();
      final result = none.toResult(() => ValidationError(message: 'missing'));

      expect(result.isFailure, isTrue);
      expect(result.error, isA<ValidationError>());
    });

    test('toNullable should convert Some to value', () {
      final some = Option.some('test');
      final nullable = some.toNullable();

      expect(nullable, equals('test'));
    });

    test('toNullable should convert None to null', () {
      final none = Option<String>.none();
      final nullable = none.toNullable();

      expect(nullable, isNull);
    });

    test('tap should execute action for Some', () {
      final some = Option.some('test');
      String? sideEffect;

      final result = some.tap((value) => sideEffect = value);

      expect(result.isSome, isTrue);
      expect(result.value, equals('test'));
      expect(sideEffect, equals('test'));
    });

    test('tap should not execute action for None', () {
      final none = Option<String>.none();
      String? sideEffect;

      final result = none.tap((value) => sideEffect = value);

      expect(result.isNone, isTrue);
      expect(sideEffect, isNull);
    });

    test('tapNone should execute action for None', () {
      final none = Option<String>.none();
      bool executed = false;

      final result = none.tapNone(() => executed = true);

      expect(result.isNone, isTrue);
      expect(executed, isTrue);
    });

    test('tapNone should not execute action for Some', () {
      final some = Option.some('test');
      bool executed = false;

      final result = some.tapNone(() => executed = true);

      expect(result.isSome, isTrue);
      expect(executed, isFalse);
    });
  });

  group('Option Extensions', () {
    test('zip should combine two Some values', () {
      final option1 = Option.some('hello');
      final option2 = Option.some(42);

      final zipped = option1.zip(option2);

      expect(zipped.isSome, isTrue);
      expect(zipped.value, equals(('hello', 42)));
    });

    test('zip should return None if either is None', () {
      final option1 = Option.some('hello');
      final option2 = Option<int>.none();

      final zipped = option1.zip(option2);

      expect(zipped.isNone, isTrue);
    });

    test('zipWith should combine values with function', () {
      final option1 = Option.some(5);
      final option2 = Option.some(10);

      final combined = option1.zipWith(option2, (a, b) => a + b);

      expect(combined.isSome, isTrue);
      expect(combined.value, equals(15));
    });
  });

  group('OptionUtils', () {
    test('tryCatch should return Some for successful operation', () {
      final option = OptionUtils.tryCatch(() => 'success');

      expect(option.isSome, isTrue);
      expect(option.value, equals('success'));
    });

    test('tryCatch should return None for throwing operation', () {
      final option = OptionUtils.tryCatch<String>(() => throw Exception('error'));

      expect(option.isNone, isTrue);
    });

    test('sequence should combine all Some values', () {
      final options = [
        Option.some(1),
        Option.some(2),
        Option.some(3),
      ];

      final combined = OptionUtils.sequence(options);

      expect(combined.isSome, isTrue);
      expect(combined.value, equals([1, 2, 3]));
    });

    test('sequence should return None if any is None', () {
      final options = [
        Option.some(1),
        Option<int>.none(),
        Option.some(3),
      ];

      final combined = OptionUtils.sequence(options);

      expect(combined.isNone, isTrue);
    });

    test('firstSome should return first Some value', () {
      final options = [
        Option<String>.none(),
        Option.some('first'),
        Option.some('second'),
      ];

      final first = OptionUtils.firstSome(options);

      expect(first.isSome, isTrue);
      expect(first.value, equals('first'));
    });

    test('firstSome should return None if all are None', () {
      final options = [
        Option<String>.none(),
        Option<String>.none(),
        Option<String>.none(),
      ];

      final first = OptionUtils.firstSome(options);

      expect(first.isNone, isTrue);
    });
  });

  group('Option Do Notation', () {
    test('should chain successful operations', () {
      final result = OptionDo.Do(($) {
        final a = $(Option.some(5));
        final b = $(Option.some(10));
        return a + b;
      });

      expect(result.isSome, isTrue);
      expect(result.value, equals(15));
    });

    test('should short-circuit on first None', () {
      final result = OptionDo.Do(($) {
        final a = $(Option.some(5));
        final b = $(Option<int>.none());
        final c = $(Option.some(10)); // Should not be executed
        return a + b + c;
      });

      expect(result.isNone, isTrue);
    });
  });

  group('Result-Option Conversion', () {
    test('Result.toOption should convert Success to Some', () {
      final result = Result.success('test');
      final option = result.toOption();

      expect(option.isSome, isTrue);
      expect(option.value, equals('test'));
    });

    test('Result.toOption should convert Failure to None', () {
      final result = Result<String>.failure(NetworkError(message: 'error'));
      final option = result.toOption();

      expect(option.isNone, isTrue);
    });

    test('Option.toResultWith should convert Some to Success', () {
      final option = Option.some('test');
      final result = option.toResultWith(ValidationError(message: 'error'));

      expect(result.isSuccess, isTrue);
      expect(result.data, equals('test'));
    });

    test('Option.toResultWith should convert None to Failure', () {
      final option = Option<String>.none();
      final result = option.toResultWith(ValidationError(message: 'error'));

      expect(result.isFailure, isTrue);
      expect(result.error, isA<ValidationError>());
    });
  });
}
