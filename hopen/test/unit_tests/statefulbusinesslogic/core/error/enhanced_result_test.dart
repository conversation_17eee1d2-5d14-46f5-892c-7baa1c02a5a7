import 'package:flutter_test/flutter_test.dart';
import 'package:hopen/statefulbusinesslogic/core/error/result.dart';
import 'package:hopen/statefulbusinesslogic/core/error/do_notation.dart';
import 'package:hopen/statefulbusinesslogic/core/error/option.dart';
import 'package:hopen/statefulbusinesslogic/core/error/base_error.dart';

void main() {
  group('Enhanced Result Extensions', () {
    test('mapError should transform error type', () {
      final failure = Result<String>.failure(NetworkError(message: 'network error'));
      
      final mapped = failure.mapError((error) => 
        ValidationError(message: 'Mapped: ${error.userMessage}'));
      
      expect(mapped.isFailure, isTrue);
      expect(mapped.error, isA<ValidationError>());
      expect(mapped.error.userMessage, contains('Mapped:'));
    });

    test('recover should provide fallback value', () {
      final failure = Result<String>.failure(NetworkError(message: 'error'));
      
      final recovered = failure.recover((error) => 'fallback');
      
      expect(recovered.isSuccess, isTrue);
      expect(recovered.data, equals('fallback'));
    });

    test('recoverWith should provide fallback Result', () {
      final failure = Result<String>.failure(NetworkError(message: 'error'));
      
      final recovered = failure.recoverWith((error) => Result.success('recovered'));
      
      expect(recovered.isSuccess, isTrue);
      expect(recovered.data, equals('recovered'));
    });

    test('filter should validate success values', () {
      final success = Result.success('hello');
      
      final filtered = success.filter(
        (data) => data.length > 10,
        () => ValidationError(message: 'Too short'),
      );
      
      expect(filtered.isFailure, isTrue);
      expect(filtered.error, isA<ValidationError>());
    });

    test('tap should execute side effect without changing result', () {
      final success = Result.success('test');
      String? sideEffect;
      
      final result = success.tap((data) => sideEffect = data);
      
      expect(result.isSuccess, isTrue);
      expect(result.data, equals('test'));
      expect(sideEffect, equals('test'));
    });

    test('tapError should execute side effect on error', () {
      final failure = Result<String>.failure(NetworkError(message: 'error'));
      BaseError? capturedError;
      
      final result = failure.tapError((error) => capturedError = error);
      
      expect(result.isFailure, isTrue);
      expect(capturedError, isA<NetworkError>());
    });
  });

  group('TaskResult Extensions', () {
    test('flatMapAsync should chain async operations', () async {
      final taskResult = Future.value(Result.success(5));
      
      final chained = taskResult.flatMapAsync((value) async => 
        Result.success(value * 2));
      
      final result = await chained;
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(10));
    });

    test('mapAsync should transform value asynchronously', () async {
      final taskResult = Future.value(Result.success('hello'));
      
      final mapped = taskResult.mapAsync((value) async => value.toUpperCase());
      
      final result = await mapped;
      expect(result.isSuccess, isTrue);
      expect(result.data, equals('HELLO'));
    });

    test('timeout should fail after specified duration', () async {
      final slowTask = Future.delayed(Duration(seconds: 2), () => Result.success('slow'));

      try {
        final result = await slowTask.timeout(Duration(milliseconds: 100));
        expect(result.isFailure, isTrue);
        expect(result.error, isA<NetworkError>());
        expect(result.error.userMessage, contains('timed out'));
      } catch (e) {
        // The timeout implementation might throw TimeoutException
        expect(e, isA<TimeoutException>());
      }
    });
  });

  group('ResultUtils', () {
    test('sequence should combine multiple Results', () {
      final results = [
        Result.success(1),
        Result.success(2),
        Result.success(3),
      ];
      
      final combined = ResultUtils.sequence(results);
      
      expect(combined.isSuccess, isTrue);
      expect(combined.data, equals([1, 2, 3]));
    });

    test('sequence should fail if any Result fails', () {
      final results = [
        Result.success(1),
        Result<int>.failure(NetworkError(message: 'error')),
        Result.success(3),
      ];
      
      final combined = ResultUtils.sequence(results);
      
      expect(combined.isFailure, isTrue);
      expect(combined.error, isA<NetworkError>());
    });

    test('sequenceAsync should combine multiple async Results', () async {
      final tasks = [
        Future.value(Result.success(1)),
        Future.value(Result.success(2)),
        Future.value(Result.success(3)),
      ];
      
      final combined = await ResultUtils.sequenceAsync(tasks);
      
      expect(combined.isSuccess, isTrue);
      expect(combined.data, equals([1, 2, 3]));
    });
  });

  group('Do Notation', () {
    test('should chain successful operations', () {
      final result = ResultDo.Do(($) {
        final a = $(Result.success(5));
        final b = $(Result.success(10));
        final c = $(Result.success(a + b));
        return c * 2;
      });

      expect(result.isSuccess, isTrue);
      expect(result.data, equals(30));
    });

    test('should short-circuit on first failure', () {
      final result = ResultDo.Do(($) {
        final a = $(Result.success(5));
        final b = $(Result<int>.failure(NetworkError(message: 'error')));
        final c = $(Result.success(10)); // Should not be executed
        return a + b + c;
      });

      expect(result.isFailure, isTrue);
      expect(result.error, isA<NetworkError>());
    });

    test('async Do notation should work with TaskResult', () async {
      final result = await TaskResultDo.Do(($) async {
        final a = await $(Future.value(Result.success(5)));
        final b = await $(Future.value(Result.success(10)));
        return a + b;
      });

      expect(result.isSuccess, isTrue);
      expect(result.data, equals(15));
    });
  });

  group('Validation Helpers', () {
    test('notNull should validate non-null values', () {
      final result = Validate.notNull('test', () => ValidationError(message: 'null'));
      expect(result.isSuccess, isTrue);
      expect(result.data, equals('test'));
    });

    test('notNull should fail for null values', () {
      final result = Validate.notNull(null, () => ValidationError(message: 'null'));
      expect(result.isFailure, isTrue);
      expect(result.error, isA<ValidationError>());
    });

    test('notEmpty should validate non-empty strings', () {
      final result = Validate.notEmpty('test', () => ValidationError(message: 'empty'));
      expect(result.isSuccess, isTrue);
      expect(result.data, equals('test'));
    });

    test('notEmpty should fail for empty strings', () {
      final result = Validate.notEmpty('', () => ValidationError(message: 'empty'));
      expect(result.isFailure, isTrue);
    });

    test('that should validate with custom predicate', () {
      final result = Validate.that(
        10,
        (value) => value > 5,
        () => ValidationError(message: 'too small'),
      );
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(10));
    });

    test('all should validate multiple predicates', () {
      final result = Validate.all<int>(
        10,
        [(value) => value > 5, (value) => value < 20],
        () => ValidationError(message: 'invalid'),
      );
      expect(result.isSuccess, isTrue);
      expect(result.data, equals(10));
    });
  });

  group('Conditional Execution', () {
    test('when should execute operation if condition is true', () {
      final result = ResultIf.when(
        true,
        () => Result.success('executed'),
        () => 'not executed',
      );
      expect(result.isSuccess, isTrue);
      expect(result.data, equals('executed'));
    });

    test('when should return default if condition is false', () {
      final result = ResultIf.when(
        false,
        () => Result.success('executed'),
        () => 'default',
      );
      expect(result.isSuccess, isTrue);
      expect(result.data, equals('default'));
    });

    test('unless should execute operation if condition is false', () {
      final result = ResultIf.unless(
        false,
        () => Result.success('executed'),
        () => 'not executed',
      );
      expect(result.isSuccess, isTrue);
      expect(result.data, equals('executed'));
    });
  });
}
