# Contact, <PERSON><PERSON>ble and Friends Requests System

## Overview

Hopen implements a comprehensive request system that manages the social connections between users. This system handles **contact requests**, **bubble lifecycle requests** (start, invite, join, kick-out), and **auto-generated friendship requests** after a bubble expires. Each request has its own dedicated dialog widget and follows a specific flow in the application.

This document describes the different types of requests, their associated dialog widgets, and the user flows for each request type.

## Request Types and Flows

### 1. Contact Request Flow (manual)

- **Trigger**: A user (stranger with no existing relationship) sends a contact request to the current user
- **Dialog Widget**: `ContactRequestDialog`
- **Location**: `lib/presentation/widgets/requests/contact_request_dialog.dart`
- **Flow**:
  1. The contact request notification is received
  2. The `ContactRequestDialog` is shown to the current user
  3. If accepted, the two users become contacts
  4. If declined, no relationship is established

### 2. Bubble Start Request Flow

- **Trigger**: A contact (not in a bubble) sends a request to start a bubble with the current user (who is also not in a bubble)
- **Dialog Widget**: `BubbleStartRequestDialog`
- **Location**: `lib/presentation/widgets/requests/bubble_start_request_dialog.dart`
- **Flow**:
  1. The bubble start request notification is received
  2. The `BubbleStartRequestDialog` is shown to the current user
  3. If accepted, a new bubble is created with both users as members
  4. The bubble countdown begins
  5. If declined, no bubble is created

### 3. Bubble Invite Request Flow

- **Trigger**: A contact (already in a non-full bubble) invites the current user (not in a bubble) to join their bubble
- **Dialog Widgets**:
  - `BubbleInviteRequestDialog` (for the invitee)
  - `BubbleProposeRequestDialog` (for existing bubble members)
- **Locations**:
  - `lib/presentation/widgets/requests/bubble_invite_request_dialog.dart`
  - `lib/presentation/widgets/requests/bubble_propose_request_dialog.dart`
- **Flow**:
  1. The bubble invite request notification is received
  2. The `BubbleInviteRequestDialog` is shown to the current user
  3. If accepted by the current user, the `BubbleProposeRequestDialog` is shown to all other members of the bubble
  4. All existing bubble members must accept the proposal
  5. If all accept, the current user joins the bubble
  6. If any member declines, the current user does not join the bubble

### 4. Bubble Join Request Flow

- **Trigger**: A contact (not in a bubble) requests to join the current user's bubble (which is not full)
- **Dialog Widgets**:
  - `BubbleJoinRequestDialog` (for the bubble member receiving the request)
  - `BubbleProposeRequestDialog` (for other bubble members)
- **Locations**:
  - `lib/presentation/widgets/requests/bubble_join_request_dialog.dart`
  - `lib/presentation/widgets/requests/bubble_propose_request_dialog.dart`
- **Flow**:
  1. The bubble join request notification is received
  2. The `BubbleJoinRequestDialog` is shown to the current user
  3. If accepted by the current user, the `BubbleProposeRequestDialog` is shown to all other members of the bubble
  4. All existing bubble members must accept the proposal
  5. If all accept, the contact joins the bubble
  6. If any member declines, the contact does not join the bubble

### 5. Bubble Kickout Request Flow

- **Trigger**: A bubble member requests to kick out another member from the bubble
- **Dialog Widget**: `BubbleKickoutRequestDialog`
- **Location**: `lib/presentation/widgets/requests/bubble_kickout_request_dialog.dart`
- **Access Method**: Via unified profile page → three-dot menu → "Kickout from bubble"
- **Flow**:
  1. A bubble member goes to another member's profile page
  2. They select "Kickout from bubble" from the options menu
  3. The `BubbleKickoutRequestDialog` is shown to all other bubble members (except the target)
  4. All voting members must accept the kickout request
  5. If all accept, the target member is removed from the bubble
  6. If any member declines, the target member remains in the bubble
- **Key Features**:
  - **Unanimous approval required**: All bubble members (except target) must agree
  - **Target exclusion**: The member being kicked out doesn't receive the dialog
  - **Red accept button**: Emphasizes the serious nature of the action
  - **Confirmation dialogs**: Additional safeguards before final action
  - **Same UI consistency**: Follows the design pattern of other request dialogs

## Implementation Details

### BubbleProposeRequestDialog

- **Purpose**: Shown to all bubble members when a new member is proposed to join the bubble
- **Location**: `lib/presentation/widgets/requests/bubble_propose_request_dialog.dart`
- **Key Components**:
  - Title: "New bubbler proposal"
  - Message: "[ProposerName] wants to add [ProposedMemberName] to [BubbleName]"
  - Profile picture of the proposed member
  - Accept/Decline buttons
  - Success message: "Proposal accepted"
- **Integration**:
  - Used by both the Bubble Invite Request flow and the Bubble Join Request flow
  - Triggered automatically when a bubble member accepts an invite or join request
  - All bubble members must approve for the new member to join

### BubbleKickoutRequestDialog

- **Purpose**: Shown to all bubble members when another member requests to kick out a target member
- **Location**: `lib/presentation/widgets/requests/bubble_kickout_request_dialog.dart`
- **Key Components**:
  - Title: "Kickout request"
  - Message: "[RequesterName] wants to remove [TargetMemberName] from [BubbleName]"
  - Profile picture of the target member
  - Accept (red)/Decline (gray) buttons
  - Success message: "Kickout request accepted"
- **Integration**:
  - Accessed via unified profile page options menu
  - Triggered when "Kickout from bubble" is selected
  - All bubble members (except target) must approve for removal

### Service Integration

- **BubbleProposeRequestService**: Handles showing the dialog and notifying bubble members
- **Location**: `lib/provider/services/bubble_propose_request_service.dart`
- **Key Methods**:
  - `showProposeRequestDialog`: Shows the dialog to a specific user
  - `notifyBubbleMembers`: Sends notifications to all bubble members
- **Integration with BLoCs**:
  - `BubbleJoinRequestBloc`: Triggers the propose request flow when a join request is accepted
  - `BubbleInviteRequestBloc`: Triggers the propose request flow when an invite is accepted
  - `BubbleKickoutRequestBloc`: Handles kickout request voting and state management

### State Management Architecture

All request dialogs follow a consistent BLoC pattern:

#### Events
- `Load*RequestEvent`: Initialize dialog with request data
- `Accept*RequestEvent`: Approve the request
- `Decline*RequestEvent`: Reject the request  
- `Reset*RequestEvent`: Clear dialog state

#### States
- `*RequestStatus.initial`: Dialog not yet loaded
- `*RequestStatus.loading`: Fetching request data
- `*RequestStatus.loaded`: Dialog ready for interaction
- `*RequestStatus.accepting`: Processing acceptance
- `*RequestStatus.accepted`: Successfully approved
- `*RequestStatus.declining`: Processing rejection
- `*RequestStatus.declined`: Successfully rejected
- `*RequestStatus.error`: Error occurred

## User Experience Guidelines

### Visual Consistency
- All dialogs use the same animated gradient title with glow effects
- Profile pictures are prominently displayed for context
- Color-coded buttons indicate action severity:
  - **Blue**: Standard positive actions (Accept for proposals/invites)
  - **Red**: Serious/destructive actions (Kickout, Block, Report)
  - **Gray**: Neutral/decline actions

### Interaction Patterns
- **Non-dismissible dialogs**: Require explicit user action
- **Confirmation steps**: Additional safeguards for destructive actions
- **Loading states**: Visual feedback during processing
- **Success feedback**: Clear confirmation when actions complete
- **Auto-dismissal**: Dialogs close automatically after successful actions

### Error Handling
- **Network errors**: Retry mechanisms and user-friendly messages
- **Validation errors**: Clear feedback on invalid actions
- **State persistence**: Maintain dialog state across interruptions
- **Graceful degradation**: Fallback behavior when services unavailable

# Bubble Completion and Friendship Auto-Generation

## 1. Overview

When a bubble reaches its expiry time, the backend archives it and publishes `events.bubble.expired` on NATS JetStream. The **friendship** micro-service consumes this event and automatically creates **bidirectional friend requests** for every pair of former bubble members. The mobile client receives these requests via MQTT (`friend_requests/{userId}`), and users simply accept or decline — there is **no manual selection of friends**.

## 2. Backend Integration (Go micro-services)

### Contact Requests
```http
POST   /api/v1/contact/requests              # send contact request
GET    /api/v1/contact/requests/sent         # list sent
GET    /api/v1/contact/requests/received     # list received
POST   /api/v1/contact/requests/:id/accept   # accept
POST   /api/v1/contact/requests/:id/decline  # decline
```

### Bubble Membership Requests
```http
POST /api/v1/bubble/start-request            # start bubble with a contact
POST /api/v1/bubble/:id/invite               # invite user to bubble
POST /api/v1/bubble/:id/join                 # request to join bubble
POST /api/v1/bubble/:id/kickout              # kick out member
POST /api/v1/bubble/:id/requests/:requestId/accept   # accept bubble-specific request
POST /api/v1/bubble/:id/requests/:requestId/decline  # decline bubble-specific request
POST /api/v1/bubble/requests/:requestId/accept       # accept any request type
POST /api/v1/bubble/requests/:requestId/decline      # decline any request type
```

### Friendship Requests (auto-generated)
```http
GET    /api/v1/friendship/requests           # list pending auto-generated requests
POST   /api/v1/friendship/requests/:id/accept   # accept
POST   /api/v1/friendship/requests/:id/decline  # decline
GET    /api/v1/friendship/friends            # list friends
```

> **Note**  Users cannot manually create friend requests. All friendship requests originate from bubble expiration events and therefore always include a `source_bubble_id`.

---

## 3. Data Model Updates (ArangoDB)

```javascript
// friend_requests collection (edge)
{
  _key: "friend_request_id",
  requester_id: "user123",
  recipient_id: "user456",
  status: "pending|accepted|declined",
  created_at: "2024-01-01T00:00:00Z",
  source_bubble_id: "bubble789",
  auto_generated: true
}
```

---

## 4. Frontend Implementation

### 4.1. Dialog Widget Architecture

All request dialogs inherit common patterns:

```dart
abstract class BaseRequestDialog extends StatefulWidget {
  // Common properties
  final String requestId;
  final DateTime requestTimestamp;
  
  // Common methods
  static Future<bool?> show(BuildContext context, {/* params */});
  
  @protected
  Widget buildDialogContent(BuildContext context);
  @protected
  void handleAccept(BuildContext context);
  @protected
  void handleDecline(BuildContext context);
}
```

### 4.2. BLoC Integration

Each request type has its own BLoC following the same pattern:

```dart
// Events
abstract class *RequestEvent extends Equatable {}
class Load*RequestEvent extends *RequestEvent {}
class Accept*RequestEvent extends *RequestEvent {}
class Decline*RequestEvent extends *RequestEvent {}
class Reset*RequestEvent extends *RequestEvent {}

// States  
enum *RequestStatus { initial, loading, loaded, accepting, accepted, declining, declined, error }
class *RequestState extends Equatable {
  final *RequestStatus status;
  final String? errorMessage;
  // Request-specific data fields
}

// BLoC
class *RequestBloc extends Bloc<*RequestEvent, *RequestState> {
  // Repository integration for API calls
  // Event handlers following consistent patterns
}
```

### 4.3. Navigation Integration

Request dialogs are integrated with the app's navigation system:

```dart
// Unified Profile Page menu integration
PopupMenuButton<String>(
  onSelected: (String result) {
    switch (result) {
      case 'kickout':
        if (isBubbler) _showKickoutDialog();
        break;
      case 'invite':
        if (canInviteToBubble) _showInviteDialog();
        break;
      // Other menu options...
    }
  },
  itemBuilder: (context) => _buildMenuItems(context),
)

// Dialog trigger methods
void _showKickoutDialog() {
  BubbleKickoutRequestDialog.show(
    context,
    targetMemberId: widget.profileUserId,
    // Other required parameters...
  );
}
```

## 5. Testing Strategy

### 5.1. Unit Tests
- BLoC event handling and state transitions
- Request validation logic
- Error handling scenarios
- API integration mocking

### 5.2. Widget Tests
- Dialog rendering and layout
- Button interactions and states
- Loading and error state display
- Animation and transition behavior

### 5.3. Integration Tests
- End-to-end request flows
- Multi-user scenarios and voting
- Real-time notification handling
- Database consistency checks

### 5.4. Performance Tests
- Large bubble scenarios (max members)
- Concurrent request handling
- Network error recovery
- Memory usage during long-running dialogs

## 6. Security Considerations

### 6.1. Authorization
- Verify user permissions before showing dialogs
- Validate bubble membership for kickout requests
- Rate limiting for request creation
- Audit logging for all request actions

### 6.2. Privacy
- Hide sensitive information from unauthorized users
- Exclude targets from kickout notification distribution
- Secure storage of request data
- GDPR compliance for data retention

### 6.3. Abuse Prevention
- Spam detection for excessive requests
- Cooldown periods between similar requests
- Community reporting for malicious behavior
- Automatic moderation triggers

## 7. Future Enhancements

### 7.1. Advanced Features
- **Request expiration**: Auto-decline after time limits
- **Partial voting**: Handle offline member scenarios
- **Request analytics**: Track success rates and patterns
- **Custom reasons**: Allow explanatory text for requests

### 7.2. User Experience
- **Batch operations**: Multiple selections in single dialog
- **Smart suggestions**: Recommend likely friend selections
- **Notification grouping**: Combine similar request types
- **Accessibility**: Screen reader and keyboard navigation support

### 7.3. Moderation Tools
- **Admin overrides**: Moderator intervention capabilities
- **Appeal processes**: Contest unfair kickouts
- **Automated enforcement**: Rule-based request handling
- **Community guidelines**: Clear policy communication

This comprehensive system provides a robust foundation for managing all social interactions within the Hopen application, ensuring consistency, security, and user satisfaction across all request types.
