# Profile Picture Requirements

## Image Specifications

### Minimum Requirements
- **Minimum Resolution**: 640x640 pixels
- **Aspect Ratio**: 1:1 (square)
- **Format**: JPEG, PNG, or WebP

### Maximum Storage
- **Maximum Resolution**: 1440x1440 pixels
- **File Size**: Recommended maximum 3MB for optimal performance
- **Compression**: Automatic compression applied for storage efficiency

### Processing Pipeline

1. **Upload Validation**:
   - Check minimum resolution (640x640)
   - Validate aspect ratio (must be square)
   - Verify file format compatibility

2. **Image Processing**:
   - Resize to 1440x1440 if larger
   - Apply compression for optimal file size (90%)
   - Generate thumbnail versions if needed

3. **Storage**:
   - Store in MinIO S3-compatible storage
   - Generate presigned URLs for secure access
   - Implement CDN caching for performance

### Implementation Notes

- Profile pictures are stored in MinIO object storage
- Automatic resizing and compression handled by backend
- Multiple resolution variants may be generated for different UI contexts
- Secure access via presigned URLs with expiration
- Fallback to user initials if no profile picture is available

### UI Display Guidelines

- Always use CircleAvatar for profile picture display
- Implement proper loading states and error handling
- Show user initials as fallback when image is unavailable
- Maintain consistent sizing across different UI components 