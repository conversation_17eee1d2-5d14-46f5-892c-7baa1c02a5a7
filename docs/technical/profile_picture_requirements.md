# Profile Picture Requirements

## Image Specifications

### Minimum Requirements
- **Minimum Resolution**: 640x640 pixels
- **Aspect Ratio**: 1:1 (square)
- **Input Formats**: JPEG, PNG, WebP, or AVIF
- **Output Format**: AVIF (primary), WebP (fallback), JPEG (legacy fallback)

### Maximum Storage
- **Maximum Resolution**: 1440x1440 pixels
- **File Size**: Recommended maximum 2MB for optimal performance
- **Compression**: Automatic AVIF compression with intelligent fallbacks for compatibility

### Processing Pipeline

1. **Upload Validation**:
   - Check minimum resolution (640x640)
   - Validate aspect ratio (must be square)
   - Verify file format compatibility

2. **Image Processing**:
   - Resize to 1440x1440 if larger
   - Convert to AVIF format with intelligent quality optimization
   - Generate WebP fallback for older browsers
   - Generate JPEG fallback for legacy compatibility
   - Create thumbnail versions (300x300) in AVIF/WebP/JPEG

3. **Storage**:
   - Store in MinIO S3-compatible storage
   - Generate presigned URLs for secure access
   - Implement CDN caching for performance

### Implementation Notes

- Profile pictures are stored in MinIO object storage
- Automatic resizing and compression handled by backend
- Multiple resolution variants may be generated for different UI contexts
- Secure access via presigned URLs with expiration
- Fallback to user initials if no profile picture is available

### UI Display Guidelines

- Always use Superellipse for profile picture display
- Implement proper loading states and error handling
- Show user initials as fallback when image is unavailable
- Maintain consistent sizing across different UI components 