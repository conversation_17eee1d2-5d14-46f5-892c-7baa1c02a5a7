package auth

import (
	"bytes"
	"context"
	"crypto/rand"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/ory"
)

// Service handles authentication operations
type Service struct {
	logger    *zap.Logger
	config    *config.Config
	db        *database.PostgreSQLClient
	oryClient *ory.Client
}

// Dependencies holds the dependencies for the auth service
type Dependencies struct {
	Logger    *zap.Logger
	Config    *config.Config
	DB        *database.PostgreSQLClient
	OryClient *ory.Client
}

// New creates a new auth service instance
func New(deps *Dependencies) *Service {
	return &Service{
		logger:    deps.Logger,
		config:    deps.Config,
		db:        deps.DB,
		oryClient: deps.OryClient,
	}
}

// RegisterRoutes registers the auth service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.POST("/register", s.register)
	router.POST("/login", s.login)
	router.POST("/logout", s.properAuthMiddleware(), s.logout) // ✅ Added auth middleware
	// Refresh endpoint removed - Kratos handles session lifecycle automatically
	router.GET("/profile", s.properAuthMiddleware(), s.getProfile)

	// MQTT authentication endpoint for EMQX
	router.POST("/mqtt", s.validateMQTTAuth)
	router.PUT("/profile", s.properAuthMiddleware(), s.updateProfile)
	router.POST("/verify-email", s.verifyEmail)
	router.POST("/reset-password", s.resetPassword)
	router.POST("/change-password", s.properAuthMiddleware(), s.changePassword)
}

// RegisterRequest represents a user registration request
type RegisterRequest struct {
	Username    string `json:"username" binding:"required,min=3,max=50"`
	Email       string `json:"email" binding:"required,email"`
	Password    string `json:"password" binding:"required,min=8"`
	FirstName   string `json:"first_name" binding:"required,min=1,max=100"`
	LastName    string `json:"last_name" binding:"required,min=1,max=100"`
	DateOfBirth string `json:"date_of_birth"` // Made optional to match Flutter app
}

// LoginRequest represents a user login request
type LoginRequest struct {
	EmailOrUsername string `json:"email" binding:"required"`
	Password        string `json:"password" binding:"required"`
}

// Pure Kratos: Simplified response structures

// UserInfo represents user information in auth responses
type UserInfo struct {
	ID          string     `json:"id"`
	Username    string     `json:"username"`
	Email       string     `json:"email"`
	FirstName   string     `json:"first_name"`
	LastName    string     `json:"last_name"`
	AvatarURL   *string    `json:"avatar_url"`
	DateOfBirth *time.Time `json:"date_of_birth"`
}

// register handles user registration with proper transaction handling
func (s *Service) register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Validate required fields
	if req.Email == "" || req.Password == "" || req.Username == "" ||
		req.FirstName == "" || req.LastName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing required fields"})
		return
	}

	// Parse date of birth
	var dateOfBirth *time.Time
	if req.DateOfBirth != "" {
		parsed, err := time.Parse("2006-01-02", req.DateOfBirth)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid date of birth format. Use YYYY-MM-DD"})
			return
		}
		dateOfBirth = &parsed
	}

	// Check if user already exists by email
	existingUser, err := s.db.GetUserByEmail(c.Request.Context(), req.Email)
	if err == nil && existingUser != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "User with this email already exists"})
		return
	}

	// Check if username is already taken
	existingUserByUsername, err := s.db.GetUserByUsername(c.Request.Context(), req.Username)
	if err == nil && existingUserByUsername != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Username is already taken"})
		return
	}

	// Create identity in Ory Kratos first
	traits := map[string]interface{}{
		"email":      req.Email,
		"username":   req.Username,
		"first_name": req.FirstName,
		"last_name":  req.LastName,
	}

	identity, err := s.oryClient.CreateIdentity(c.Request.Context(), req.Email, req.Password, traits)
	if err != nil {
		s.logger.Error("Failed to create identity in Kratos",
			zap.String("email", req.Email),
			zap.Error(err))

		// Check if it's a duplicate identity error
		if strings.Contains(err.Error(), "already exists") || strings.Contains(err.Error(), "duplicate") {
			c.JSON(http.StatusConflict, gin.H{"error": "User with this email already exists"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user account"})
		}
		return
	}

	// Create user in our local database
	dbUser := &database.User{
		ID:          identity.Id,
		Username:    &req.Username,
		Email:       req.Email,
		FirstName:   &req.FirstName,
		LastName:    &req.LastName,
		DateOfBirth: dateOfBirth,
		IsActive:    true,
		IsPrivate:   false,
	}

	if err := s.db.CreateUser(c.Request.Context(), dbUser); err != nil {
		s.logger.Error("Failed to create user in local database",
			zap.String("user_id", identity.Id),
			zap.String("email", req.Email),
			zap.Error(err))

		// Clean up the Kratos identity since local DB creation failed
		if deleteErr := s.oryClient.DeleteIdentity(c.Request.Context(), identity.Id); deleteErr != nil {
			s.logger.Error("Failed to cleanup Kratos identity after DB error",
				zap.String("identity_id", identity.Id),
				zap.Error(deleteErr))
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user account"})
		return
	}

	// Authenticate user immediately after successful registration
	sessionToken, err := s.kratosAuthenticate(c.Request.Context(), req.Email, req.Password)
	if err != nil {
		s.logger.Error("Failed to authenticate after registration",
			zap.String("user_id", identity.Id),
			zap.String("email", req.Email),
			zap.Error(err))

		// Registration succeeded but authentication failed
		// Return success but indicate user needs to login manually
		user := &UserInfo{
			ID:        identity.Id,
			Username:  req.Username,
			Email:     req.Email,
			FirstName: req.FirstName,
			LastName:  req.LastName,
		}

		c.JSON(http.StatusCreated, gin.H{
			"success": true,
			"message": "Account created successfully. Please login to continue.",
			"user":    user,
		})
		return
	}

	// Full success - user created and authenticated
	user := &UserInfo{
		ID:        identity.Id,
		Username:  req.Username,
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
	}

	s.logger.Info("User registered and authenticated successfully",
		zap.String("user_id", identity.Id),
		zap.String("email", req.Email))

	c.JSON(http.StatusCreated, gin.H{
		"success":      true,
		"access_token": sessionToken,
		"user":         user,
	})
}

// login handles user login with comprehensive validation
func (s *Service) login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Validate required fields
	if req.EmailOrUsername == "" || req.Password == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email or username and password are required"})
		return
	}

	// Determine if the input is an email or username
	emailRegex := regexp.MustCompile(`^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$`)
	isEmail := emailRegex.MatchString(req.EmailOrUsername)

	var dbUser *database.User
	var err error

	if isEmail {
		// Get user from local database by email
		dbUser, err = s.db.GetUserByEmail(c.Request.Context(), req.EmailOrUsername)
		if err != nil {
			s.logger.Warn("User not found in database by email",
				zap.String("email", req.EmailOrUsername))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid email or password"})
			return
		}
	} else {
		// Get user from local database by username
		dbUser, err = s.db.GetUserByUsername(c.Request.Context(), req.EmailOrUsername)
		if err != nil {
			s.logger.Warn("User not found in database by username",
				zap.String("username", req.EmailOrUsername))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid username or password"})
			return
		}
	}

	// Check if user account is active
	if !dbUser.IsActive {
		s.logger.Warn("Inactive user attempted login",
			zap.String("user_id", dbUser.ID),
			zap.String("login_input", req.EmailOrUsername))
		c.JSON(http.StatusForbidden, gin.H{"error": "Account is deactivated"})
		return
	}

	// Validate credentials with Ory Kratos using the user's email address
	// (Kratos always uses email for authentication, regardless of how user logged in)
	sessionToken, err := s.kratosAuthenticate(c.Request.Context(), dbUser.Email, req.Password)
	if err != nil {
		s.logger.Warn("Kratos authentication failed",
			zap.String("user_email", dbUser.Email),
			zap.String("login_input", req.EmailOrUsername),
			zap.Error(err))
		if isEmail {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid email or password"})
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid username or password"})
		}
		return
	}

	// Create user info for response
	user := &UserInfo{
		ID:        dbUser.ID,
		Username:  *dbUser.Username,
		Email:     dbUser.Email,
		FirstName: *dbUser.FirstName,
		LastName:  *dbUser.LastName,
	}

	s.logger.Info("User logged in successfully",
		zap.String("user_id", dbUser.ID),
		zap.String("login_input", req.EmailOrUsername))

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"access_token": sessionToken,
		"user":         user,
	})
}

// logout handles user logout with comprehensive cleanup
func (s *Service) logout(c *gin.Context) {
	ctx := c.Request.Context()

	// Get user ID from middleware (set during authentication)
	userID, exists := c.Get("user_id")
	if !exists {
		// Even if user is not authenticated, return success for logout
		// This handles cases where the session is already expired
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Logged out successfully",
		})
		return
	}

	identityID := userID.(string)

	// Extract session token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		s.logger.Warn("Logout attempted without authorization header",
			zap.String("user_id", identityID))
		// Still return success - user is effectively logged out
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Logged out successfully",
		})
		return
	}

	// Parse Bearer token
	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || parts[0] != "Bearer" {
		s.logger.Warn("Invalid authorization header format during logout",
			zap.String("user_id", identityID))
		// Still return success - user is effectively logged out
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Logged out successfully",
		})
		return
	}
	sessionToken := parts[1]

	// Invalidate session in Ory Kratos
	if err := s.oryClient.InvalidateSession(ctx, sessionToken); err != nil {
		s.logger.Warn("Failed to invalidate Kratos session",
			zap.String("user_id", identityID),
			zap.Error(err))
		// Don't fail logout if session invalidation fails
		// The session might already be expired or invalid
	} else {
		s.logger.Info("Kratos session invalidated successfully",
			zap.String("user_id", identityID))
	}

	// Log the logout event
	s.logger.Info("User logged out successfully",
		zap.String("user_id", identityID))

	// Always return success for logout
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Logged out successfully",
	})
}

// Refresh endpoint removed - Kratos handles session lifecycle automatically

// getProfile handles getting user profile
func (s *Service) getProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		s.logger.Error("User ID is not a string")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get user from database
	dbUser, err := s.db.GetUserByID(c.Request.Context(), userIDStr)
	if err != nil {
		s.logger.Error("Failed to get user profile",
			zap.String("user_id", userIDStr),
			zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Debug logging
	s.logger.Debug("🔍 Profile endpoint - dbUser retrieved",
		zap.String("user_id", dbUser.ID),
		zap.String("username", func() string {
			if dbUser.Username != nil {
				return *dbUser.Username
			}
			return "nil"
		}()),
		zap.String("avatar_url", func() string {
			if dbUser.AvatarURL != nil {
				return *dbUser.AvatarURL
			}
			return "nil"
		}()))

	// Create user info for response
	user := &UserInfo{
		ID:          dbUser.ID,
		Username:    *dbUser.Username,
		Email:       dbUser.Email,
		FirstName:   *dbUser.FirstName,
		LastName:    *dbUser.LastName,
		AvatarURL:   dbUser.AvatarURL,   // ✅ FIX: Include avatar URL from database
		DateOfBirth: dbUser.DateOfBirth, // ✅ FIX: Include date of birth from database
	}

	// Debug logging for response
	s.logger.Debug("🔍 Profile endpoint - response user",
		zap.String("user_id", user.ID),
		zap.String("username", user.Username),
		zap.String("avatar_url", func() string {
			if user.AvatarURL != nil {
				return *user.AvatarURL
			}
			return "nil"
		}()))

	c.JSON(http.StatusOK, user)
}

// updateProfile handles updating user profile
func (s *Service) updateProfile(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Call the user service (internal HTTPS call within same process)
	userServiceURL := fmt.Sprintf("https://localhost:%d/api/v1/users/%s", s.config.App.Port, userID.(string))

	// Convert updates to JSON
	updateData, err := json.Marshal(updates)
	if err != nil {
		s.logger.Error("Failed to marshal update data", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process update data"})
		return
	}

	// Create HTTP request to user service (internal call)
	req, err := http.NewRequestWithContext(c.Request.Context(), "PUT", userServiceURL, bytes.NewBuffer(updateData))
	if err != nil {
		s.logger.Error("Failed to create request to user service", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update profile"})
		return
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", c.GetHeader("Authorization")) // Forward auth header

	// Make request to user service (internal call with TLS skip for localhost)
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: tr,
	}
	resp, err := client.Do(req)
	if err != nil {
		s.logger.Error("Failed to call user service", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update profile"})
		return
	}
	defer resp.Body.Close()

	// Forward the response from user service
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Error("Failed to read user service response", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update profile"})
		return
	}

	s.logger.Info("Profile updated successfully",
		zap.String("user_id", userID.(string)),
		zap.Any("updates", updates))

	c.Data(resp.StatusCode, "application/json", body)
}

// verifyEmail handles email verification
func (s *Service) verifyEmail(c *gin.Context) {
	// Implementation for email verification
	c.JSON(http.StatusOK, gin.H{"message": "Email verified successfully"})
}

// resetPassword handles password reset
func (s *Service) resetPassword(c *gin.Context) {
	// Implementation for password reset
	c.JSON(http.StatusOK, gin.H{"message": "Password reset email sent"})
}

// changePassword handles password change
func (s *Service) changePassword(c *gin.Context) {
	// Implementation for password change
	c.JSON(http.StatusOK, gin.H{"message": "Password changed successfully"})
}

// JWT functions removed - Pure Kratos implementation

// properAuthMiddleware uses the comprehensive auth middleware that handles both Hydra and Kratos
func (s *Service) properAuthMiddleware() gin.HandlerFunc {
	return authmw.NewAuthMiddleware(s.oryClient, s.logger)
}

// generateSecureToken generates a cryptographically secure random token
func (s *Service) generateSecureToken(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// hashPassword hashes a password using bcrypt
func (s *Service) hashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// verifyPassword verifies a password against its hash
func (s *Service) verifyPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// validateMQTTAuth validates MQTT authentication requests from EMQX
func (s *Service) validateMQTTAuth(c *gin.Context) {
	var req struct {
		Username string `json:"username"`
		Password string `json:"password"` // JWT token (as password)
		ClientID string `json:"clientid"`
		Topic    string `json:"topic,omitempty"`
		Action   string `json:"action,omitempty"` // "publish" or "subscribe"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Error("Invalid MQTT auth request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"result": "deny",
			"reason": "invalid_request",
		})
		return
	}

	// Pure Kratos: Validate session token only
	ctx := context.Background()
	session, err := s.oryClient.ValidateSession(ctx, req.Password)
	if err != nil {
		s.logger.Warn("Invalid Kratos session token for MQTT", zap.Error(err))
		c.JSON(http.StatusOK, gin.H{"result": "deny", "reason": "invalid_token"})
		return
	}

	userID := session.Identity.Id
	s.logger.Debug("MQTT auth using Kratos session token", zap.String("user_id", userID))

	// Ensure username == user_id
	if req.Username != userID {
		s.logger.Warn("Username mismatch in MQTT auth",
			zap.String("username", req.Username), zap.String("user_id", userID))
		c.JSON(http.StatusOK, gin.H{"result": "deny", "reason": "username_mismatch"})
		return
	}

	// Topic authorisation (optional topic check)
	if req.Topic != "" && !s.checkMQTTTopicPermission(userID, req.Topic, req.Action) {
		s.logger.Warn("MQTT topic permission denied",
			zap.String("user_id", userID), zap.String("topic", req.Topic), zap.String("action", req.Action))
		c.JSON(http.StatusOK, gin.H{"result": "deny", "reason": "topic_permission_denied"})
		return
	}

	// Success
	s.logger.Info("MQTT authentication successful", zap.String("user_id", userID), zap.String("topic", req.Topic))
	c.JSON(http.StatusOK, gin.H{"result": "allow", "user_id": userID})
}

// getUserByID retrieves user information by ID from the database
func (s *Service) getUserByID(userID string) (*UserInfo, error) {
	query := `SELECT id, username, email, first_name, last_name, avatar_url FROM users WHERE id = $1`

	var user UserInfo
	err := s.db.Pool.QueryRow(context.Background(), query, userID).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.AvatarURL,
	)

	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	return &user, nil
}

// checkMQTTTopicPermission ensures the user can perform action on topic.
func (s *Service) checkMQTTTopicPermission(userID, topic, action string) bool {
	// Personal topics
	if action == "publish" {
		if topic == fmt.Sprintf("hopen/chat/%s", userID) {
			return true
		}
	}
	if action == "subscribe" {
		// Unified personal notifications & requests topic
		if topic == fmt.Sprintf("hopen/requests/%s", userID) {
			return true
		}
	}
	// Bubble topics: hopen/bubbles/{bubble_id}/chat or /notifications
	if strings.HasPrefix(topic, "hopen/bubbles/") {
		parts := strings.Split(topic, "/")
		if len(parts) >= 3 {
			bubbleID := parts[2]
			return s.checkBubbleMembership(userID, bubbleID)
		}
	}
	return false
}

// checkBubbleMembership queries Postgres for active bubble member.
func (s *Service) checkBubbleMembership(userID, bubbleID string) bool {
	query := `SELECT EXISTS(SELECT 1 FROM bubble_members WHERE bubble_id = $1 AND user_id = $2 AND status = 'active')`
	var exists bool
	if err := s.db.Pool.QueryRow(context.Background(), query, bubbleID, userID).Scan(&exists); err != nil {
		s.logger.Error("bubble membership check failed", zap.Error(err))
		return false
	}
	return exists
}

// extractBubbleIDFromTopic is kept for backward compatibility.
func (s *Service) extractBubbleIDFromTopic(topic string) string {
	parts := strings.Split(topic, "/")
	if len(parts) >= 3 && parts[0] == "hopen" && parts[1] == "bubbles" {
		return parts[2]
	}
	return ""
}

// kratosAuthenticate performs a password login against Ory Kratos' public API and returns the resulting session token.
func (s *Service) kratosAuthenticate(ctx context.Context, email, password string) (string, error) {
	kratosURL := strings.TrimSuffix(s.config.Ory.KratosPublicURL, "/")
	if kratosURL == "" {
		return "", fmt.Errorf("kratos public URL not configured")
	}

	// 1. Initialize a native login flow
	initURL := fmt.Sprintf("%s/self-service/login/api", kratosURL)
	initReq, err := http.NewRequestWithContext(ctx, http.MethodGet, initURL, nil)
	if err != nil {
		return "", err
	}
	initReq.Header.Set("Accept", "application/json")

	resp, err := http.DefaultClient.Do(initReq)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("kratos init login flow failed: %s", string(body))
	}

	var flow struct {
		ID string `json:"id"`
		UI struct {
			Action string `json:"action"`
		} `json:"ui"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&flow); err != nil {
		return "", err
	}

	// 2. Submit the password credentials to complete the flow
	payload := map[string]string{
		"method":     "password",
		"identifier": email,
		"password":   password,
	}
	payloadBytes, _ := json.Marshal(payload)
	submitReq, err := http.NewRequestWithContext(ctx, http.MethodPost, flow.UI.Action, bytes.NewReader(payloadBytes))
	if err != nil {
		return "", err
	}
	submitReq.Header.Set("Content-Type", "application/json")
	submitReq.Header.Set("Accept", "application/json")

	submitResp, err := http.DefaultClient.Do(submitReq)
	if err != nil {
		return "", err
	}
	defer submitResp.Body.Close()
	if submitResp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(submitResp.Body)
		return "", fmt.Errorf("kratos login failed: %s", string(body))
	}

	var success struct {
		SessionToken string `json:"session_token"`
	}
	if err := json.NewDecoder(submitResp.Body).Decode(&success); err != nil {
		return "", err
	}
	if success.SessionToken == "" {
		return "", fmt.Errorf("kratos login response missing session token")
	}
	return success.SessionToken, nil
}
