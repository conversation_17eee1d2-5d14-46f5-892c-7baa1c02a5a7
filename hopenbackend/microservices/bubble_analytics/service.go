package bubble_analytics

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
)

// Service handles bubble analytics (read-only, event-driven)
type Service struct {
	logger   *zap.Logger
	arangoDB *database.ArangoDBClient
	config   *config.Config
	natsConn *nats.Conn
	js       nats.JetStreamContext
	sub      *nats.Subscription
}

// Dependencies holds the dependencies for the bubble analytics service
type Dependencies struct {
	Logger   *zap.Logger
	ArangoDB *database.ArangoDBClient
	Config   *config.Config
	NATS     *nats.Conn
}

// BubbleMembershipAnalytics represents analytics data in ArangoDB
type BubbleMembershipAnalytics struct {
	Key       string     `json:"_key,omitempty"`
	BubbleID  string     `json:"bubble_id"`
	UserID    string     `json:"user_id"`
	Status    string     `json:"status"`
	JoinedAt  time.Time  `json:"joined_at"`
	LeftAt    *time.Time `json:"left_at,omitempty"`
	Source    string     `json:"source"` // "postgresql_event"
	UpdatedAt time.Time  `json:"updated_at"`
}

// BubbleMemberJoinedEvent represents the NATS event
type BubbleMemberJoinedEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	UserID    string `json:"user_id"`
	MemberID  string `json:"member_id"`
	Timestamp int64  `json:"timestamp"`
}

// BubbleMemberLeftEvent represents the NATS event
type BubbleMemberLeftEvent struct {
	EventType string `json:"event_type"`
	BubbleID  string `json:"bubble_id"`
	UserID    string `json:"user_id"`
	Reason    string `json:"reason"`
	Timestamp int64  `json:"timestamp"`
}

// New creates a new bubble analytics service
func New(deps *Dependencies) *Service {
	service := &Service{
		logger:   deps.Logger,
		arangoDB: deps.ArangoDB,
		config:   deps.Config,
		natsConn: deps.NATS,
	}

	// Initialize JetStream
	if err := service.initializeJetStream(); err != nil {
		service.logger.Error("Failed to initialize JetStream", zap.Error(err))
		return service
	}

	// Start consuming NATS events
	service.startEventConsumer()

	return service
}

// initializeJetStream initializes JetStream context and creates streams
func (s *Service) initializeJetStream() error {
	js, err := s.natsConn.JetStream()
	if err != nil {
		return fmt.Errorf("failed to create JetStream context: %w", err)
	}
	s.js = js

	// Create or update the bubble events stream
	streamConfig := &nats.StreamConfig{
		Name:        "BUBBLE_EVENTS",
		Description: "Stream for bubble membership events",
		Subjects:    []string{"events.bubble.>"},
		Retention:   nats.WorkQueuePolicy,
		MaxAge:      24 * time.Hour, // Keep events for 24 hours
		Storage:     nats.FileStorage,
		Replicas:    1,
	}

	_, err = js.AddStream(streamConfig)
	if err != nil {
		// Stream might already exist, try to update it
		_, err = js.UpdateStream(streamConfig)
		if err != nil {
			return fmt.Errorf("failed to create/update stream: %w", err)
		}
	}

	s.logger.Info("JetStream initialized successfully")
	return nil
}

// startEventConsumer starts consuming NATS JetStream events for analytics
func (s *Service) startEventConsumer() {
	if s.js == nil {
		s.logger.Error("JetStream not initialized, cannot start event consumer")
		return
	}

	// Create durable consumer with proper configuration
	consumerConfig := &nats.ConsumerConfig{
		Durable:       "bubble-analytics-consumer",
		AckPolicy:     nats.AckExplicitPolicy,
		MaxDeliver:    3,
		AckWait:       30 * time.Second,
		ReplayPolicy:  nats.ReplayInstantPolicy,
		FilterSubject: "events.bubble.>",
	}

	// Create or update consumer
	_, err := s.js.AddConsumer("BUBBLE_EVENTS", consumerConfig)
	if err != nil {
		s.logger.Error("Failed to create JetStream consumer", zap.Error(err))
		return
	}

	// Subscribe with durable consumer
	sub, err := s.js.PullSubscribe("events.bubble.>", "bubble-analytics-consumer")
	if err != nil {
		s.logger.Error("Failed to create JetStream subscription", zap.Error(err))
		return
	}

	s.sub = sub
	s.logger.Info("Started consuming bubble events for analytics with JetStream",
		zap.String("consumer", "bubble-analytics-consumer"),
		zap.String("stream", "BUBBLE_EVENTS"))

	// Start message processing goroutine
	go s.processMessages()
}

// processMessages processes JetStream messages in a loop
func (s *Service) processMessages() {
	for {
		// Fetch messages in batches with proper error handling
		msgs, err := s.sub.Fetch(10, nats.MaxWait(5*time.Second))
		if err != nil {
			if err == nats.ErrTimeout {
				continue // No messages available, continue polling
			}
			s.logger.Error("Failed to fetch messages", zap.Error(err))
			time.Sleep(5 * time.Second)
			continue
		}

		// Process each message with proper acknowledgment
		for _, msg := range msgs {
			if err := s.processMessage(msg); err != nil {
				s.logger.Error("Failed to process message",
					zap.String("subject", msg.Subject),
					zap.Error(err))

				// Negative acknowledgment for retry
				if err := msg.Nak(); err != nil {
					s.logger.Error("Failed to NAK message", zap.Error(err))
				}
			} else {
				// Positive acknowledgment for successful processing
				if err := msg.Ack(); err != nil {
					s.logger.Error("Failed to ACK message", zap.Error(err))
				}
			}
		}
	}
}

// processMessage processes a single JetStream message
func (s *Service) processMessage(msg *nats.Msg) error {
	s.logger.Debug("Processing message",
		zap.String("subject", msg.Subject),
		zap.String("data", string(msg.Data)))

	switch msg.Subject {
	case "events.bubble.member_joined":
		return s.handleMemberJoinedEvent(msg.Data)
	case "events.bubble.member_left":
		return s.handleMemberLeftEvent(msg.Data)
	default:
		s.logger.Warn("Unknown message subject", zap.String("subject", msg.Subject))
		return nil // Unknown subjects are not errors
	}
}

// handleMemberJoinedEvent processes member joined events
func (s *Service) handleMemberJoinedEvent(data []byte) error {
	var event BubbleMemberJoinedEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return fmt.Errorf("failed to unmarshal member joined event: %w", err)
	}

	s.logger.Info("Processing member joined event",
		zap.String("bubble_id", event.BubbleID),
		zap.String("user_id", event.UserID),
		zap.String("member_id", event.MemberID))

	// Create or update analytics record in ArangoDB
	analytics := &BubbleMembershipAnalytics{
		BubbleID:  event.BubbleID,
		UserID:    event.UserID,
		Status:    "active",
		JoinedAt:  time.Unix(event.Timestamp, 0),
		Source:    "postgresql_event",
		UpdatedAt: time.Now(),
	}

	// Use upsert to handle duplicates
	query := `
		UPSERT { bubble_id: @bubble_id, user_id: @user_id }
		INSERT @analytics
		UPDATE { 
			status: @status,
			joined_at: @joined_at,
			left_at: null,
			updated_at: @updated_at
		}
		IN bubble_memberships_analytics_cache
	`

	_, err := s.arangoDB.Database.Query(context.Background(), query, map[string]interface{}{
		"bubble_id":  event.BubbleID,
		"user_id":    event.UserID,
		"status":     "active",
		"joined_at":  analytics.JoinedAt,
		"updated_at": analytics.UpdatedAt,
		"analytics":  analytics,
	})

	if err != nil {
		return fmt.Errorf("failed to update analytics: %w", err)
	}

	s.logger.Info("Member joined analytics updated successfully",
		zap.String("bubble_id", event.BubbleID),
		zap.String("user_id", event.UserID))

	return nil
}

// handleMemberLeftEvent processes member left events
func (s *Service) handleMemberLeftEvent(data []byte) error {
	var event BubbleMemberLeftEvent
	if err := json.Unmarshal(data, &event); err != nil {
		return fmt.Errorf("failed to unmarshal member left event: %w", err)
	}

	s.logger.Info("Processing member left event",
		zap.String("bubble_id", event.BubbleID),
		zap.String("user_id", event.UserID),
		zap.String("reason", event.Reason))

	// Update analytics record in ArangoDB
	query := `
		FOR doc IN bubble_memberships_analytics_cache
		FILTER doc.bubble_id == @bubble_id AND doc.user_id == @user_id
		UPDATE doc WITH { 
			status: @status,
			left_at: @left_at,
			updated_at: @updated_at
		} IN bubble_memberships_analytics_cache
	`

	leftAt := time.Unix(event.Timestamp, 0)
	_, err := s.arangoDB.Database.Query(context.Background(), query, map[string]interface{}{
		"bubble_id":  event.BubbleID,
		"user_id":    event.UserID,
		"status":     "left",
		"left_at":    leftAt,
		"updated_at": time.Now(),
	})

	if err != nil {
		return fmt.Errorf("failed to update analytics: %w", err)
	}

	s.logger.Info("Member left analytics updated successfully",
		zap.String("bubble_id", event.BubbleID),
		zap.String("user_id", event.UserID),
		zap.String("reason", event.Reason))

	return nil
}

// Close gracefully closes the service
func (s *Service) Close() {
	if s.sub != nil {
		if err := s.sub.Unsubscribe(); err != nil {
			s.logger.Error("Failed to unsubscribe from JetStream", zap.Error(err))
		}
	}
	s.logger.Info("Bubble analytics service closed")
}

// GetMutualBubbleMembers gets mutual bubble members for analytics (example query)
func (s *Service) GetMutualBubbleMembers(ctx context.Context, userID1, userID2 string) ([]string, error) {
	query := `
		FOR m1 IN bubble_memberships_analytics_cache
		FILTER m1.user_id == @userID1 AND m1.status == "active"
		FOR m2 IN bubble_memberships_analytics_cache
		FILTER m2.user_id == @userID2 AND m2.status == "active" AND m1.bubble_id == m2.bubble_id
		RETURN DISTINCT m1.bubble_id`

	cursor, err := s.arangoDB.Database.Query(ctx, query, map[string]interface{}{
		"userID1": userID1,
		"userID2": userID2,
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close()

	var bubbleIDs []string
	for cursor.HasMore() {
		var bubbleID string
		_, err := cursor.ReadDocument(ctx, &bubbleID)
		if err != nil {
			continue
		}
		bubbleIDs = append(bubbleIDs, bubbleID)
	}

	return bubbleIDs, nil
}
