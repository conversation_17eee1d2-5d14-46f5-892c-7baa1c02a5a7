package notification

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ses"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/api/option"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/ory"
)

// Service handles notification operations using PostgreSQL, AWS SES, FCM, and MQTT
type Service struct {
	logger     *zap.Logger
	db         *database.PostgreSQLClient
	config     *config.Config
	fcmClient  *messaging.Client
	sesClient  *ses.SES
	oryClient  *ory.Client
	mqttClient mqtt.Client
}

// Dependencies holds the dependencies for the notification service
type Dependencies struct {
	Logger    *zap.Logger
	DB        *database.PostgreSQLClient
	Config    *config.Config
	OryClient *ory.Client
}

// New creates a new notification service instance
func New(deps *Dependencies) *Service {
	service := &Service{
		logger:    deps.Logger,
		db:        deps.DB,
		config:    deps.Config,
		oryClient: deps.OryClient,
	}

	// Initialize FCM client
	if err := service.initializeFCM(); err != nil {
		service.logger.Error("Failed to initialize FCM client", zap.Error(err))
	}

	// Initialize AWS SES client
	if err := service.initializeSES(); err != nil {
		service.logger.Error("Failed to initialize AWS SES client", zap.Error(err))
	}

	// Initialize MQTT client
	if err := service.initializeMQTT(); err != nil {
		service.logger.Error("Failed to initialize MQTT client", zap.Error(err))
	}

	return service
}

// RegisterRoutes registers the notification service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	// Notification creation endpoint (for internal service communication)
	router.POST("", s.createNotificationHandler)

	router.GET("", s.authMiddleware(), s.getNotifications)
	router.POST("/:id/read", s.authMiddleware(), s.markAsRead)
	router.POST("/:id/unread", s.authMiddleware(), s.markAsUnread)
	router.DELETE("/:id", s.authMiddleware(), s.deleteNotification)
	router.POST("/mark-all-read", s.authMiddleware(), s.markAllAsRead)
	router.GET("/unread-count", s.authMiddleware(), s.getUnreadCount)
	router.PUT("/settings", s.authMiddleware(), s.updateNotificationSettings)
	router.GET("/settings", s.authMiddleware(), s.getNotificationSettings)

	// FCM token management
	router.POST("/fcm/token", s.authMiddleware(), s.registerFCMToken)
	router.DELETE("/fcm/token", s.authMiddleware(), s.unregisterFCMToken)

	// Push notification endpoints
	router.POST("/push/send", s.authMiddleware(), s.sendPushNotification)
	router.POST("/push/topic", s.authMiddleware(), s.sendTopicNotification)
}

// Notification represents a notification in the system
type Notification struct {
	ID        string                 `json:"id"`
	UserID    string                 `json:"user_id"`
	Type      string                 `json:"type"`
	Title     string                 `json:"title"`
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data"`
	IsRead    bool                   `json:"is_read"`
	CreatedAt time.Time              `json:"created_at"`
	ReadAt    *time.Time             `json:"read_at,omitempty"`
}

// NotificationSettings represents user notification preferences
type NotificationSettings struct {
	UserID   string                 `json:"user_id"`
	Settings map[string]interface{} `json:"settings"`
}

// CreateNotificationRequest represents a notification creation request
type CreateNotificationRequest struct {
	UserID  string                 `json:"user_id" binding:"required"`
	Type    string                 `json:"type" binding:"required"`
	Title   string                 `json:"title" binding:"required"`
	Message string                 `json:"message" binding:"required"`
	Data    map[string]interface{} `json:"data"`
}

// UpdateSettingsRequest represents notification settings update request
type UpdateSettingsRequest struct {
	Settings map[string]interface{} `json:"settings" binding:"required"`
}

// getNotifications handles getting user notifications with pagination
func (s *Service) getNotifications(c *gin.Context) {
	userID, _ := c.Get("user_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	notificationType := c.Query("type")
	unreadOnly := c.Query("unread_only") == "true"

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// Build query
	query := `
		SELECT id, user_id, type, title, message, data, is_read, created_at, read_at
		FROM notifications
		WHERE user_id = $1`
	args := []interface{}{userID.(string)}
	argIndex := 2

	if notificationType != "" {
		query += fmt.Sprintf(" AND type = $%d", argIndex)
		args = append(args, notificationType)
		argIndex++
	}

	if unreadOnly {
		query += " AND is_read = false"
	}

	query += " ORDER BY created_at DESC"
	query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, pageSize, offset)

	rows, err := s.db.Pool.Query(c.Request.Context(), query, args...)
	if err != nil {
		s.logger.Error("Failed to get notifications", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notifications"})
		return
	}
	defer rows.Close()

	var notifications []*Notification
	for rows.Next() {
		var notification Notification
		err := rows.Scan(
			&notification.ID, &notification.UserID, &notification.Type,
			&notification.Title, &notification.Message, &notification.Data,
			&notification.IsRead, &notification.CreatedAt, &notification.ReadAt,
		)
		if err != nil {
			s.logger.Error("Failed to scan notification", zap.Error(err))
			continue
		}
		notifications = append(notifications, &notification)
	}

	// Get total count
	countQuery := `
		SELECT COUNT(*) FROM notifications 
		WHERE user_id = $1`
	countArgs := []interface{}{userID.(string)}

	if notificationType != "" {
		countQuery += " AND type = $2"
		countArgs = append(countArgs, notificationType)
	}

	if unreadOnly {
		countQuery += " AND is_read = false"
	}

	var totalCount int
	err = s.db.Pool.QueryRow(c.Request.Context(), countQuery, countArgs...).Scan(&totalCount)
	if err != nil {
		s.logger.Error("Failed to get notification count", zap.Error(err))
		totalCount = len(notifications)
	}

	c.JSON(http.StatusOK, gin.H{
		"notifications": notifications,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total_count": totalCount,
			"total_pages": (totalCount + pageSize - 1) / pageSize,
		},
	})
}

// markAsRead handles marking a notification as read
func (s *Service) markAsRead(c *gin.Context) {
	userID, _ := c.Get("user_id")
	notificationID := c.Param("id")

	if notificationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	// Validate UUID format
	if _, err := uuid.Parse(notificationID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID format"})
		return
	}

	query := `
		UPDATE notifications 
		SET is_read = true, read_at = NOW()
		WHERE id = $1 AND user_id = $2`

	result, err := s.db.Pool.Exec(c.Request.Context(), query, notificationID, userID.(string))
	if err != nil {
		s.logger.Error("Failed to mark notification as read", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update notification"})
		return
	}

	if result.RowsAffected() == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
		return
	}

	s.logger.Info("Notification marked as read",
		zap.String("notification_id", notificationID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Notification marked as read"})
}

// markAsUnread handles marking a notification as unread
func (s *Service) markAsUnread(c *gin.Context) {
	userID, _ := c.Get("user_id")
	notificationID := c.Param("id")

	if notificationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	query := `
		UPDATE notifications 
		SET is_read = false, read_at = NULL
		WHERE id = $1 AND user_id = $2`

	result, err := s.db.Pool.Exec(c.Request.Context(), query, notificationID, userID.(string))
	if err != nil {
		s.logger.Error("Failed to mark notification as unread", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update notification"})
		return
	}

	if result.RowsAffected() == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
		return
	}

	s.logger.Info("Notification marked as unread",
		zap.String("notification_id", notificationID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Notification marked as unread"})
}

// deleteNotification handles deleting a notification
func (s *Service) deleteNotification(c *gin.Context) {
	userID, _ := c.Get("user_id")
	notificationID := c.Param("id")

	if notificationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	query := `
		DELETE FROM notifications 
		WHERE id = $1 AND user_id = $2`

	result, err := s.db.Pool.Exec(c.Request.Context(), query, notificationID, userID.(string))
	if err != nil {
		s.logger.Error("Failed to delete notification", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete notification"})
		return
	}

	if result.RowsAffected() == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
		return
	}

	s.logger.Info("Notification deleted",
		zap.String("notification_id", notificationID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Notification deleted"})
}

// markAllAsRead handles marking all notifications as read
func (s *Service) markAllAsRead(c *gin.Context) {
	userID, _ := c.Get("user_id")

	query := `
		UPDATE notifications 
		SET is_read = true, read_at = NOW()
		WHERE user_id = $1 AND is_read = false`

	result, err := s.db.Pool.Exec(c.Request.Context(), query, userID.(string))
	if err != nil {
		s.logger.Error("Failed to mark all notifications as read", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update notifications"})
		return
	}

	s.logger.Info("All notifications marked as read",
		zap.String("user_id", userID.(string)),
		zap.Int64("count", result.RowsAffected()))

	c.JSON(http.StatusOK, gin.H{
		"message": "All notifications marked as read",
		"count":   result.RowsAffected(),
	})
}

// getUnreadCount handles getting unread notification count
func (s *Service) getUnreadCount(c *gin.Context) {
	userID, _ := c.Get("user_id")

	query := `
		SELECT COUNT(*) FROM notifications 
		WHERE user_id = $1 AND is_read = false`

	var count int
	err := s.db.Pool.QueryRow(c.Request.Context(), query, userID.(string)).Scan(&count)
	if err != nil {
		s.logger.Error("Failed to get unread count", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get unread count"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"unread_count": count})
}

// updateNotificationSettings handles updating user notification settings
func (s *Service) updateNotificationSettings(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req UpdateSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	query := `
		INSERT INTO notification_settings (user_id, settings, updated_at)
		VALUES ($1, $2, NOW())
		ON CONFLICT (user_id)
		DO UPDATE SET settings = $2, updated_at = NOW()`

	_, err := s.db.Pool.Exec(c.Request.Context(), query, userID.(string), req.Settings)
	if err != nil {
		s.logger.Error("Failed to update notification settings", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update settings"})
		return
	}

	s.logger.Info("Notification settings updated",
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Notification settings updated"})
}

// getNotificationSettings handles getting user notification settings
func (s *Service) getNotificationSettings(c *gin.Context) {
	userID, _ := c.Get("user_id")

	query := `
		SELECT settings FROM notification_settings
		WHERE user_id = $1`

	var settings map[string]interface{}
	err := s.db.Pool.QueryRow(c.Request.Context(), query, userID.(string)).Scan(&settings)
	if err != nil {
		// Return default settings if none exist
		defaultSettings := map[string]interface{}{
			"email_notifications":     true,
			"push_notifications":      true,
			"bubble_invites":          true,
			"friend_requests":         true,
			"contact_requests":        true,
			"bubble_expiry_reminders": true,
			"birthday_reminders":      true,
			"message_notifications":   true,
		}

		c.JSON(http.StatusOK, gin.H{"settings": defaultSettings})
		return
	}

	c.JSON(http.StatusOK, gin.H{"settings": settings})
}

// createNotificationHandler handles HTTP requests to create notifications
func (s *Service) createNotificationHandler(c *gin.Context) {
	var req CreateNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	notification, err := s.CreateNotification(c.Request.Context(), &req)
	if err != nil {
		s.logger.Error("Failed to create notification", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create notification"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":      "Notification created successfully",
		"notification": notification,
	})
}

// CreateNotification creates a new notification (internal method)
func (s *Service) CreateNotification(ctx context.Context, req *CreateNotificationRequest) (*Notification, error) {
	notificationID := uuid.New().String()
	now := time.Now()

	notification := &Notification{
		ID:        notificationID,
		UserID:    req.UserID,
		Type:      req.Type,
		Title:     req.Title,
		Message:   req.Message,
		Data:      req.Data,
		IsRead:    false,
		CreatedAt: now,
	}

	query := `
		INSERT INTO notifications (id, user_id, type, title, message, data, is_read, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`

	_, err := s.db.Pool.Exec(ctx, query,
		notification.ID, notification.UserID, notification.Type,
		notification.Title, notification.Message, notification.Data,
		notification.IsRead, notification.CreatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	s.logger.Info("Notification created",
		zap.String("notification_id", notificationID),
		zap.String("user_id", req.UserID),
		zap.String("type", req.Type))

	// Send real-time MQTT notification for immediate delivery to active users
	go s.publishMQTTNotification(notification)

	// Send push notification for inactive users
	go s.sendPushNotificationIfNeeded(ctx, notification)

	// Send email notification if enabled
	go s.sendEmailNotification(notification)

	return notification, nil
}

// SendBubbleInviteNotification sends a bubble invite notification
func (s *Service) SendBubbleInviteNotification(ctx context.Context, recipientID, senderID, bubbleID string) error {
	req := &CreateNotificationRequest{
		UserID:  recipientID,
		Type:    "bubble_invite",
		Title:   "Bubble Invitation",
		Message: "You've been invited to join a bubble!",
		Data: map[string]interface{}{
			"sender_id": senderID,
			"bubble_id": bubbleID,
		},
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// SendFriendRequestNotification sends a friend request notification
func (s *Service) SendFriendRequestNotification(ctx context.Context, recipientID, requesterID, sourceBubbleID string) error {
	req := &CreateNotificationRequest{
		UserID:  recipientID,
		Type:    "friend_request",
		Title:   "Friend Request",
		Message: "You have a new friend request from a bubble connection!",
		Data: map[string]interface{}{
			"requester_id":     requesterID,
			"source_bubble_id": sourceBubbleID,
			"auto_generated":   true,
		},
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// SendBubbleJoinRequestNotification sends a bubble join request notification
func (s *Service) SendBubbleJoinRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID string) error {
	req := &CreateNotificationRequest{
		UserID:  recipientID,
		Type:    "bubble_join_request",
		Title:   "Bubble Join Request",
		Message: "Someone wants to join your bubble!",
		Data: map[string]interface{}{
			"requester_id": requesterID,
			"bubble_id":    bubbleID,
		},
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// SendBubbleProposeRequestNotification sends a bubble propose request notification
func (s *Service) SendBubbleProposeRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID, proposedUserID string) error {
	req := &CreateNotificationRequest{
		UserID:  recipientID,
		Type:    "bubble_propose_request",
		Title:   "Bubble Member Proposal",
		Message: "Someone has been proposed to join your bubble!",
		Data: map[string]interface{}{
			"requester_id":     requesterID,
			"bubble_id":        bubbleID,
			"proposed_user_id": proposedUserID,
		},
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// SendBubbleKickoutRequestNotification sends a bubble kickout request notification
func (s *Service) SendBubbleKickoutRequestNotification(ctx context.Context, recipientID, requesterID, bubbleID, targetUserID string) error {
	req := &CreateNotificationRequest{
		UserID:  recipientID,
		Type:    "bubble_kickout_request",
		Title:   "Bubble Kickout Request",
		Message: "A kickout request has been made in your bubble!",
		Data: map[string]interface{}{
			"requester_id":   requesterID,
			"bubble_id":      bubbleID,
			"target_user_id": targetUserID,
		},
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// SendBubbleStartRequestNotification sends a bubble start request notification
func (s *Service) SendBubbleStartRequestNotification(ctx context.Context, recipientID, requesterID string) error {
	req := &CreateNotificationRequest{
		UserID:  recipientID,
		Type:    "bubble_start_request",
		Title:   "Bubble Start Request",
		Message: "Someone wants to start a bubble with you!",
		Data: map[string]interface{}{
			"requester_id": requesterID,
		},
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// SendContactRequestNotification sends a contact request notification
func (s *Service) SendContactRequestNotification(ctx context.Context, recipientID, requesterID string) error {
	req := &CreateNotificationRequest{
		UserID:  recipientID,
		Type:    "contact_request",
		Title:   "Contact Request",
		Message: "You have a new contact request!",
		Data: map[string]interface{}{
			"requester_id": requesterID,
		},
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// SendBubbleExpiryNotification sends a bubble expiry reminder
func (s *Service) SendBubbleExpiryNotification(ctx context.Context, userID, bubbleID string, daysLeft int) error {
	req := &CreateNotificationRequest{
		UserID:  userID,
		Type:    "bubble_expiry",
		Title:   "Bubble Expiring Soon",
		Message: fmt.Sprintf("Your bubble will expire in %d days", daysLeft),
		Data: map[string]interface{}{
			"bubble_id": bubbleID,
			"days_left": daysLeft,
		},
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// SendMessageNotification sends a message notification
func (s *Service) SendMessageNotification(ctx context.Context, recipientID, senderID, bubbleID, messageID string) error {
	req := &CreateNotificationRequest{
		UserID:  recipientID,
		Type:    "message",
		Title:   "New Message",
		Message: "You have a new message in your bubble!",
		Data: map[string]interface{}{
			"sender_id":  senderID,
			"bubble_id":  bubbleID,
			"message_id": messageID,
		},
	}

	_, err := s.CreateNotification(ctx, req)
	return err
}

// sendEmailNotification sends an email notification via AWS SES
func (s *Service) sendEmailNotification(notification *Notification) {
	// Check if user has email notifications enabled
	settings, err := s.getUserNotificationSettings(notification.UserID)
	if err != nil || !settings["email_notifications"].(bool) {
		return
	}

	// Get user email from database
	userEmail, err := s.getUserEmail(notification.UserID)
	if err != nil {
		s.logger.Error("Failed to get user email", zap.Error(err))
		return
	}

	// Send email via AWS SES
	if err := s.sendSESEmail(userEmail, notification); err != nil {
		s.logger.Error("Failed to send email via AWS SES",
			zap.String("user_id", notification.UserID),
			zap.String("email", userEmail),
			zap.String("type", notification.Type),
			zap.Error(err))
	} else {
		s.logger.Info("Email notification sent successfully via AWS SES",
			zap.String("user_id", notification.UserID),
			zap.String("email", userEmail),
			zap.String("type", notification.Type))
	}
}

// getUserNotificationSettings gets user notification settings
func (s *Service) getUserNotificationSettings(userID string) (map[string]interface{}, error) {
	query := `
		SELECT settings FROM notification_settings
		WHERE user_id = $1`

	var settings map[string]interface{}
	err := s.db.Pool.QueryRow(context.Background(), query, userID).Scan(&settings)
	if err != nil {
		// Return default settings
		return map[string]interface{}{
			"email_notifications": true,
			"push_notifications":  true,
		}, nil
	}

	return settings, nil
}

// getUserEmail gets user email address
func (s *Service) getUserEmail(userID string) (string, error) {
	query := `
		SELECT email FROM users
		WHERE id = $1`

	var email string
	err := s.db.Pool.QueryRow(context.Background(), query, userID).Scan(&email)
	if err != nil {
		return "", fmt.Errorf("failed to get user email: %w", err)
	}

	return email, nil
}

// authMiddleware provides authentication middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.NewAuthMiddleware(s.oryClient, s.logger)
}

// initializeFCM initializes the Firebase Cloud Messaging client
func (s *Service) initializeFCM() error {
	// Check if FCM is configured
	if s.config.Firebase.ServiceAccountPath == "" {
		s.logger.Warn("FCM service account path not configured, push notifications disabled")
		return nil
	}

	ctx := context.Background()
	opt := option.WithCredentialsFile(s.config.Firebase.ServiceAccountPath)
	app, err := firebase.NewApp(ctx, nil, opt)
	if err != nil {
		return fmt.Errorf("failed to initialize Firebase app: %w", err)
	}

	client, err := app.Messaging(ctx)
	if err != nil {
		return fmt.Errorf("failed to initialize FCM client: %w", err)
	}

	s.fcmClient = client
	s.logger.Info("FCM client initialized successfully")
	return nil
}

// initializeSES initializes the AWS SES client
func (s *Service) initializeSES() error {
	// Check if AWS SES is configured
	if s.config.AWS.Region == "" {
		s.logger.Warn("AWS SES region not configured, email notifications disabled")
		return nil
	}

	// Create AWS session
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(s.config.AWS.Region),
	})
	if err != nil {
		return fmt.Errorf("failed to create AWS session: %w", err)
	}

	s.sesClient = ses.New(sess)
	s.logger.Info("AWS SES client initialized successfully")
	return nil
}

// sendSESEmail sends an email via AWS SES
func (s *Service) sendSESEmail(toEmail string, notification *Notification) error {
	if s.sesClient == nil {
		return fmt.Errorf("AWS SES client not initialized")
	}

	// Prepare email content
	subject := notification.Title

	// Create email template based on notification type
	htmlBody, textBody := s.createEmailTemplate(notification)

	// Create SES email input
	input := &ses.SendEmailInput{
		Destination: &ses.Destination{
			ToAddresses: []*string{aws.String(toEmail)},
		},
		Message: &ses.Message{
			Body: &ses.Body{
				Html: &ses.Content{
					Charset: aws.String("UTF-8"),
					Data:    aws.String(htmlBody),
				},
				Text: &ses.Content{
					Charset: aws.String("UTF-8"),
					Data:    aws.String(textBody),
				},
			},
			Subject: &ses.Content{
				Charset: aws.String("UTF-8"),
				Data:    aws.String(subject),
			},
		},
		Source: aws.String(s.config.AWS.SESFromEmail),
	}

	// Send email
	result, err := s.sesClient.SendEmail(input)
	if err != nil {
		return fmt.Errorf("failed to send email via SES: %w", err)
	}

	s.logger.Info("Email sent successfully via AWS SES",
		zap.String("message_id", *result.MessageId),
		zap.String("to_email", toEmail),
		zap.String("notification_type", notification.Type))

	return nil
}

// createEmailTemplate creates HTML and text email templates
func (s *Service) createEmailTemplate(notification *Notification) (string, string) {
	// HTML template
	htmlTemplate := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>%s</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #4CAF50; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Hopen</h1>
        </div>
        <div class="content">
            <h2>%s</h2>
            <p>%s</p>
        </div>
        <div class="footer">
            <p>This email was sent from Hopen. If you no longer wish to receive these emails, you can update your notification preferences in the app.</p>
        </div>
    </div>
</body>
</html>`, notification.Title, notification.Title, notification.Message)

	// Text template
	textTemplate := fmt.Sprintf(`
HOPEN

%s

%s

---
This email was sent from Hopen. If you no longer wish to receive these emails, you can update your notification preferences in the app.
`, notification.Title, notification.Message)

	return htmlTemplate, textTemplate
}

// registerFCMToken registers a user's FCM token for push notifications
func (s *Service) registerFCMToken(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req struct {
		Token    string `json:"token" binding:"required"`
		DeviceID string `json:"device_id" binding:"required"`
		Platform string `json:"platform" binding:"required"` // "ios" or "android"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Store FCM token in database
	query := `
		INSERT INTO fcm_tokens (user_id, token, device_id, platform, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
		ON CONFLICT (user_id, device_id)
		DO UPDATE SET token = $2, platform = $4, updated_at = $6`

	now := time.Now()
	_, err := s.db.Pool.Exec(c.Request.Context(), query,
		userID.(string), req.Token, req.DeviceID, req.Platform, now, now)

	if err != nil {
		s.logger.Error("Failed to store FCM token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to register FCM token"})
		return
	}

	s.logger.Info("FCM token registered successfully",
		zap.String("user_id", userID.(string)),
		zap.String("device_id", req.DeviceID),
		zap.String("platform", req.Platform))

	c.JSON(http.StatusOK, gin.H{"message": "FCM token registered successfully"})
}

// unregisterFCMToken removes a user's FCM token
func (s *Service) unregisterFCMToken(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req struct {
		DeviceID string `json:"device_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	query := `DELETE FROM fcm_tokens WHERE user_id = $1 AND device_id = $2`
	result, err := s.db.Pool.Exec(c.Request.Context(), query, userID.(string), req.DeviceID)

	if err != nil {
		s.logger.Error("Failed to delete FCM token", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unregister FCM token"})
		return
	}

	if result.RowsAffected() == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "FCM token not found"})
		return
	}

	s.logger.Info("FCM token unregistered successfully",
		zap.String("user_id", userID.(string)),
		zap.String("device_id", req.DeviceID))

	c.JSON(http.StatusOK, gin.H{"message": "FCM token unregistered successfully"})
}

// sendPushNotification sends a push notification to specific users (token-based)
func (s *Service) sendPushNotification(c *gin.Context) {
	var req struct {
		UserIDs []string          `json:"user_ids" binding:"required"`
		Title   string            `json:"title" binding:"required"`
		Body    string            `json:"body" binding:"required"`
		Data    map[string]string `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if s.fcmClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "FCM not configured"})
		return
	}

	// Get FCM tokens for the specified users
	tokens, err := s.getFCMTokensForUsers(c.Request.Context(), req.UserIDs)
	if err != nil {
		s.logger.Error("Failed to get FCM tokens", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get FCM tokens"})
		return
	}

	if len(tokens) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"message": "No FCM tokens found for specified users",
			"sent":    0,
		})
		return
	}

	// Create FCM message
	message := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title: req.Title,
			Body:  req.Body,
		},
		Data:   req.Data,
		Tokens: tokens,
	}

	// Send the message
	response, err := s.fcmClient.SendMulticast(c.Request.Context(), message)
	if err != nil {
		s.logger.Error("Failed to send FCM message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send push notification"})
		return
	}

	s.logger.Info("Push notification sent successfully",
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failure_count", response.FailureCount),
		zap.Int("total_tokens", len(tokens)))

	c.JSON(http.StatusOK, gin.H{
		"message":       "Push notification sent",
		"success_count": response.SuccessCount,
		"failure_count": response.FailureCount,
		"total_tokens":  len(tokens),
	})
}

// sendTopicNotification sends a push notification to a topic (topic-based)
func (s *Service) sendTopicNotification(c *gin.Context) {
	var req struct {
		Topic string            `json:"topic" binding:"required"`
		Title string            `json:"title" binding:"required"`
		Body  string            `json:"body" binding:"required"`
		Data  map[string]string `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if s.fcmClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "FCM not configured"})
		return
	}

	// Create FCM topic message
	message := &messaging.Message{
		Notification: &messaging.Notification{
			Title: req.Title,
			Body:  req.Body,
		},
		Data:  req.Data,
		Topic: req.Topic,
	}

	// Send the message
	response, err := s.fcmClient.Send(c.Request.Context(), message)
	if err != nil {
		s.logger.Error("Failed to send FCM topic message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send topic notification"})
		return
	}

	s.logger.Info("Topic notification sent successfully",
		zap.String("topic", req.Topic),
		zap.String("message_id", response))

	c.JSON(http.StatusOK, gin.H{
		"message":    "Topic notification sent",
		"message_id": response,
		"topic":      req.Topic,
	})
}

// getFCMTokensForUsers retrieves FCM tokens for specified users
func (s *Service) getFCMTokensForUsers(ctx context.Context, userIDs []string) ([]string, error) {
	if len(userIDs) == 0 {
		return []string{}, nil
	}

	// Build query with placeholders for user IDs
	query := `
		SELECT token FROM fcm_tokens
		WHERE user_id = ANY($1) AND token IS NOT NULL AND token != ''
		ORDER BY updated_at DESC`

	rows, err := s.db.Pool.Query(ctx, query, userIDs)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tokens []string
	for rows.Next() {
		var token string
		if err := rows.Scan(&token); err != nil {
			s.logger.Error("Failed to scan FCM token", zap.Error(err))
			continue
		}
		tokens = append(tokens, token)
	}

	return tokens, nil
}

// SendPushToUser sends a push notification to a specific user (helper method for other services)
func (s *Service) SendPushToUser(ctx context.Context, userID, title, body string, data map[string]string) error {
	if s.fcmClient == nil {
		s.logger.Warn("FCM not configured, skipping push notification")
		return nil
	}

	tokens, err := s.getFCMTokensForUsers(ctx, []string{userID})
	if err != nil {
		return fmt.Errorf("failed to get FCM tokens: %w", err)
	}

	if len(tokens) == 0 {
		s.logger.Debug("No FCM tokens found for user", zap.String("user_id", userID))
		return nil
	}

	message := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Data:   data,
		Tokens: tokens,
	}

	response, err := s.fcmClient.SendMulticast(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to send FCM message: %w", err)
	}

	s.logger.Info("Push notification sent to user",
		zap.String("user_id", userID),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failure_count", response.FailureCount))

	return nil
}

// SendTopicPush sends a push notification to a topic (helper method for other services)
func (s *Service) SendTopicPush(ctx context.Context, topic, title, body string, data map[string]string) error {
	if s.fcmClient == nil {
		s.logger.Warn("FCM not configured, skipping topic notification")
		return nil
	}

	message := &messaging.Message{
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Data:  data,
		Topic: topic,
	}

	response, err := s.fcmClient.Send(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to send FCM topic message: %w", err)
	}

	s.logger.Info("Topic notification sent",
		zap.String("topic", topic),
		zap.String("message_id", response))

	return nil
}

// initializeMQTT initializes the MQTT client for real-time notifications
func (s *Service) initializeMQTT() error {
	// Check if MQTT is configured
	if s.config.MQTT.Broker == "" {
		s.logger.Warn("MQTT broker not configured, real-time notifications disabled")
		return nil
	}

	opts := mqtt.NewClientOptions()
	opts.AddBroker(s.config.MQTT.Broker)
	opts.SetClientID(fmt.Sprintf("notification-service-%d", time.Now().Unix()))
	opts.SetUsername(s.config.MQTT.Username)
	opts.SetPassword(s.config.MQTT.Password)
	opts.SetKeepAlive(time.Duration(s.config.MQTT.KeepAlive) * time.Second)
	opts.SetCleanSession(true)

	opts.SetOnConnectHandler(func(client mqtt.Client) {
		s.logger.Info("Notification service connected to MQTT broker")
	})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		s.logger.Error("Notification service lost connection to MQTT broker", zap.Error(err))
	})

	s.mqttClient = mqtt.NewClient(opts)
	if token := s.mqttClient.Connect(); token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to connect to MQTT broker: %w", token.Error())
	}

	s.logger.Info("MQTT client initialized successfully for notifications")
	return nil
}

// publishMQTTNotification publishes a real-time notification via MQTT
func (s *Service) publishMQTTNotification(notification *Notification) {
	if s.mqttClient == nil || !s.mqttClient.IsConnected() {
		s.logger.Debug("MQTT client not available, skipping real-time notification")
		return
	}

	// Fetch additional user information for better dialog display
	var requesterName, requesterUsername, requesterAvatarUrl string
	if notification.Data != nil {
		if requesterID, ok := notification.Data["requester_id"].(string); ok && requesterID != "" {
			// Try to get user information from the database
			// This is a simplified approach - in production you might want to cache this
			if userInfo, err := s.getUserInfo(requesterID); err == nil {
				requesterName = userInfo.Name
				requesterUsername = userInfo.Username
				if userInfo.AvatarUrl != nil {
					requesterAvatarUrl = *userInfo.AvatarUrl
				}
			}
		}
	}

	// Create MQTT notification payload with enhanced data
	mqttPayload := map[string]interface{}{
		"id":         notification.ID,
		"type":       notification.Type,
		"title":      notification.Title,
		"message":    notification.Message,
		"data":       notification.Data,
		"created_at": notification.CreatedAt.Format(time.RFC3339),
		"timestamp":  time.Now().Format(time.RFC3339),
		"source":     "mqtt_realtime",
	}

	// Add request-specific data for better dialog handling
	if notification.Data != nil {
		if requesterID, ok := notification.Data["requester_id"].(string); ok {
			mqttPayload["requester_id"] = requesterID
			if requesterName != "" {
				mqttPayload["requester_name"] = requesterName
			}
			if requesterUsername != "" {
				mqttPayload["requester_username"] = requesterUsername
			}
			if requesterAvatarUrl != "" {
				mqttPayload["requester_avatar_url"] = requesterAvatarUrl
			}
		}
		if bubbleID, ok := notification.Data["bubble_id"].(string); ok {
			mqttPayload["bubble_id"] = bubbleID
		}
		if senderID, ok := notification.Data["sender_id"].(string); ok {
			mqttPayload["sender_id"] = senderID
		}
		if messageID, ok := notification.Data["message_id"].(string); ok {
			mqttPayload["message_id"] = messageID
		}
	}

	payload, err := json.Marshal(mqttPayload)
	if err != nil {
		s.logger.Error("Failed to marshal MQTT notification payload", zap.Error(err))
		return
	}

	// Publish to user-specific request topic for immediate dialog triggering
	topic := fmt.Sprintf("hopen/requests/%s", notification.UserID)
	token := s.mqttClient.Publish(topic, 1, false, payload) // QoS 1 for reliable delivery

	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish MQTT notification",
			zap.String("topic", topic),
			zap.String("notification_id", notification.ID),
			zap.Error(token.Error()))
	} else {
		s.logger.Info("MQTT notification published successfully",
			zap.String("topic", topic),
			zap.String("notification_id", notification.ID),
			zap.String("type", notification.Type))
	}
}

// getUserInfo fetches user information for enhanced notifications
func (s *Service) getUserInfo(userID string) (*UserInfo, error) {
	// This is a simplified implementation
	// In production, you might want to:
	// 1. Cache user information in Redis/Valkey
	// 2. Use a dedicated user service
	// 3. Include this information in the original notification request

	query := `
		SELECT 
			COALESCE(first_name, '') || ' ' || COALESCE(last_name, '') as name,
			username,
			avatar_url
		FROM users 
		WHERE id = $1 AND is_active = true
	`

	var userInfo UserInfo
	err := s.db.Pool.QueryRow(context.Background(), query, userID).Scan(
		&userInfo.Name,
		&userInfo.Username,
		&userInfo.AvatarUrl,
	)

	if err != nil {
		s.logger.Debug("Failed to fetch user info for notification",
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, err
	}

	return &userInfo, nil
}

// UserInfo represents user information for notifications
type UserInfo struct {
	Name      string  `json:"name"`
	Username  string  `json:"username"`
	AvatarUrl *string `json:"avatar_url"`
}

// sendPushNotificationIfNeeded sends FCM push notification for inactive users
func (s *Service) sendPushNotificationIfNeeded(ctx context.Context, notification *Notification) {
	// Only send push notifications if the user has been inactive for a configurable duration
	const inactivityThreshold = 2 * time.Minute

	recentlyActive, err := s.userRecentlyActive(ctx, notification.UserID, inactivityThreshold)
	if err != nil {
		s.logger.Warn("Failed to determine user last activity", zap.String("user_id", notification.UserID), zap.Error(err))
	}
	if recentlyActive {
		s.logger.Debug("User recently active – skipping push notification", zap.String("user_id", notification.UserID))
		return
	}

	if s.fcmClient == nil {
		s.logger.Debug("FCM not configured, skipping push notification")
		return
	}

	// Get user's FCM tokens
	tokens, err := s.getFCMTokensForUsers(ctx, []string{notification.UserID})
	if err != nil {
		s.logger.Error("Failed to get FCM tokens for push notification",
			zap.String("user_id", notification.UserID),
			zap.Error(err))
		return
	}

	if len(tokens) == 0 {
		s.logger.Debug("No FCM tokens found for user, skipping push notification",
			zap.String("user_id", notification.UserID))
		return
	}

	// Create enhanced push notification data
	data := map[string]string{
		"notification_id": notification.ID,
		"type":            notification.Type,
		"created_at":      notification.CreatedAt.Format(time.RFC3339),
		"action":          "show_request_dialog",
	}

	// Add request-specific data for deep linking
	if notification.Data != nil {
		if requesterID, ok := notification.Data["requester_id"].(string); ok {
			data["requester_id"] = requesterID
		}
		if bubbleID, ok := notification.Data["bubble_id"].(string); ok {
			data["bubble_id"] = bubbleID
		}
	}

	message := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title: notification.Title,
			Body:  notification.Message,
		},
		Data:   data,
		Tokens: tokens,
		Android: &messaging.AndroidConfig{
			Priority: "high",
			Notification: &messaging.AndroidNotification{
				ChannelID: "requests",
				Priority:  messaging.PriorityHigh,
			},
		},
		APNS: &messaging.APNSConfig{
			Headers: map[string]string{
				"apns-priority": "10",
			},
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert: &messaging.ApsAlert{
						Title: notification.Title,
						Body:  notification.Message,
					},
					Badge:    new(int), // Will be set to 1
					Sound:    "default",
					Category: "REQUEST_CATEGORY",
				},
			},
		},
	}

	response, err := s.fcmClient.SendMulticast(ctx, message)
	if err != nil {
		s.logger.Error("Failed to send push notification",
			zap.String("user_id", notification.UserID),
			zap.String("notification_id", notification.ID),
			zap.Error(err))
		return
	}

	s.logger.Info("Push notification sent successfully",
		zap.String("user_id", notification.UserID),
		zap.String("notification_id", notification.ID),
		zap.String("type", notification.Type),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failure_count", response.FailureCount))
}

// userRecentlyActive returns true if the user's last_active_at is within the provided duration.
func (s *Service) userRecentlyActive(ctx context.Context, userID string, within time.Duration) (bool, error) {
	query := `SELECT COALESCE(last_active_at, created_at) FROM users WHERE id = $1`

	var lastActive time.Time
	if err := s.db.Pool.QueryRow(ctx, query, userID).Scan(&lastActive); err != nil {
		return false, err
	}

	return time.Since(lastActive) < within, nil
}
