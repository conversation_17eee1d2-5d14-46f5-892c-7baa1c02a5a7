# Hopen Backend Configuration
app:
  name: "hopen-backend"
  version: "1.0.0"
  environment: "development"
  port: 4000
  debug: true
  tls:
    enabled: true    # Enable TLS with proper certificates
    cert_file: "./certs/hopen-dev.crt"
    key_file: "./certs/hopen-dev.key"
    http3: true      # Enable HTTP/3 with TLS
    http2: true      # Enable HTTP/2 with TLS
    auto_cert: false # Using manually generated certificates
    auto_cert_dir: "/etc/ssl/autocert"

# Database configurations
databases:
  postgresql:
    host: "*********"
    port: 5432
    database: "hopen_db"
    username: "hopen"
    password: "hopen123"
    ssl_mode: "disable"
    max_connections: 25
    min_connections: 5
    max_connection_lifetime: "1h"
    max_connection_idle_time: "30m"
    health_check_period: "1m"

  arangodb:
    endpoints: ["http://*********:8529"]
    database: "hopen_social"
    username: "root"
    password: "hopen123"
    max_connections: 10
    timeout: "30s"
    keep_alive: "60s"

  cassandra:
    hosts: ["*********:9042"]
    keyspace: "hopen"
    consistency: "quorum"
    num_connections: 5
    timeout: "30s"
    connect_timeout: "10s"

# Cache configuration (Valkey)
valkey:
  host: "*********"
  port: 6379
  address: "*********:6379"
  password: "hopen123"
  database: 0
  pool_size: 10
  min_idle_connections: 5
  max_retries: 3
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

# Message queue configuration (NATS)
nats:
  host: "*********"
  port: 4222
  username: ""
  password: ""
  url: "nats://*********:4222"
  max_reconnects: 10
  reconnect_wait: "2s"
  timeout: "10s"

# Object storage configuration (MinIO)
minio:
  endpoint: "*********:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin123"
  use_ssl: false
  bucket_name: "hopen"
  region: ""

# MQTT configuration (EMQX)
mqtt:
  broker: "tcp://hopen_emqx:1883"
  client_id: "hopen-backend"
  username: "hopen"
  password: "hopen"
  keep_alive: 60
  clean_session: true
  qos: 1

# JWT configuration
jwt:
  secret: "your-jwt-secret-key-change-in-production"
  refresh_secret: "your-refresh-secret-key-change-in-production"
  issuer: "hopen-backend"
  audience: "hopen-app"
  access_token_expiry: "1h"
  refresh_token_expiry: "24h"

# Rate limiting configuration
rate_limit:
  enabled: true
  default_limit: 100
  default_window: "1m"
  burst_multiplier: 2.0
  cleanup_interval: "5m"
  
# Ory Stack configuration
ory:
  kratos_public_url: "http://*********:4433"
  kratos_admin_url: "http://*********:4434"
  hydra_public_url: "http://localhost:4444"
  hydra_admin_url: "http://localhost:4445"

# Enterprise modules configuration
enterprise:
  security:
    enabled: true
    csp_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
    hsts_max_age: ********
    max_request_size: 10485760  # 10MB
    enable_content_scan: true
    
  resilience:
    enabled: true
    circuit_breakers:
      postgresql:
        max_failures: 5
        timeout: "30s"
        reset_timeout: "60s"
      arangodb:
        max_failures: 3
        timeout: "20s"
        reset_timeout: "45s"
      cassandra:
        max_failures: 5
        timeout: "30s"
        reset_timeout: "60s"
        
  monitoring:
    enabled: true
    metrics_port: 9090
    social_metrics: true
    business_metrics: true
    
  gateway:
    enabled: true
    port: 8080
    service_discovery: true
    load_balancing: true
    
  database:
    enhanced_pooling: true
    read_write_splitting: true
    health_monitoring: true
    auto_scaling: true
    
  idempotency:
    enabled: true
    default_ttl: "24h"
    storage: "valkey"
    enable_fingerprinting: true

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"
  
# CORS configuration
cors:
  allowed_origins: ["http://localhost:3000", "https://hopenapp.com"]
  allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allowed_headers: ["Content-Type", "Authorization", "X-Requested-With"]
  expose_headers: ["X-Total-Count"]
  allow_credentials: true
  max_age: 86400

# Social app specific configuration
social:
  bubble:
    initial_expiry_days: 90
    extension_days: 30
    max_members: 5
    min_members: 2
    
  friendship:
    auto_generate_on_bubble_expiry: true
    request_expiry_days: 30
    
  contact:
    max_requests_per_day: 50
    request_expiry_days: 7
    
  chat:
    max_message_length: 2000
    file_upload_max_size: 50485760  # 50MB
    supported_file_types: ["image/jpeg", "image/png", "image/gif", "video/mp4", "audio/mp3"]

# Media configuration
media:
  max_file_size: 104857600  # 100MB for general media files
  profile_picture_max_size: 2097152  # 2MB for profile pictures (as per requirements)
  allowed_types: ["image/jpeg", "image/png", "image/gif", "image/webp", "video/mp4", "video/webm", "audio/mp3", "audio/wav", "application/pdf"]
  profile_picture_types: ["image/jpeg", "image/png", "image/webp"]  # Allowed profile picture formats
  thumbnail_size: 300
  cdn:
    enabled: false  # Set to true when CDN is configured
    base_url: "https://cdn.hopenapp.com"  # CDN base URL
    cache_control: "public, max-age=********"  # 1 year cache

# Firebase configuration for FCM push notifications
firebase:
  service_account_path: "config/firebase-service-account.json"
  project_id: "hopen-app"
