#!/bin/bash

# Test script for profile picture upload flow
echo "Testing profile picture upload flow..."

# First, get a session token (you'll need to login first)
echo "1. Getting session token..."
# This would normally come from a login request
SESSION_TOKEN="ory_st_ygNi6v4eKG1j4..."  # Replace with actual token

# Test the generate-upload-url endpoint
echo "2. Testing generate-upload-url endpoint..."
curl -X POST "https://*********:4000/api/v1/media/generate-upload-url" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $SESSION_TOKEN" \
  -d '{
    "file_name": "test_profile.jpg",
    "content_type": "image/jpeg",
    "file_size": 102400
  }' \
  -k

echo -e "\n\n3. If successful, you should see a response with upload_url, file_id, and object_key"
echo "4. The Flutter app will then upload the file directly to MinIO using the presigned URL"
echo "5. Finally, it will call confirm-upload to save the metadata"
echo -e "\nTest completed!" 