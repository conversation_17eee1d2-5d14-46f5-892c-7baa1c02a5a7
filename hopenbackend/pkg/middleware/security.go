package middleware

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// SecurityMiddleware provides enterprise-grade security middleware
type SecurityMiddleware struct {
	config  *SecurityConfig
	metrics *SecurityMetrics
}

// SecurityConfig holds security configuration
type SecurityConfig struct {
	CSPPolicy         string   `yaml:"csp_policy"`
	HSTSMaxAge        int      `yaml:"hsts_max_age"`
	MaxRequestSize    int64    `yaml:"max_request_size"`
	EnableContentScan bool     `yaml:"enable_content_scan"`
	TrustedOrigins    []string `yaml:"trusted_origins"`
	TrustedProxies    []string `yaml:"trusted_proxies"`
}

// SecurityMetrics holds security-related metrics
type SecurityMetrics struct {
	blockedRequests      *prometheus.CounterVec
	securityViolations   *prometheus.CounterVec
	processingDuration   prometheus.Histogram
	requestSizeHistogram prometheus.Histogram
}

// NewSecurityMiddleware creates a new security middleware instance
func NewSecurityMiddleware(config *SecurityConfig) *SecurityMiddleware {
	metrics := &SecurityMetrics{
		blockedRequests: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_security_blocked_requests_total",
			Help: "Total number of blocked requests by security middleware",
		}, []string{"reason"}),
		securityViolations: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_security_violations_total",
			Help: "Total number of security violations detected",
		}, []string{"type"}),
		processingDuration: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "hopen_security_processing_duration_seconds",
			Help:    "Time spent processing security checks",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0},
		}),
		requestSizeHistogram: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "hopen_request_size_bytes",
			Help:    "Size of incoming requests in bytes",
			Buckets: []float64{1024, 10240, 102400, 1048576, 10485760, 52428800},
		}),
	}

	return &SecurityMiddleware{config: config, metrics: metrics}
}

// SecurityHandler returns the main security middleware handler
func (sm *SecurityMiddleware) SecurityHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Debug: Log that the security middleware is being called
		fmt.Printf("DEBUG: Security middleware called for %s %s\n", c.Request.Method, c.Request.URL.Path)

		// 1. Request size validation
		if err := sm.validateRequestSize(c.Request); err != nil {
			sm.metrics.blockedRequests.WithLabelValues("size_limit").Inc()
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "Request too large"})
			c.Abort()
			return
		}

		// 2. Content type validation
		if err := sm.validateContentType(c.Request); err != nil {
			sm.metrics.blockedRequests.WithLabelValues("invalid_content").Inc()
			c.JSON(http.StatusUnsupportedMediaType, gin.H{"error": "Invalid content type"})
			c.Abort()
			return
		}

		// 3. Malicious content scanning
		if sm.config.EnableContentScan {
			if err := sm.scanContent(c.Request); err != nil {
				sm.metrics.securityViolations.WithLabelValues("malicious_content").Inc()
				c.JSON(http.StatusBadRequest, gin.H{"error": "Content blocked"})
				c.Abort()
				return
			}
		}

		// 4. Set security headers
		sm.setSecurityHeaders(c.Writer)

		// 5. Record metrics
		sm.metrics.processingDuration.Observe(time.Since(start).Seconds())

		c.Next()
	}
}

// validateRequestSize validates the request size
func (sm *SecurityMiddleware) validateRequestSize(r *http.Request) error {
	if r.ContentLength > sm.config.MaxRequestSize {
		return fmt.Errorf("request size %d exceeds maximum %d", r.ContentLength, sm.config.MaxRequestSize)
	}

	if r.ContentLength > 0 {
		sm.metrics.requestSizeHistogram.Observe(float64(r.ContentLength))
	}
	return nil
}

// validateContentType validates the content type
func (sm *SecurityMiddleware) validateContentType(r *http.Request) error {
	contentType := r.Header.Get("Content-Type")
	if contentType == "" {
		return nil
	}

	allowedTypes := []string{"application/json", "application/x-www-form-urlencoded", "multipart/form-data", "text/plain", "image/jpeg", "image/png", "image/gif", "video/mp4", "audio/mp3", "audio/wav"}
	for _, allowed := range allowedTypes {
		if strings.HasPrefix(contentType, allowed) {
			return nil
		}
	}
	return fmt.Errorf("content type %s not allowed", contentType)
}

// scanContent scans request content for malicious patterns
func (sm *SecurityMiddleware) scanContent(r *http.Request) error {
	patterns := []string{"<script", "javascript:", "vbscript:", "onload=", "onerror=", "eval(", "document.cookie", "document.write", "window.location", "../", "..\\", "SELECT * FROM", "DROP TABLE", "INSERT INTO", "UPDATE SET", "DELETE FROM", "UNION SELECT", "' OR '1'='1", "' OR 1=1", "admin'--", "admin'/*"}

	path := strings.ToLower(r.URL.Path)
	for _, p := range patterns {
		if strings.Contains(path, p) {
			return fmt.Errorf("malicious pattern detected")
		}
	}
	for _, values := range r.URL.Query() {
		for _, v := range values {
			lv := strings.ToLower(v)
			for _, p := range patterns {
				if strings.Contains(lv, p) {
					return fmt.Errorf("malicious pattern detected")
				}
			}
		}
	}
	for _, values := range r.Header {
		for _, v := range values {
			lv := strings.ToLower(v)
			for _, p := range patterns {
				if strings.Contains(lv, p) {
					return fmt.Errorf("malicious pattern detected")
				}
			}
		}
	}
	return nil
}

// setSecurityHeaders sets security headers for production
func (sm *SecurityMiddleware) setSecurityHeaders(w http.ResponseWriter) {
	w.Header().Set("Content-Security-Policy", sm.config.CSPPolicy)
	w.Header().Set("Strict-Transport-Security", fmt.Sprintf("max-age=%d; includeSubDomains", sm.config.HSTSMaxAge))
	w.Header().Set("X-Content-Type-Options", "nosniff")
	w.Header().Set("X-Frame-Options", "DENY")
	w.Header().Set("X-XSS-Protection", "1; mode=block")
	w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
	w.Header().Set("Permissions-Policy", "geolocation=(), microphone=(), camera=()")
	w.Header().Set("Server", "")
	w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
	w.Header().Set("Pragma", "no-cache")
	w.Header().Set("Expires", "0")

	// Advertise HTTP/3 support via Alt-Svc header
	// This tells clients that HTTP/3 is available on the same host and port
	// Use the external port (8443) that clients connect to, not the internal port (4000)
	altSvcValue := "h3=\":8443\"; ma=86400"
	w.Header().Set("Alt-Svc", altSvcValue)

	// Debug: Log that we're setting the Alt-Svc header
	fmt.Printf("DEBUG: Security middleware setting Alt-Svc header: %s\n", altSvcValue)
}
